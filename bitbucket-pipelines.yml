# Template PHP Build

# This template allows you to validate your PHP application.
# The workflow allows running tests and code linting on the default branch.

#image: composer:2.0

#pipelines:
#  default:
#    - parallel:
#       - step:
#            name: Test
#            script:
#             - composer install
#              -  ./vendor/bin/phpunit test
#           caches:
#              - composer
#        - step:
#            name: Lint
#            script:
#              - composer install
#              - ./vendor/bin/phplint . --exclude=vendor
#           caches:
#             - composer
# image: composer:2.0
# pipelines:
#   default:
#     - step:
#         name: Build and Deploy
#         caches:
#           - composer
#         script:
#           - echo "Starting Build Process"
#           # - npm install

#           # - npm run build
#           - echo "Deploying to remote server"
#           - apt-get update && apt-get install -y sshpass
#           - sshpass -p 'Br!ll!@nt-B@cKb0n!@Lt#' scp -o StrictHostKeyChecking=no -r * root@*************:ls
#           - ls
# pipelines:
# image: composer:2.0
image: php:8.2-cli
pipelines:
  branches:
    main:
      - step:
          name: Install Dependencies
          caches:
            - composer
          script:
            - echo "Installing PHP Extensions and Dependencies"
            # - apt-get update && apt-get install -y zip unzip git
            # - curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer
            # - echo "Installing Composer Dependencies"
            # - COMPOSER_ALLOW_SUPERUSER=1 composer install --prefer-dist --no-ansi --no-interaction --no-progress --no-scripts
      - step:
          name: Deploy to Server
          script:
            - echo "Deploying to server"
            - apt-get update && apt-get install -y sshpass
            # - sshpass -p 'Br!ll!@nt-B@cKb0n!@Lt#' ssh -o StrictHostKeyChecking=no root@************* "ll"
            - sshpass -p 'Br!ll!@nt-B@cKb0n!@Lt#' ssh -o StrictHostKeyChecking=no root@************* "mkdir -p /var/www/saas1"
            # - sshpass -p 'Br!ll!@nt-B@cKb0n!@Lt#' rsync -avz --delete --exclude='.env' --exclude='storage/' --exclude='vendor/' ./ root@*************:/var/www/saas1
            - sshpass -p 'Br!ll!@nt-B@cKb0n!@Lt#' ssh -o StrictHostKeyChecking=no root@************* " cd /var/www/saas1/saas-lms-international && ls -la && chmod  +x script.sh && sh script.sh "
