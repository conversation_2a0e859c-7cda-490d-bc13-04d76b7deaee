    public function deleteQuestion(Request $request)
    {
        try {
            $question = ChapterQuizQuestion::where('id', $request->id)->first();
            if ($question->question_image != null) {
                $this->deleteImage($question->question_image);
            }
            if ($question->option1_image != null) {
                $this->deleteImage($question->option1_image);
            }
            if ($question->option2_image != null) {
                $this->deleteImage($question->option2_image);
            }
            if ($question->option3_image != null) {
                $this->deleteImage($question->option3_image);
            }
            if ($question->option4_image != null) {
                $this->deleteImage($question->option4_image);
            }
            if ($question->explanation_image != null) {
                $this->deleteImage($question->explanation_image);
            }
            
            // Delete the associated ChapterQuizItem
            ChapterQuizItem::where('chapter_quiz_question_id', $question->id)->delete();
            
            $question->delete();

            return $this->apiResponse([], 'Question Deleted Successfully', true, 200);
        } catch (\Throwable $th) {
            return $this->apiResponse([], $th->getMessage(), false, 500);
        }
    }
    
    /**
     * Delete a fill in the blank question
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteFillInBlankQuestion(Request $request)
    {
        try {
            $question = FillInTheBlankQuestion::where('id', $request->id)->first();
            if (!$question) {
                return $this->apiResponse([], 'Question not found', false, 404);
            }
            
            // Delete images if they exist
            if ($question->question_image != null) {
                $this->deleteImage($question->question_image);
            }
            if ($question->explanation_image != null) {
                $this->deleteImage($question->explanation_image);
            }
            
            // Delete associated blank answers
            BlankAnswer::where('fill_in_the_blank_question_id', $question->id)->delete();
            
            // Delete the associated ChapterQuizItem
            ChapterQuizItem::where('chapter_quiz_fill_in_blank_id', $question->id)->delete();
            
            $question->delete();

            return $this->apiResponse([], 'Fill in the Blank Question Deleted Successfully', true, 200);
        } catch (\Throwable $th) {
            return $this->apiResponse([], $th->getMessage(), false, 500);
        }
    }
    
    /**
     * Delete a true/false question
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteTrueFalseQuestion(Request $request)
    {
        try {
            $question = ChapterTrueFalseQuestion::where('id', $request->id)->first();
            if (!$question) {
                return $this->apiResponse([], 'Question not found', false, 404);
            }
            
            // Delete images if they exist
            if ($question->question_image != null) {
                $this->deleteImage($question->question_image);
            }
            if ($question->explanation_image != null) {
                $this->deleteImage($question->explanation_image);
            }
            
            // Delete the associated ChapterQuizItem
            ChapterQuizItem::where('chapter_quiz_true_false_id', $question->id)->delete();
            
            $question->delete();

            return $this->apiResponse([], 'True/False Question Deleted Successfully', true, 200);
        } catch (\Throwable $th) {
            return $this->apiResponse([], $th->getMessage(), false, 500);
        }
    }
    
    /**
     * Delete a matching question
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteMatchingQuestion(Request $request)
    {
        try {
            $question = MatchingQuestion::where('id', $request->id)->first();
            if (!$question) {
                return $this->apiResponse([], 'Question not found', false, 404);
            }
            
            // Delete images if they exist
            if ($question->explanation_image != null) {
                $this->deleteImage($question->explanation_image);
            }
            
            // Delete associated matching answers
            MatchingAnswer::where('matching_question_id', $question->id)->delete();
            
            // Delete the associated ChapterQuizItem
            ChapterQuizItem::where('chapter_quiz_matching_id', $question->id)->delete();
            
            $question->delete();

            return $this->apiResponse([], 'Matching Question Deleted Successfully', true, 200);
        } catch (\Throwable $th) {
            return $this->apiResponse([], $th->getMessage(), false, 500);
        }
    }
