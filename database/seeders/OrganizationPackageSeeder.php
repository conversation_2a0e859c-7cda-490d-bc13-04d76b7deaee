<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\OrganizationPackage;


class OrganizationPackageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        OrganizationPackage::insert([
            [
                'plan_name' => 'Starter',
                'description' => 'A basic package for small organizations.',
                'price' => 1490,
                'discount_percentage' => 10,
                'sale_price' => 1490,
                'billing_cycle' => 'monthly',
                'features' => json_encode(["5 GB Storage", "200 Users", "Basic Support"]),
                'max_users' => 5,
                'max_courses' => 10,
                'support_level' => 'basic',
                'trial_period_days' => 14,
                'is_active' => true,
                'is_popular' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'plan_name' => 'Professional',
                'description' => 'An intermediate package for growing organizations.',
                'price' => 3490,
                'discount_percentage' => 10,
                'sale_price' => 3490,
                'billing_cycle' => 'monthly',
                'features' => json_encode(["15 GB Storage", "500 Users", "Basic Support"]),
                'max_users' => 25,
                'max_courses' => 50,
                'support_level' => 'priority',
                'trial_period_days' => 14,
                'is_active' => true,
                'is_popular' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'plan_name' => 'Enterprise',
                'description' => 'A premium package for large organizations with custom needs.',
                'price' => 4990,
                'discount_percentage' => 10,
                'sale_price' => 4990,
                'billing_cycle' => 'yearly',
                'features' => json_encode(["30 GB Storage", "1000 Users", "Premium Support"]),
                'max_users' => null,
                'max_courses' => null,
                'support_level' => 'dedicated',
                'trial_period_days' => 30,
                'is_active' => true,
                'is_popular' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);
    }
}
