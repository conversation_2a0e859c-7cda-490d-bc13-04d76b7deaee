<?php

namespace Database\Seeders;

use App\Models\Organization;
use App\Models\QuizCoreSubjects;
use App\Models\QuizQuestionSet;
use App\Models\QuizType;
use App\Models\User;
use Illuminate\Database\Seeder;

class UsersTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {

        $user = [
            [
                'name' => 'System Admin',
                'email' => '<EMAIL>',
                'username' => 'systemadmin',
                'password' => bcrypt('systemadmin'),
                'user_type' => 'SystemAdmin',
                'organization_id' => null,
                'is_active' => 1,
            ],

            [
                'name' => 'Super Admin',
                'email' => '<EMAIL>',
                'username' => 'superadmin',
                'password' => bcrypt('superadmin'),
                'user_type' => 'SuperAdmin',
                'organization_id' => null,
                'is_active' => 1,
            ],

            [
                'name' => 'Organization Admin',
                'email' => '<EMAIL>',
                'username' => 'admin',
                'password' => bcrypt('admin'),
                'user_type' => 'OrganizationAdmin',
                'organization_id' => 1,
                'is_active' => 1,
            ],

            [
                'name' => 'Organization Admin 2',
                'email' => '<EMAIL>',
                'username' => 'admin2',
                'password' => bcrypt('admin2'),
                'user_type' => 'OrganizationAdmin',
                'organization_id' => 2,
                'is_active' => 1,
            ],
        ];

        $org = [
            [
                'name' => 'SAAS LMS',
                'address' => 'House # 13 (5th Floor), Block-C, Main Road, Banasree, Rampura, Dhaka-1219',
                'email' => '<EMAIL>',
                'contact_no' => '+88 02 8396601',
                'logo' => 'logo/bb_logo.png',
                'menu_position' => 'top',
                'contact_person' => 'Md. Mynul Islam ',
                'is_active' => true,
                'banner' => 'banner/bb_banner.jpg',
                'contact_number' => null,
                'hotline_number' => '09611900205',
                'host_url' => 'http://localhost:4001',
                'asset_host' => null,
                'color_theme' => '#ffffff',
                'details' => "BacBon Learning's web-based LMS offers online courses, exams, and progress tracking, accessible on any device. It manages content like videos and documents, and awards certificates, providing a complete blended learning solution.",
            ],

            // ,[
            //     'name' => 'SAAS Learning',
            //     'address' => 'House # 13 (5th Floor), Block-C, Main Road, Banasree, Rampura, Dhaka-1219',
            //     'email' => '<EMAIL>',
            //     'contact_no' => '+88 02 8396601',
            //     'logo' => 'logo/bb_logo.png',
            //     'menu_position' => 'top',
            //     'contact_person' => 'Md. Mynul Islam ',
            //     'is_active' => true,
            //     'banner' => 'banner/bb_banner.jpg',
            //     'contact_number' => null,
            //     'hotline_number' => '09611900205',
            //     'host_url' => 'http://localhost:4001',
            //     'asset_host' => null,
            //     'color_theme' => '#ffffff',
            //     'details' => "BacBon Learning's web-based LMS offers online courses, exams, and progress tracking, accessible on any device. It manages content like videos and documents, and awards certificates, providing a complete blended learning solution."

            // ]
        ];

        $quizCoreSubject = [
            [
                'created_by' => 1,
                'organization_id' => 1,
                'name' => 'English',
                'name_bn' => 'ইংরেজি',
                'is_optional' => 0,
                'optional_subject_id' => null,
                'is_active' => 1,
            ],

            [
                'created_by' => 1,
                'organization_id' => 1,
                'name' => 'Bangla',
                'name_bn' => 'বাংলা',
                'is_optional' => 0,
                'optional_subject_id' => null,
                'is_active' => 1,
            ],

            [
                'created_by' => 1,
                'organization_id' => 1,
                'name' => 'Maths',
                'name_bn' => 'গণিত',
                'is_optional' => 0,
                'optional_subject_id' => null,
                'is_active' => 1,
            ],

            [
                'created_by' => 1,
                'organization_id' => 1,
                'name' => 'Science',
                'name_bn' => 'বিজ্ঞান',
                'is_optional' => 0,
                'optional_subject_id' => null,
                'is_active' => 1,
            ],

            [
                'created_by' => 1,
                'organization_id' => 1,
                'name' => 'Social Science',
                'name_bn' => 'সাধারণ বিজ্ঞান',
                'is_optional' => 0,
                'optional_subject_id' => null,
                'is_active' => 1,
            ],

            [
                'created_by' => 1,
                'organization_id' => 1,
                'name' => 'Computer Science',
                'name_bn' => 'কম্পিউটার বিজ্ঞান',
                'is_optional' => 0,
                'optional_subject_id' => null,
                'is_active' => 1,
            ],

        ];

        $sets = [
            [
                'created_by' => 1,
                'organization_id' => 1,
                'name' => 'Set A',
            ],

            [
                'created_by' => 1,
                'organization_id' => 1,
                'name' => 'Set B',

            ],

            [
                'created_by' => 1,
                'organization_id' => 1,
                'name' => 'Set C',

            ],

            [
                'created_by' => 1,
                'organization_id' => 1,
                'name' => 'Set D',

            ],

        ];

        $quizType =
        [
            [
                'created_by' => 1,
                'organization_id' => 1,
                'name' => 'General Quiz',
                'name_bn' => 'সাধারণ পরীক্ষা',
                'participation_limit' => 1,
                'in_course' => 0,
                'is_active' => 1,
            ],

            [
                'created_by' => 1,
                'organization_id' => 1,
                'name' => 'Revision Test',
                'name_bn' => 'রিভিউন্সিত পরীক্ষা',
                'participation_limit' => 1,
                'in_course' => 0,
                'is_active' => 1,
            ],

            [
                'created_by' => 1,
                'organization_id' => 1,
                'name' => 'Weekly Test',
                'name_bn' => 'সাপ্তাহিক পরীক্ষা',
                'participation_limit' => 1,
                'in_course' => 1,
                'is_active' => 1,
            ],
            [
                'created_by' => 1,
                'organization_id' => 1,
                'name' => 'Model Test',
                'name_bn' => 'মডেল কুইজ',
                'participation_limit' => 1,
                'in_course' => 1,
                'is_active' => 1,
            ],

        ];

        $quizCoreSubject = [
            [
                'created_by' => 1,
                'organization_id' => 1,
                'name' => 'English',
                'name_bn' => 'ইংরেজি',
                'is_optional' => 0,
                'optional_subject_id' => null,
                'is_active' => 1,
            ],

            [
                'created_by' => 1,
                'organization_id' => 1,
                'name' => 'Bangla',
                'name_bn' => 'বাংলা',
                'is_optional' => 0,
                'optional_subject_id' => null,
                'is_active' => 1,
            ],

            [
                'created_by' => 1,
                'organization_id' => 1,
                'name' => 'Maths',
                'name_bn' => 'গণিত',
                'is_optional' => 0,
                'optional_subject_id' => null,
                'is_active' => 1,
            ],

            [
                'created_by' => 1,
                'organization_id' => 1,
                'name' => 'Science',
                'name_bn' => 'বিজ্ঞান',
                'is_optional' => 0,
                'optional_subject_id' => null,
                'is_active' => 1,
            ],

            [
                'created_by' => 1,
                'organization_id' => 1,
                'name' => 'Social Science',
                'name_bn' => 'সাধারণ বিজ্ঞান',
                'is_optional' => 0,
                'optional_subject_id' => null,
                'is_active' => 1,
            ],

            [
                'created_by' => 1,
                'organization_id' => 1,
                'name' => 'Computer Science',
                'name_bn' => 'কম্পিউটার বিজ্ঞান',
                'is_optional' => 0,
                'optional_subject_id' => null,
                'is_active' => 1,
            ]


        ];

        $sets = [
            [
                'created_by' => 1,
                'organization_id' => 1,
                'name' => 'Set A',
            ],

            [
                'created_by' => 1,
                'organization_id' => 1,
                'name' => 'Set B',

            ],

            [
                'created_by' => 1,
                'organization_id' => 1,
                'name' => 'Set C',

            ],

            [
                'created_by' => 1,
                'organization_id' => 1,
                'name' => 'Set D',

            ],

        ];

        $quizType=
        [
            [
                'created_by' => 1,
                'organization_id' => 1,
                'name' => 'General Quiz',
                'name_bn' => 'সাধারণ পরীক্ষা',
                'participation_limit' => 1,
                'in_course' => 0,
                'is_active' => 1,
            ],

            [
                'created_by' => 1,
                'organization_id' => 1,
                'name' => 'Revision Test',
                'name_bn' => 'রিভিউন্সিত পরীক্ষা',
                'participation_limit' => 1,
                'in_course' => 0,
                'is_active' => 1,
            ],

            [
                'created_by' => 1,
                'organization_id' => 1,
                'name' => 'Weekly Test',
                'name_bn' => 'সাপ্তাহিক পরীক্ষা',
                'participation_limit' => 1,
                'in_course' => 1,
                'is_active' => 1,
            ],
            [
                'created_by' => 1,
                'organization_id' => 1,
                'name' => 'Model Test',
                'name_bn' => 'মডেল কুইজ',
                'participation_limit' => 1,
                'in_course' => 1,
                'is_active' => 1,
            ],

        ];

        Organization::insert($org);
        User::insert($user);
        QuizCoreSubjects::insert($quizCoreSubject);
        QuizQuestionSet::insert($sets);
        QuizType::insert($quizType);
    }
}
