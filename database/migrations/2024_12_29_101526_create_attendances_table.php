<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('attendances', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('course_id')->nullable();
            $table->bigInteger('batch_id')->nullable();
            $table->bigInteger('student_id');
            $table->bigInteger('class_schedule_id')->nullable();
            $table->boolean('is_present')->default(0);
            $table->string('reason')->nullable();
            $table->date('attendance_date')->nullable();
            $table->bigInteger('attendance_by')->nullable();
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('attendances');
    }
};
