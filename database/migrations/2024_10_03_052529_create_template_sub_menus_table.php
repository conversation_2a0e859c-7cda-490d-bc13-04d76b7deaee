<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('template_sub_menus', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('template_menu_id');
            $table->string('name');
            $table->string('name_bn')->nullable();
            $table->string('description')->nullable();
            $table->string('link')->nullable();
            $table->string('icon')->nullable();
            $table->boolean('is_active')->default(1);
            $table->integer('sequence')->default(0);
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('template_sub_menus');
    }
};
