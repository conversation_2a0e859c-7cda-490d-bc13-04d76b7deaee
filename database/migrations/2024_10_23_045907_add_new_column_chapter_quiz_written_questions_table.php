<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('chapter_quiz_written_questions', function (Blueprint $table) {
            $table->longText('description')->nullable()->after('chapter_quiz_id');

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('chapter_quiz_written_questions', function (Blueprint $table) {
            //
        });
    }
};
