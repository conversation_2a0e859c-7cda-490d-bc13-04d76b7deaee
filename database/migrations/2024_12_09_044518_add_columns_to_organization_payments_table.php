<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('organization_payments', function (Blueprint $table) {
            $table->string('payment_method')->nullable()->after('is_trial_taken');
            $table->string('bkash_number')->nullable()->after('payment_method');
            $table->string('bkash_transaction_id')->nullable()->after('bkash_number');
            $table->string('card_brand')->nullable()->after('bkash_transaction_id');
            $table->integer('card_last_four')->nullable()->after('card_brand');
            $table->string('currency', 3)->default('BDT')->after('card_last_four');
            $table->string('tran_id')->nullable()->after('currency');
            $table->string('success_url')->nullable()->after('tran_id');
            $table->string('fail_url')->nullable()->after('success_url');
            $table->string('customer_name')->nullable()->after('fail_url');
            $table->string('customer_email')->nullable()->after('customer_name');
            $table->string('customer_phone')->nullable()->after('customer_email');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('organization_payments', function (Blueprint $table) {
            //
        });
    }
};
