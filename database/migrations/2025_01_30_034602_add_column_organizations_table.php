<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('organizations', function (Blueprint $table) {
            $table->bigInteger('custom_course_number')->default(0);
            $table->bigInteger('custom_student_number')->default(0);
            $table->bigInteger('custom_user_number')->default(0);
            $table->bigInteger('custom_book_number')->default(0);
            $table->bigInteger('custom_instructor_number')->default(0);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('organizations', function (Blueprint $table) {
            //
        });
    }
};
