<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('organization_packages', function (Blueprint $table) {
            $table->id();
            $table->string('plan_name', 255);
            $table->text('description')->nullable();
            $table->string('plan_image')->nullable();
            $table->decimal('price', 10, 2);
            $table->integer('discount_percentage')->nullable();
            $table->decimal('sale_price', 10, 2)->nullable();
            $table->enum('billing_cycle', ['monthly', 'yearly']);
            $table->json('features')->nullable();
            $table->integer('max_users')->nullable();
            $table->integer('max_courses')->nullable();
            $table->enum('support_level', ['basic', 'priority', 'dedicated'])->default('basic');
            $table->integer('trial_period_days')->default(14);
            $table->boolean('is_active')->default(1);
            $table->boolean('is_popular')->default(0);
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('organization_packages');
    }
};
