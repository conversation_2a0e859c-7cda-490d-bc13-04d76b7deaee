<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('chapter_quiz_results', function (Blueprint $table) {
            $table->timestamp('end_time')->nullable()->after('negetive_count');
            $table->timestamp('submitted_at')->nullable()->after('end_time');

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('chapter_quiz_results', function (Blueprint $table) {
            //
        });
    }
};
