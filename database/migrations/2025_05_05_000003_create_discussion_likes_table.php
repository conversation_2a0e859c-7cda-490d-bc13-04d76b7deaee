<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('discussion_likes', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('organization_id')->nullable();
            $table->bigInteger('user_id');
            $table->bigInteger('discussion_id')->nullable();
            $table->bigInteger('comment_id')->nullable();
            $table->bigInteger('created_by')->nullable();
            $table->timestamps();

            // Ensure a user can only like a discussion or comment once
            $table->unique(['user_id', 'discussion_id', 'comment_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('discussion_likes');
    }
};
