<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('offline_exam_students', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('course_id');
            $table->bigInteger('offline_exam_id');
            $table->bigInteger('student_id');
            $table->bigInteger('batch_id')->nullable();
            $table->double('mcq_mark')->nullable();
            $table->double('written_mark')->nullable();
            $table->double('assignment_mark')->nullable();
            $table->double('presentation_mark')->nullable();
            $table->double('practical_mark')->nullable();
            $table->double('total_mark')->nullable();
            $table->boolean('is_passed')->default(false);
            $table->bigInteger('marked_by')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('offline_exam_students');
    }
};
