<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('promotional_items', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('organization_id');
            $table->string('title');
            $table->string('title_bn')->nullable();
            $table->text('description')->nullable();
            $table->string('image')->nullable();
            $table->enum('type', ['course', 'video', 'script', 'quiz', 'ebook', 'page'])->default('video');
            $table->bigInteger('course_id')->nullable();
            $table->bigInteger('chapter_video_id')->nullable();
            $table->bigInteger('chapter_script_id')->nullable();
            $table->bigInteger('chapter_quiz_id')->nullable();
            $table->bigInteger('ebook_id')->nullable();
            $table->string('youtube_url')->nullable();
            $table->boolean('is_active')->default(1);
            $table->bigInteger('created_by')->nullable();
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('promotional_items');
    }
};
