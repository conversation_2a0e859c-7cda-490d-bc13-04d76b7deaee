<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('templates', function (Blueprint $table) {
            $table->id(); // id (Primary key)
            $table->string('title'); // title (Non-nullable)
            $table->text('short_description')->nullable(); // short_description (Nullable)
            $table->string('folder_name'); // folder_name (Non-nullable)
            $table->string('theme_color')->nullable(); // theme_color (Nullable)
            $table->string('title_color')->nullable(); // title_color (Nullable)
            $table->string('text_color')->nullable(); // text_color (Nullable)
            $table->string('theme_image')->nullable(); // theme_image (Nullable)
            $table->boolean('is_active')->default(1); // is_active (Non-nullable)
            $table->timestamps(); // created_at and updated_at
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('templates');
    }
};
