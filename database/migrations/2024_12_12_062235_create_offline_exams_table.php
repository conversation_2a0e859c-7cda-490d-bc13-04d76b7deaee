<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('offline_exams', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('organization_id')->nullable();
            $table->bigInteger('course_id')->nullable();
            $table->string('title', 100);
            $table->string('title_bn', 100)->nullable();
            $table->text('description')->nullable();
            $table->integer('mcq_mark')->default(0);
            $table->integer('written_mark')->default(0);
            $table->integer('assignment_mark')->default(0);
            $table->integer('presentation_mark')->default(0);
            $table->integer('practical_mark')->default(0);
            $table->integer('pass_mark')->default(0);
            $table->integer('total_mark')->default(0);
            $table->integer('duration')->default(0);
            $table->dateTime('exam_date')->nullable();
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('offline_exams');
    }
};
