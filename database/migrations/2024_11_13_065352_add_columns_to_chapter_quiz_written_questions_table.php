<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('chapter_quiz_written_questions', function (Blueprint $table) {
            $table->longText('instruction')->nullable()->after('no_of_question');
            $table->integer('duration')->default(0)->after('instruction');

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('chapter_quiz_written_questions', function (Blueprint $table) {
            //
        });
    }
};
