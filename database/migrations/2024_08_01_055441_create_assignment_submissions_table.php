<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('assignment_submissions', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('organization_id')->nullable();
            $table->bigInteger('course_id');
            $table->bigInteger('assignment_id');
            $table->bigInteger('student_id')->comment('User ID: student');
            $table->bigInteger('evaluated_by')->nullable()->comment('User ID: mentor');
            $table->string('ip_address')->nullable();
            $table->longText('answer')->nullable()->comment('Trainee Answer');
            $table->integer('marks')->nullable();
            $table->string('remarks')->nullable();
            $table->enum('status', ['Pending', 'Submitted', 'Evaluated', 'Retake'])->default('Pending');
            $table->softDeletes();
            $table->bigInteger('created_by')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('assignment_submissions');
    }
};
