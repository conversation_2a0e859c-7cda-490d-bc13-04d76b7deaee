<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('assignments', function (Blueprint $table) {
            $table->unsignedBigInteger('batch_id')->nullable()->after('id');
        });

        Schema::table('class_schedules', function (Blueprint $table) {
            $table->unsignedBigInteger('batch_id')->nullable()->after('id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('assignments', function (Blueprint $table) {
            $table->dropColumn('batch_id');
        });

        Schema::table('class_schedules', function (Blueprint $table) {
            $table->dropColumn('batch_id');
        });
    }
};
