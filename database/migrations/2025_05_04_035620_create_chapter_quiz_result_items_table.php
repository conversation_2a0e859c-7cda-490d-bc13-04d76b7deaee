<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('chapter_quiz_result_items', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('organization_id')->nullable();
            $table->bigInteger('chapter_quiz_result_id');
            $table->bigInteger('chapter_quiz_item_id');
            $table->bigInteger('chapter_quiz_question_id')->nullable();
            $table->bigInteger('chapter_quiz_true_false_id')->nullable();
            $table->bigInteger('chapter_quiz_matching_id')->nullable();
            $table->bigInteger('chapter_quiz_fill_in_blank_id')->nullable();
            $table->enum('type', ['mcq', 'true_false', 'matching', 'fill_in_blank'])->default('mcq');
            $table->string('answer')->nullable();
            $table->boolean('is_correct')->default(false);
            $table->float('mark_obtained')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('chapter_quiz_result_items');
    }
};
