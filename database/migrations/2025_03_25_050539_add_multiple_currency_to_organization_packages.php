<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('organization_packages', function (Blueprint $table) {
            $table->decimal('yen_price', 10, 2)->default(0);
            $table->integer('yen_discount_percentage')->default(0);
            $table->decimal('yen_sale_price', 10, 2)->default(0);
            $table->decimal('krw_price', 10, 2)->default(0);
            $table->integer('krw_discount_percentage')->default(0);
            $table->decimal('krw_sale_price', 10, 2)->default(0);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('organization_packages', function (Blueprint $table) {
            //
        });
    }
};
