<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAssignmentsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('assignments', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('organization_id')->nullable();
            $table->bigInteger('mentor_id')->nullable();
            $table->bigInteger('course_id')->nullable();
            $table->string('title');
            $table->string('title_bn')->nullable();
            $table->integer('mark')->nullable();
            $table->integer('pass_mark')->nullable();
            $table->integer('total_time')->nullable();
            $table->text('instructions')->nullable();
            $table->text('description')->nullable();
            $table->string('supporting_doc')->nullable();
            $table->dateTime('publish_date')->nullable();
            $table->dateTime('deadline')->nullable();
            $table->enum('status', ['Ongoing', 'Unpublished', 'OnHold', 'Finished'])->default('Unpublished');
            $table->bigInteger('created_by')->nullable();
            $table->boolean('is_active')->default(1);
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('assignments');
    }
}
