<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('temp_videos', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('raw_url');
            $table->string('ip');
            $table->string('device');
            $table->string('browser');
            $table->string('hash');
            $table->float('size')->default(0);
            $table->boolean('is_used')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('temp_videos');
    }
};
