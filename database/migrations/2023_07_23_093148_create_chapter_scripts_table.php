<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateChapterScriptsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('chapter_scripts', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('organization_id')->nullable();
            $table->string('title');
            $table->string('title_bn')->nullable();
            $table->text('description')->nullable();
            $table->string('script_code');
            $table->bigInteger('course_id');
            $table->bigInteger('course_category_id')->nullable();
            $table->bigInteger('class_level_id')->nullable();
            $table->bigInteger('subject_id')->nullable();
            $table->bigInteger('chapter_id')->nullable();
            $table->text('raw_url')->nullable();
            $table->string('thumbnail')->nullable();
            $table->float('price')->default(0.00);
            $table->float('rating')->default(0.00);
            $table->boolean('is_free')->default(1);
            $table->integer('sequence')->default(0);
            $table->boolean('is_active')->default(1);
            $table->softDeletes();
            $table->bigInteger('created_by')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('chapter_scripts');
    }
}
