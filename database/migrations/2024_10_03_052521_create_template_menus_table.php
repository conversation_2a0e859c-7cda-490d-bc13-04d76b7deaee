<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('template_menus', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('template_id');
            $table->string('name');
            $table->string('link')->nullable();
            $table->boolean('is_authentication_needed')->default(0);
            $table->boolean('has_submenu')->default(1);
            $table->boolean('is_course')->default(1);
            $table->boolean('is_content')->default(0);
            $table->string('icon')->nullable();
            $table->string('headline')->nullable();
            $table->longText('description')->nullable();
            $table->integer('sequence')->default(0);
            $table->boolean('is_active')->default(1);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('template_menus');
    }
};
