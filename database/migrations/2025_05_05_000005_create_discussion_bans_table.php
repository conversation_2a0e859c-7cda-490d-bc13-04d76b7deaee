<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('discussion_bans', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('organization_id')->nullable();
            $table->bigInteger('user_id');
            $table->bigInteger('course_id')->nullable(); // If null, banned from all discussions
            $table->bigInteger('banned_by');
            $table->text('reason')->nullable();
            $table->bigInteger('created_by')->nullable();
            $table->timestamp('expires_at')->nullable(); // If null, permanent ban
            $table->timestamps();

            // Ensure a user can only be banned once per course
            $table->unique(['user_id', 'course_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('discussion_bans');
    }
};
