<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('template_items', function (Blueprint $table) {
            $table->id(); // id (Primary key)
            $table->unsignedBigInteger('template_id'); // template_id (Foreign key)
            $table->string('title'); // title
            $table->text('short_description')->nullable(); // short_description (Nullable)
            $table->string('image'); // image
            $table->timestamps(); // created_at and updated_at
            $table->boolean('is_active')->default(1); // is_active (Non-nullable)
            // Foreign key constraint
            $table->foreign('template_id')->references('id')->on('templates')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('template_items');
    }
};
