<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSubjectsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('subjects', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('organization_id')->nullable();
            $table->string('name');
            $table->string('name_bn')->nullable();
            $table->bigInteger('class_level_id');
            $table->string('subject_code');
            $table->float('price')->default(0.00);
            $table->boolean('is_free')->default(1);
            $table->string('icon')->nullable();
            $table->string('color_code')->nullable();
            $table->integer('sequence')->default(0);
            $table->boolean('is_active')->default(1);
            $table->softDeletes();
            $table->bigInteger('created_by')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('subjects');
    }
}
