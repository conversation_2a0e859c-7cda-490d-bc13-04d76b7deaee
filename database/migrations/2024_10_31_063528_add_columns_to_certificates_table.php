<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('certificates', function (Blueprint $table) {
            $table->longText('certification_text')->nullable()->after('name');
            $table->string('background_image')->nullable()->after('certification_text');
            $table->string('logo')->nullable()->after('background_image');
            $table->string('signature')->nullable()->after('logo');
            $table->string('authorize_person')->nullable()->after('signature');
            $table->string('designation')->nullable()->after('authorize_person');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('certificates', function (Blueprint $table) {
            //
        });
    }
};
