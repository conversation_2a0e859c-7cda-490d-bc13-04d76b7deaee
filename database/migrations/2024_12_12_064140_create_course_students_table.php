<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('course_students', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('course_id');
            $table->bigInteger('student_id');
            $table->bigInteger('batch_id')->nullable();
            $table->dateTime('enrolled_date');
            $table->integer('quiz_participation_count')->default(0);
            $table->integer('video_watch_count')->default(0);
            $table->integer('script_watch_count')->default(0);
            $table->integer('assignment_submission_count')->default(0);
            $table->integer('class_attendance_count')->default(0);
            $table->double('total_paid_amount')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('course_students');
    }
};
