<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('payment_details', function (Blueprint $table) {
            $table->string('payment_method')->nullable()->after('total');
            $table->string('trx_id')->nullable()->after('payment_method');
            $table->string('image')->nullable()->after('trx_id');
            $table->dateTime('deadline')->nullable()->after('image');
            $table->string('pay_for')->nullable()->after('deadline');
            $table->float('paid_amount')->nullable()->after('pay_for');
            $table->float('payable_amount')->nullable()->after('paid_amount');
            $table->float('due_amount')->nullable()->after('payable_amount');
            $table->boolean('is_approved')->nullable()->after('due_amount');
            $table->bigInteger('approved_by')->nullable()->after('is_approved');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('payment_details', function (Blueprint $table) {
            //
        });
    }
};
