<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('fill_in_the_blank_questions', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('chapter_quiz_id');
            $table->bigInteger('organization_id')->nullable();
            $table->string('question_text');
            $table->string('question_image')->nullable();
            $table->string('explanation_text')->nullable();
            $table->string('explanation_image')->nullable();
            $table->boolean('is_active')->default(1);
            $table->bigInteger('created_by')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('fill_in_the_blank_questions');
    }
};
