<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCourseOutlinesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('course_outlines', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('organization_id')->nullable();
            $table->string('title');
            $table->string('title_bn')->nullable();
            $table->bigInteger('course_id');
            $table->bigInteger('course_category_id')->nullable();
            $table->bigInteger('class_level_id')->nullable();
            $table->bigInteger('subject_id')->nullable();
            $table->bigInteger('content_subject_id')->nullable();
            $table->bigInteger('chapter_id')->nullable();
            $table->bigInteger('chapter_script_id')->nullable();
            $table->bigInteger('chapter_video_id')->nullable();
            $table->bigInteger('chapter_quiz_id')->nullable();
            $table->boolean('is_free')->default(0);
            $table->integer('sequence')->default(0);
            $table->boolean('is_active')->default(1);

            $table->softDeletes();
            $table->bigInteger('created_by')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('course_outlines');
    }
}
