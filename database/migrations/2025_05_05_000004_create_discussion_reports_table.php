<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('discussion_reports', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('organization_id')->nullable();
            $table->bigInteger('user_id');
            $table->bigInteger('discussion_id')->nullable();
            $table->bigInteger('comment_id')->nullable();
            $table->text('reason');
            $table->string('status')->default('pending'); // 'pending', 'reviewed', 'rejected'
            $table->bigInteger('reviewed_by')->nullable();
            $table->bigInteger('created_by')->nullable();
            $table->timestamp('reviewed_at')->nullable();
            $table->timestamps();

            // Ensure a user can only report a discussion or comment once
            $table->unique(['user_id', 'discussion_id', 'comment_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('discussion_reports');
    }
};
