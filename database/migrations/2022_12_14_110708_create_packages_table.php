<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePackagesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('packages', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('organization_id')->nullable();
            $table->string('title');
            $table->text('description')->nullable();
            $table->integer('limit')->default(1);
            $table->integer('cycle')->default(1);
            $table->text('promotion_title')->nullable();
            $table->text('promotion_details')->nullable();
            $table->string('feature_image')->nullable();
            $table->boolean('is_active')->default(1);
            $table->softDeletes();
            $table->bigInteger('created_by')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('packages');
    }
}
