<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('courses', function (Blueprint $table) {
            $table->double('minimum_enroll_amount')->default(0)->after('discount_percentage');
            $table->integer('max_installment_qty')->default(1)->after('minimum_enroll_amount');
            $table->enum('installment_type', ['One Time', 'Installment', 'Monthly'])->default('One Time')->after('max_installment_qty');
            $table->double('monthly_amount')->default(1)->after('installment_type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('courses', function (Blueprint $table) {
            //
        });
    }
};
