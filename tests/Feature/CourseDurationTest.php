<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use App\Models\Course;
use App\Models\Organization;
use App\Models\User;
use App\Models\CourseType;

class CourseDurationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create an organization manually
        $this->organization = Organization::create([
            'name' => 'Test Organization',
            'email' => '<EMAIL>',
            'contact_no' => '1234567890',
            'is_active' => true,
        ]);

        // Create a user manually
        $this->user = User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'username' => 'testuser',
            'password' => bcrypt('password'),
            'organization_id' => $this->organization->id,
            'user_type' => 'OrganizationAdmin',
            'is_active' => true,
        ]);

        // Create a course type
        $this->courseType = CourseType::create([
            'name' => 'Test Course Type',
            'organization_id' => $this->organization->id,
            'created_by' => $this->user->id,
            'is_active' => true,
        ]);
    }

    /** @test */
    public function it_can_create_course_with_duration_fields()
    {
        $courseData = [
            'title' => 'Test Course',
            'description' => 'Test course description',
            'organization_id' => $this->organization->id,
            'created_by' => $this->user->id,
            'course_type_id' => $this->courseType->id,
            'course_duration' => 30.5,
            'duration_per_day' => 2.0,
            'is_active' => true,
            'is_free' => false,
        ];

        $course = Course::create($courseData);

        $this->assertDatabaseHas('courses', [
            'id' => $course->id,
            'title' => 'Test Course',
            'course_duration' => 30.5,
            'duration_per_day' => 2.0,
        ]);

        $this->assertEquals(30.5, $course->course_duration);
        $this->assertEquals(2.0, $course->duration_per_day);
    }

    /** @test */
    public function it_can_update_course_duration_fields()
    {
        $course = Course::create([
            'title' => 'Test Course for Update',
            'organization_id' => $this->organization->id,
            'created_by' => $this->user->id,
            'course_type_id' => $this->courseType->id,
            'course_duration' => 20.0,
            'duration_per_day' => 1.5,
            'is_active' => true,
        ]);

        $course->update([
            'course_duration' => 45.0,
            'duration_per_day' => 3.0,
        ]);

        $this->assertDatabaseHas('courses', [
            'id' => $course->id,
            'course_duration' => 45.0,
            'duration_per_day' => 3.0,
        ]);
    }

    /** @test */
    public function duration_fields_are_nullable_and_default_to_zero()
    {
        $course = Course::create([
            'title' => 'Test Course Default Values',
            'organization_id' => $this->organization->id,
            'created_by' => $this->user->id,
            'course_type_id' => $this->courseType->id,
            'is_active' => true,
        ]);

        // Check that the fields default to 0 when not specified
        $this->assertEquals(0, $course->course_duration);
        $this->assertEquals(0, $course->duration_per_day);
    }
}
