@component('mail::message')



# 🎉 Welcome to {{ $organization->name ?? 'our platform' }}, {{ $notifiable->name }} 👋

We're excited to welcome you aboard! Your account has been successfully created and you're now part of the {{ $organization->name ?? 'community' }}.

---

### 🔐 Your Login Credentials:

**Email:** {{ $notifiable->email }}
**Password:** {{ $password }}

> For security, we recommend changing your password after logging in.

---

@component('mail::button', ['url' => $organization->host_url . '/login'])
Log in to Dashboard
@endcomponent

If you did not register for this account, please disregard this email or contact our support team.

---

### 🔗 Stay Connected with {{ $organization->name }}

@php
    $hasAnySocial = $organization->facebook || $organization->twitter || $organization->linkedin || $organization->youtube;
@endphp

@if($hasAnySocial)
<p style="margin-top: 30px; font-weight: bold;">Follow us on:</p>
<ul style="padding-left: 0; list-style: none;">
    @if($organization->facebook)
    <li><a href="{{ $organization->facebook }}">Facebook</a></li>
    @endif
    @if($organization->twitter)
    <li><a href="{{ $organization->twitter }}">Twitter</a></li>
    @endif
    @if($organization->linkedin)
    <li><a href="{{ $organization->linkedin }}">LinkedIn</a></li>
    @endif
    @if($organization->youtube)
    <li><a href="{{ $organization->youtube }}">YouTube</a></li>
    @endif
</ul>
@endif

Thanks & regards,
**The {{ $organization->name ?? config('app.name') }} Team**

@endcomponent
