<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Confirmation</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .email-container {
            background-color: #ffffff;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 600;
        }
        .header .icon {
            font-size: 48px;
            margin-bottom: 10px;
        }
        .content {
            padding: 40px 30px;
        }
        .greeting {
            font-size: 18px;
            margin-bottom: 20px;
            color: #2c3e50;
        }
        .success-message {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        .success-message h2 {
            color: #155724;
            margin: 0 0 10px 0;
            font-size: 24px;
        }
        .success-message p {
            color: #155724;
            margin: 0;
            font-size: 16px;
        }
        .course-details {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 25px;
            margin: 25px 0;
            border-left: 4px solid #667eea;
        }
        .course-details h3 {
            color: #2c3e50;
            margin: 0 0 15px 0;
            font-size: 20px;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        .detail-row:last-child {
            border-bottom: none;
        }
        .detail-label {
            font-weight: 600;
            color: #495057;
        }
        .detail-value {
            color: #2c3e50;
            font-weight: 500;
            margin-left: 4px;
        }
        .amount {
            font-weight: 700;
            color: #28a745;
            margin-left: 4px;
        }
        .next-steps {
            background-color: #e3f2fd;
            border-radius: 8px;
            padding: 20px;
            margin: 25px 0;
        }
        .next-steps h3 {
            color: #1565c0;
            margin: 0 0 15px 0;
        }
        .next-steps ul {
            margin: 0;
            padding-left: 20px;
        }
        .next-steps li {
            margin-bottom: 8px;
            color: #1976d2;
        }
        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            margin: 20px 0;
            text-align: center;
        }
        .footer {
            background-color: #f8f9fa;
            padding: 30px;
            text-align: center;
            border-top: 1px solid #e9ecef;
        }
        .footer p {
            margin: 5px 0;
            color: #6c757d;
            font-size: 14px;
        }
        .organization-info {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
        }
        @media (max-width: 600px) {
            body {
                padding: 10px;
            }
            .content {
                padding: 20px 15px;
            }
            .detail-row {
                flex-direction: column;
                align-items: flex-start;
            }
            .detail-value {
                margin-top: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <div class="icon">✅</div>
            <h1>Payment Successful!</h1>
        </div>

        <div class="content">
            <div class="greeting">
                Dear {{ $user->name }},
            </div>

            <div class="success-message">
                <h2>🎉 Congratulations!</h2>
                <p>Your payment has been processed successfully and you are now enrolled in the course.</p>
            </div>

            <div class="course-details">
                <h3>📚 Course Details</h3>
                <div class="detail-row">
                    <span class="detail-label">Course Title:</span>
                    <span class="detail-value">{{ $course->title }}</span>
                </div>
                @if($course->short_description)
                <div class="detail-row">
                    <span class="detail-label">Description:</span>
                    <span class="detail-value">{{ $course->short_description }}</span>
                </div>
                @endif
                <div class="detail-row">
                    <span class="detail-label">Enrollment Date:</span>
                    <span class="detail-value">{{ $payment->created_at->format('F j, Y \a\t g:i A') }}</span>
                </div>
            </div>

            <div class="course-details">
                <h3>💳 Payment Information</h3>
                <div class="detail-row">
                    <span class="detail-label">Transaction ID:</span>
                    <span class="detail-value">{{ $payment->transaction_id }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Amount Paid: </span>
                    <span class="detail-value amount">{{ strtoupper($payment->currency) }} {{ number_format($payment->paid_amount, 2) }}</span>
                </div>
                @if($payment->discount_amount > 0)
                <div class="detail-row">
                    <span class="detail-label">Discount Applied:</span>
                    <span class="detail-value">{{ strtoupper($payment->currency) }} {{ number_format($payment->discount_amount, 2) }}</span>
                </div>
                @endif
                <div class="detail-row">
                    <span class="detail-label">Payment Method:</span>
                    <span class="detail-value">{{ ucfirst($payment->payment_method) }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Payment Status:</span>
                    <span class="detail-value" style="color: #28a745; font-weight: 600;">{{ $payment->status }}</span>
                </div>
            </div>

            <div class="next-steps">
                <h3>🚀 What's Next?</h3>
                <ul>
                    <li>You can now access your course materials immediately</li>
                    <li>Check your course dashboard for lessons and resources</li>
                    <li>Join the course community and connect with other students</li>
                    <li>Download any course materials that are available</li>
                    <li>Start learning at your own pace!</li>
                </ul>
            </div>

            <div style="text-align: center;">
                <a href="{{ $course->slug ? $organization->host_url . '/course/' . $course->slug : $organization->host_url . '/course-details/' . $course->id }}" class="cta-button">
                    Access Your Course Now
                </a>
            </div>

            <p style="margin-top: 30px; color: #6c757d; font-size: 14px;">
                If you have any questions about your enrollment or need assistance accessing your course,
                please don't hesitate to contact our support team.
            </p>

            @if($organization)
            <div class="organization-info">
                <p><strong>{{ $organization->name }}</strong></p>
                @if($organization->email)
                <p>Email: {{ $organization->email }}</p>
                @endif
                @if($organization->contact_no)
                <p>Phone: {{ $organization->contact_no }}</p>
                @endif
                @if($organization->website)
                <p>Website: <a href="{{ $organization->website }}">{{ $organization->website }}</a></p>
                @endif
            </div>
            @endif
        </div>

        <div class="footer">
            <p><strong>Thank you for choosing us for your learning journey!</strong></p>
            <p>This is an automated email. Please do not reply to this message.</p>
            <p>&copy; {{ date('Y') }} {{ $organization->name ?? config('app.name') }}. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
