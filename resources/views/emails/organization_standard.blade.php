<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Organization Notification</title>
    <style>
        body {
            font-family: 'Segoe UI', <PERSON>l, sans-serif;
            background: #f7f7f7;
            margin: 0;
            padding: 0;
        }
        .email-container {
            background: #fff;
            max-width: 480px;
            margin: 40px auto;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.07);
            padding: 32px 24px;
        }
        .org-logo {
            display: block;
            margin: 0 auto 16px auto;
            max-width: 120px;
            max-height: 80px;
        }
        .org-name {
            text-align: center;
            font-size: 1.5em;
            color: #2a4d8f;
            font-weight: bold;
            margin-bottom: 24px;
            letter-spacing: 1px;
        }
        .content {
            color: #333;
            font-size: 1.08em;
            margin-bottom: 32px;
        }
        .footer {
            text-align: center;
            color: #aaa;
            font-size: 0.95em;
            margin-top: 32px;
        }
    </style>
</head>
<body>
    <div class="email-container">
        {{-- Organization Logo --}}
        @if(isset($organization_logo))
            <img src="{{ $organization_logo }}" alt="Organization Logo" class="org-logo">
        @endif

        {{-- Organization Name --}}
        <div class="org-name">
            {{ $organization_name }}
        </div>

        {{-- Main Content --}}
        <div class="content">
            {{-- Place your dynamic content here, e.g. --}}
            {!! $slot ?? '' !!}
        </div>

        <div class="footer">
            If you did not request this, please ignore this email.<br>
            &copy; {{ date('Y') }} {{ $organization_name }}
        </div>
    </div>
</body>
</html>
