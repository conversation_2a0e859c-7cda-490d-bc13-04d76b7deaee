<?php

use Illuminate\Support\Facades\Broadcast;

/*
|--------------------------------------------------------------------------
| Broadcast Channels
|--------------------------------------------------------------------------
|
| Here you may register all of the event broadcasting channels that your
| application supports. The given channel authorization callbacks are
| used to check if an authenticated user can listen to the channel.
|
*/

Broadcast::channel('App.Models.User.{id}', function ($user, $id) {
    return (int) $user->id === (int) $id;
});

Broadcast::channel('edu-channel', function ($user) {
    return true; // Or your authorization logic
});

// Channel for general notifications
Broadcast::channel('notifications', function () {
    return true; // Anyone can listen to general notifications
});

// Channel for user-specific notifications
Broadcast::channel('user.{userId}', function ($user, $userId) {
    return (int) $user->id === (int) $userId;
});

// Channel for organization-specific notifications
Broadcast::channel('organization.{organizationId}', function ($user, $organizationId) {
    return (int) $user->organization_id === (int) $organizationId;
});

// Channel for specific notification channels
Broadcast::channel('{channel}', function () {
    return true; // You can add authorization logic here if needed
});
