<?php

use App\Http\Controllers\Auth\AuthController;
use App\Http\Controllers\Auth\MobileOtpController;
use App\Http\Controllers\ContentController;
use App\Http\Controllers\CourseController;
use App\Http\Controllers\DiscussionController;
use App\Http\Controllers\Mobile\CourseController as MobileCourseController;
use App\Http\Controllers\Mobile\CategoryController  as MobileCategoryController;
use App\Http\Controllers\Mobile\ContentController as MobileContentController;
use App\Http\Controllers\Mobile\OrganizationController as MobileOrganizationController;
use App\Http\Controllers\Mobile\StudentController as MobileStudentController;
use App\Http\Controllers\Mobile\ContentWatchLogController;
use App\Http\Controllers\Mobile\ClassScheduleController;
use App\Http\Controllers\AttendanceController;
use App\Http\Controllers\PaymentTypeOrganizationController;
use App\Http\Controllers\MasterSettingsController;
use App\Http\Controllers\MentorController;
use App\Http\Controllers\CouponController;
use App\Http\Controllers\BatchController;
use App\Http\Controllers\Mobile\MentorController as MobileMentorController;
use App\Http\Controllers\StudentController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\AssignmentController;
use App\Http\Controllers\Mobile\PaymentController as MobilePaymentController;
use Illuminate\Support\Facades\Route;


Route::post('login-with-mobile', [AuthController::class, 'loginWithMobile'])->middleware('throttle:6,60');

Route::post('verify-otp', [MobileOtpController::class, 'verifyOTP']);
Route::post('verify-otp-forgot-password', [MobileOtpController::class, 'verifyOTPForgotPassword']);
Route::post('register-user', [AuthController::class, 'registerUserWithOtp']);
Route::post('verify-password', [AuthController::class, 'verifyPassword']);
Route::post('forgot-password-by-mobile', [AuthController::class, 'forgotPassword']);
Route::post('reset-password', [AuthController::class, 'resetPassword']);



Route::post('check-user-agent', [MobileOrganizationController::class, 'checkUserAgent']);

Route::get('landing-page-information', [MobileOrganizationController::class, 'getDetails']);


Route::get('course-details', [MobileCourseController::class, 'getDetails'])->name('mobile-course-details');
Route::get('rating-list', [MobileCourseController::class, 'getRatingList']);


Route::get('course-list', [MobileCourseController::class, 'getList']);

Route::get('category-wise-course-list', [MobileCategoryController::class, 'getListForMobile']);

Route::get('category-list', [MasterSettingsController::class, 'mobileMenuList']);
Route::get('course-list-by-id/{menu_id}', [MasterSettingsController::class, 'courseListByID']);
Route::get('all-content-list', [ContentController::class, 'allContentList']);
Route::get('course-details-by-id/{course_id}', [CourseController::class, 'courseDetailsByID']);
Route::get('all-mentor-list', [MentorController::class, 'allMentorList']);
Route::get('mentor-details-by-id/{mentor_id}', [MentorController::class, 'mentorDetailsByID']);
// Route::get('course-details-by-user/{course_id}', [CourseController::class, 'courseDetailsByUserID']);


// Content Access and Completion
Route::get('get-content-details', [MobileContentController::class, 'getContentDetails']);
Route::get('get-course-payment-details', [MobileCourseController::class, 'getPaymentDetails']);



Route::middleware('auth:sanctum')->group(function () {


    Route::get('student-dashboard', [MobileCourseController::class, 'studentDashboard']);
    Route::get('learning-activities', [MobileCourseController::class, 'learningActivities']);
    Route::get('learning-graph', [MobileCourseController::class, 'learningGraph']);


    Route::get('get-last-content', [ContentWatchLogController::class, 'getLastAccessedContent']);


    Route::post('logout', [AuthController::class, 'logoutCurrentUser']);
    Route::post('logout-by-device', [AuthController::class, 'logoutByDevice']);
    Route::post('logout-all-device', [AuthController::class, 'logoutAllDevice']);
    Route::post('logout-exept-current-device', [AuthController::class, 'logoutExeptCurrentDevice']);
    Route::get('get-login-devices', [AuthController::class, 'getLoginDevices']);
    Route::post('change-password', [AuthController::class, 'changePassword']);


    Route::get('mark-video-completed', [ContentWatchLogController::class, 'markVideoCompleted']);
    Route::get('mark-script-completed', [ContentWatchLogController::class, 'markScriptCompleted']);



    Route::get('my-course-list', [MobileCourseController::class, 'getMyCourseList']);

    Route::get('student-profile', [MobileStudentController::class, 'studentDetails']);
    Route::post('submit-written-answer', [CourseController::class, 'submitWrittenAnswerMobile']);
    Route::post('student-end-live-class', [CourseController::class, 'studentEndLiveClass']);


    Route::post('purchase-course', [PaymentController::class, 'purchaseCourse']);
    Route::post('enroll-course', [PaymentController::class, 'enrollCourse']);
    Route::get('my-payment-list', [MobilePaymentController::class, 'myPaymentList']);
    Route::get('my-purchase-list', [MobilePaymentController::class, 'myPurchaseList']);



    Route::get('quiz-details/{quiz_id}', [CourseController::class, 'chapterQuizDetails']);
    Route::post('start-quiz', [MobileCourseController::class, 'startQuiz']);
    Route::post('submit-quiz', [CourseController::class, 'submitQuizAnswer']);
    // Route::post('submit-written-answer', [MobileCourseController::class, 'submitWrittenAnswer']);
    Route::get('result-list', [MobileCourseController::class, 'getResultList']);
    Route::get('result-details', [MobileCourseController::class, 'getResultDetails']);
    Route::get('student-quiz-result-details-by-id/{result_id}', [MobileCourseController::class, 'quizAnswerDetails']);

    Route::post('update-profile', [MobileStudentController::class, 'updateProfile']);



    Route::get('live-class-list', [ClassScheduleController::class, 'getLiveClassList']);
    Route::get('class-schedules', [ClassScheduleController::class, 'getLiveClassListForStudent']);
    Route::post('join-class', [ClassScheduleController::class, 'studentJoinClass']);


    Route::get('student-assignment-list', [AssignmentController::class, 'studentAssignmentListMobile']);
    Route::get('assignment-details', [AssignmentController::class, 'assignmentDetails']);
    Route::get('assignments', [AssignmentController::class, 'showAssignment']);
    Route::put('assignments/{id}', [AssignmentController::class, 'updateAssignment']);
    Route::delete('assignments/{id}', [AssignmentController::class, 'deleteAssignment']);
    Route::get('assignments/{id}', [AssignmentController::class, 'showAssignment']);
    // Route::get('assignment-details/{id}', [AssignmentController::class, 'assignmentDetails']);
    //submit assignment
    Route::post('submit-assignment', [AssignmentController::class, 'storeSubmitAssignment']);
    Route::put('submit-assignment/{id}', [AssignmentController::class, 'updateSubmitAssignment']);


    Route::get('certificates', [MobilePaymentController::class, 'getCertificates']);
    Route::post('generate-certificates', [MobilePaymentController::class, 'generateCertificate']);

    Route::post('coupon-check', [CouponController::class, 'couponCheck'])->name('coupon.check_mobile');

    // Mentor Routes
    Route::group(['prefix' => 'mentor'], function () {

        Route::get('dashboard', [MobileMentorController::class, 'dashboard']);
        Route::get('activities', [MobileMentorController::class, 'activities']);
        Route::get('my-profile', [MobileMentorController::class, 'myProfile']);
        Route::post('update-profile', [MobileMentorController::class, 'updateProfile']);

        Route::get('student-list', [MobileCourseController::class, 'studentList']);
        Route::get('my-student-list', [MobileMentorController::class, 'myStudentList']);


        Route::get('course-list-for-filter', [CourseController::class, 'courseListForFilterMentor']);

        Route::get('course-list', [MobileMentorController::class, 'myCourseList']);
        Route::get('course-details', [MobileCourseController::class, 'mentorCourseDetails']);
        Route::get('course-outlines', [MobileCourseController::class, 'mentorCourseOutlines']);
        Route::get('contents-details', [MobileCourseController::class, 'mentorCourseContentsDetails']);
        Route::get('quiz-details', [MobileCourseController::class, 'mentorQuizDetails']);

        Route::post('create-live-class-schedule', [ClassScheduleController::class, 'createLiveClassSchedule']);
        Route::post('update-live-class-schedule', [ClassScheduleController::class, 'updateLiveClassSchedule']);
        Route::post('delete-live-class-schedule', [ClassScheduleController::class, 'deleteLiveClassSchedule']);
        Route::get('live-class-list', [ClassScheduleController::class, 'mentorLiveClassList']);
        Route::get('live-class-details', [ClassScheduleController::class, 'mentorLiveClassDetails']);
        Route::post('start-live-class', [CourseController::class, 'startLiveClass']);
        Route::post('end-live-class', [CourseController::class, 'endLiveClass']);
        Route::get('all-students-live-class', [ClassScheduleController::class, 'allStudentsLiveClass']);

        Route::post('create-assignment', [AssignmentController::class, 'createAssignment']);
        Route::get('assignment-list', [AssignmentController::class, 'assignmentList']);
        Route::get('all-students-assignment', [AssignmentController::class, 'allStudentsAssignment']);
        Route::put('update-assignment', [AssignmentController::class, 'updateAssignment']);
        Route::get('assignments-details', [AssignmentController::class, 'assignmentDetailsForMentor']);
        Route::get('submission-details', [AssignmentController::class, 'submissionDetailsForMentor']);
        Route::put('publish-assignment', [AssignmentController::class, 'publishAssignment']);
        Route::post('mark-assignment', [AssignmentController::class, 'markAssignment']);


        Route::get('student-list-for-attendance', [AttendanceController::class, 'studentList']);
        Route::post('save-attendance', [AttendanceController::class, 'saveAttendance']);

        Route::get('batch-list', [BatchController::class, 'mentorBatchList']);

    });




    Route::apiResource('organization-payment-typelist', PaymentTypeOrganizationController::class);
    // List discussions for a course
    Route::get('discussions', [DiscussionController::class, 'index']);

    // Create a new discussion
    Route::post('discussions', [DiscussionController::class, 'store']);

    // Get a specific discussion with comments
    Route::get('discussions/{id}', [DiscussionController::class, 'show']);

    // Add a comment to a discussion
    Route::post('comments', [DiscussionController::class, 'addComment']);

    // Like or unlike a discussion or comment
    Route::post('like', [DiscussionController::class, 'toggleLike']);

    // Report a discussion or comment
    Route::post('report', [DiscussionController::class, 'report']);
});


