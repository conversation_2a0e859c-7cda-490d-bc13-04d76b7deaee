<?php

use App\Http\Controllers\Auth\AuthController;
use App\Http\Controllers\Auth\ForgotPasswordController;
use App\Http\Controllers\Auth\VerifyEmailController;
use App\Http\Controllers\Auth\MobileOtpController;
use App\Http\Controllers\ContentController;
use App\Http\Controllers\EnrollmentController;
use App\Http\Controllers\LocationController;
use App\Http\Controllers\MasterSettingsController;
use App\Http\Controllers\OrganizationController;
use App\Http\Controllers\PackageController;
use App\Http\Controllers\PromotionalSiteController;
use App\Http\Controllers\SchoolController;
use App\Http\Controllers\StudentController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\TemplateController;
use App\Http\Controllers\Api\UserStoryController;
use App\Http\Controllers\Api\UserStoryTestimonialController;
use App\Http\Controllers\FileUploadController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Mail;
use App\Mail\SendMail;


use App\Models\MobileOtp;
use App\Notifications\SendOTPEmail;
use App\Models\Organization;


Route::post('login-with-mobile', [AuthController::class, 'loginWithMobile']);
// ->middleware('throttle:6,60');
Route::post('verify-otp', [MobileOtpController::class, 'verifyOTP']);
Route::post('verify-otp-forgot-password', [MobileOtpController::class, 'verifyOTPForgotPassword']);
Route::post('register-user', [AuthController::class, 'registerUserWithOtp']);
Route::post('verify-password', [AuthController::class, 'verifyPassword']);
Route::post('forgot-password-by-mobile', [AuthController::class, 'forgotPassword']);
Route::post('reset-password', [AuthController::class, 'resetPassword']);


Route::post('/auth/register', [AuthController::class, 'registerUser']);
Route::post('/auth/login', [AuthController::class, 'loginUser']);
Route::post('/auth/admin-login', [AuthController::class, 'loginAdmin']);
Route::post('auth/client-registration', [AuthController::class, 'clientRegistration']);
Route::get('auth/logout', [AuthController::class, 'logout']);
Route::get('country-list', [MasterSettingsController::class, 'countryList']);
Route::get('school-list', [SchoolController::class, 'schoolList']);
Route::get('get-expert-list', [AuthController::class, 'getExpertList']);
Route::post('client-info-save', [PromotionalSiteController::class, 'clientInfoSave']);
Route::get('client-list', [PromotionalSiteController::class, 'clientList']);
Route::get('division-list', [LocationController::class, 'divisionList']);
Route::get('district-list/{division_id}', [LocationController::class, 'districtListByID']);
Route::get('upazila-list/{district_id}', [LocationController::class, 'upazilaListByID']);
Route::get('area-list/{upazilla_id}', [LocationController::class, 'unionListByID']);
Route::get('menu-list', [MasterSettingsController::class, 'adminMenuList']);
Route::get('tag-list', [MasterSettingsController::class, 'tagsList']);
Route::get('organization-list', [OrganizationController::class, 'organizationList']);
Route::get('class-list', [ContentController::class, 'classList']);
Route::post('forgot-password', [ForgotPasswordController::class, 'sendResetLinkEmail']);
Route::get('/email/verify/{id}/{hash}', [VerifyEmailController::class, 'verify'])
    ->middleware(['signed'])
    ->name('verification.verify');


Route::get('templates', [TemplateController::class, 'getTemplates']);


Route::get('package-list', [PaymentController::class, 'packageList']);

Route::middleware('auth:sanctum')->group(function () {

    Route::post('logout', [AuthController::class, 'logoutCurrentUser']);
    Route::post('logout-by-device', [AuthController::class, 'logoutByDevice']);
    Route::post('logout-all-device', [AuthController::class, 'logoutAllDevice']);
    Route::post('logout-exept-current-device', [AuthController::class, 'logoutExeptCurrentDevice']);
    Route::post('change-password', [AuthController::class, 'changePassword']);
    Route::get('get-login-devices', [AuthController::class, 'getLoginDevices']);

    Route::get('get-profile', [AuthController::class, 'getProfile']);
    Route::post('profile-update', [AuthController::class, 'updateUser']);
    Route::post('update-interest', [AuthController::class, 'updateInterest']);
    Route::post('update-tags', [StudentController::class, 'updateInterests']);
    Route::post('item-enrollment', [EnrollmentController::class, 'itemEnrollment']);
    Route::post('/email/resend', [VerifyEmailController::class, 'resend'])
        ->middleware('throttle:6,1')
        ->name('verification.resend');

});

Route::middleware(['auth:sanctum', 'verified'])->get('/user/profile', function (Request $request) {
    return $request->user();
});

Route::group(['prefix' => 'open'], function () {
    Route::get('package-list', [PackageController::class, 'packageList']);
    Route::get('package-details-by-id/{package_id}', [PackageController::class, 'packageDetailsByID']);
    Route::get('syllabus-list', [MasterSettingsController::class, 'packageTypeList']);

    // User Stories API routes for public access
    Route::get('user-stories', [UserStoryController::class, 'index']);
    Route::get('user-stories/landing', [UserStoryController::class, 'landing']);
    Route::get('user-stories/{userStory}', [UserStoryController::class, 'show']);
    Route::get('user-stories/{userStory}/testimonials', [UserStoryController::class, 'testimonials']);

    // Testimonials API routes for public access
    Route::get('testimonials', [UserStoryTestimonialController::class, 'index']);
    Route::get('testimonials/featured', [UserStoryTestimonialController::class, 'featured']);
    Route::get('testimonials/{testimonial}', [UserStoryTestimonialController::class, 'show']);
    Route::get('testimonials/by-user-story/{userStoryId}', [UserStoryTestimonialController::class, 'byUserStory']);
});

Route::post('truncate-data', [MasterSettingsController::class, 'truncateData']);

// File Upload Routes for Testing AWS S3 Integration
Route::prefix('file-upload')->group(function () {
    Route::post('/s3', [FileUploadController::class, 'uploadToS3']);
    Route::post('/local', [FileUploadController::class, 'uploadToLocal']);
    Route::post('/image/s3', [FileUploadController::class, 'uploadImageS3']);
    Route::post('/image/local', [FileUploadController::class, 'uploadImageLocal']);
    Route::post('/multiple/s3', [FileUploadController::class, 'uploadMultipleFiles']);
    Route::delete('/s3', [FileUploadController::class, 'deleteFromS3']);
    Route::post('/signed-url', [FileUploadController::class, 'getSignedUrl']);
    Route::post('/check-exists', [FileUploadController::class, 'checkFileExists']);
});

Route::any('{url}', function () {
    return response()->json(['message' => 'Page Not Found'], 404);
})->where('url', '.*');
