<?php

namespace App\Console\Commands;

use App\Models\Category;
use Illuminate\Console\Command;
use Illuminate\Support\Str;

class GenerateCategorySlugs extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'categories:generate-slugs';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate slugs for all categories that do not have one';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $categories = Category::whereNull('slug')->orWhere('slug', '')->get();
        
        $count = 0;
        foreach ($categories as $category) {
            $slug = Str::slug($category->name);
            $originalSlug = $slug;
            $counter = 1;
            
            // Check if slug already exists
            while (Category::where('slug', $slug)
                ->where('id', '!=', $category->id)
                ->where('organization_id', $category->organization_id)
                ->exists()) {
                $slug = "{$originalSlug}-{$counter}";
                $counter++;
            }
            
            $category->slug = $slug;
            $category->save();
            $count++;
        }
        
        $this->info("Generated slugs for {$count} categories.");
        
        return Command::SUCCESS;
    }
}
