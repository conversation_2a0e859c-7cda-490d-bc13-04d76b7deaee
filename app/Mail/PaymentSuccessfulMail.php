<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class PaymentSuccessfulMail extends Mailable
{
    use Queueable, SerializesModels;

    public $payment;
    public $course;
    public $user;
    public $organization;
    public $paymentDetail;

    /**
     * Create a new message instance.
     */
    public function __construct($payment, $course, $user, $organization, $paymentDetail = null)
    {
        $this->payment = $payment;
        $this->course = $course;
        $this->user = $user;
        $this->organization = $organization;
        $this->paymentDetail = $paymentDetail;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Payment Confirmation - Course Enrollment Successful',
            from: config('mail.from.address', 'noreply@' . config('app.url')),
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            html: 'emails.payment-successful',
            text: 'emails.payment-successful-text',
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
