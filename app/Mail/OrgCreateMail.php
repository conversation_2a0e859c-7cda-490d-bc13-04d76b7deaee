<?php

namespace App\Mail;

use App\Models\Organization;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class OrgCreateMail extends Mailable
{
    use Queueable, SerializesModels;

    public $organization;

    public $customUrl;
    public $adminUrl;

    /**
     * Create a new message instance.
     */
    public function __construct(Organization $organization)
    {
        $this->organization = $organization;
        $this->customUrl = $organization->short_name;
        $this->adminUrl = "https://edupackbd.com/login";
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Organization Created Successfully',
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            markdown: 'emails.organization-reg',
            with: [
                'organization' => $this->organization,
                'customUrl' => $this->customUrl,
                'adminUrl' => $this->adminUrl
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
