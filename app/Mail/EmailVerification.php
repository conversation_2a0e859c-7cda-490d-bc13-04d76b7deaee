<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\URL;

class EmailVerification extends Mailable
{
    use Queueable, SerializesModels;

    public $url;

    /**
     * Create a new message instance.
     *
     * @param  string  $email
     */
    public function __construct($user)
    {
        // Generate a signed URL with a token for email verification

        $generate = URL::signedRoute('verify-email', [
            'id' => $user->getKey(),
            'hash' => sha1($user->getEmailForVerification()),
        ]);

        // Replace backend URL with frontend URL and add header authorization token
        $this->url = str_replace(env('APP_URL'), env('FRONTEND_URL'), $generate);
        $this->url = $this->url . '?token=' . $user->createToken('auth_token')->plainTextToken;
        $user->tokens()->where('name', 'auth_token')->delete();
        $user->createToken('auth_token', ['token' => $user->createToken('auth_token')->plainTextToken]);
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Email Verification',
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            markdown: 'emails.email-verification',
            with: ['url' => $this->url] // Pass the URL to the view
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
