<?php

namespace App\Notifications;

use Illuminate\Auth\Notifications\VerifyEmail as VerifyEmailNotification;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Support\Facades\URL;

class CustomVerifyEmail extends VerifyEmailNotification
{
    /**
     * Build the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        // Custom verification URL
        $verificationUrl = $this->customVerificationUrl($notifiable);

        return (new MailMessage)
            ->subject('🚀 Confirm Your Email Address')
            ->greeting('Hello '.$notifiable->name.' 👋,')
            ->line('We are excited to have you on board. Please confirm your email address by clicking the button below:')
            ->action('Verify Email Address', $verificationUrl)
            ->line('If you did not create an account, no further action is required.')
            ->line('Thank you for choosing our service!')
            ->salutation('Cheers,')
            ->salutation('The Team')
            ->markdown('emails.verify-email', [
                'url' => $verificationUrl,
                'notifiable' => $notifiable,
            ]);
    }

    /**
     * Generate the custom verification URL that includes the frontend URL.
     *
     * @param  mixed  $notifiable
     * @return string
     */
    protected function customVerificationUrl($user)
    {
        // $baseFrontendUrl = env('SPA_URL').'/email/verify';

        $verificationUrl = URL::temporarySignedRoute(
            'verification.verify',
            now()->addMinutes(60),
            ['id' => $user->getKey(), 'hash' => sha1($user->getEmailForVerification())]
        );

        // Extract the query string from the signed URL
        // $queryString = parse_url($verificationUrl, PHP_URL_QUERY);

        // Return the full URL including the frontend base URL and query string
        return $verificationUrl;
    }
}
