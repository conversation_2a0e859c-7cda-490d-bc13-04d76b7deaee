<?php

namespace App\Notifications;

use Illuminate\Notifications\Notification;
// use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Facades\Mail;

class SendOTPEmail extends Notification
{
    public $connection = 'sync'; // or 'database', 'redis', etc.
    // public $queue = 'default';
    
    protected $otp;
    protected $organization;

    public function __construct($otp, $organization)
    {
        $this->otp = $otp;
        $this->organization = $organization;
    }

    public function via($notifiable)
    {
        return ['mail'];
    }

    public function toMail($notifiable)
    {
        $organization_logo = $this->organization && $this->organization->logo ? $this->organization->logo : null;
        $organization_name = $this->organization ? $this->organization->name : 'Organization';
        $otp = $this->otp;
        $content = "<p>Your <strong>{$organization_name}</strong> verification code is:</p><div style='font-size:2em;font-weight:bold;margin:16px 0;'>{$otp}</div><p>This code will expire in 7 minutes.</p>";

        return (new MailMessage)
            ->subject('Your OTP for Verification')
            ->view('emails.organization_standard', [
                'organization_logo' => 'https://api.edupackbd.com/uploads/'.$organization_logo,
                'organization_name' => $organization_name,
                'slot' => $content,
            ]);
    }
}