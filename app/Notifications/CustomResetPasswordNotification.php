<?php

namespace App\Notifications;

use Illuminate\Auth\Notifications\ResetPassword as ResetPasswordNotification;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Support\Facades\URL;

class CustomResetPasswordNotification extends ResetPasswordNotification
{
    /**
     * Build the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        // Generate the custom reset password URL
        $resetUrl = $this->customResetUrl($notifiable);

        return (new MailMessage)
            ->subject('Reset Your Password')
            ->greeting('Hello!')
            ->line('You are receiving this email because we received a password reset request for your account.')
            ->action('Reset Password', $resetUrl)
            ->line('If you did not request a password reset, no further action is required.')
            ->line('Thank you!')
            ->line('Regards,')
            ->line('Your Application Team');
    }

    /**
     * Generate the custom reset password URL.
     *
     * @param  mixed  $notifiable
     * @return string
     */
    protected function customResetUrl($notifiable)
    {
        $baseFrontendUrl = env('FRONTEND_URL').'/reset-password';

        $resetUrl = URL::temporarySignedRoute(
            'password.reset.form',
            now()->addMinutes(60),
            ['token' => $this->token, 'email' => $notifiable->getEmailForPasswordReset()]
        );

        // dd($resetUrl);
        // Extract the query string from the signed URL
        $queryString = parse_url($resetUrl, PHP_URL_QUERY);

        // Return the full URL including the frontend base URL and query string
        return $resetUrl;
    }
}
