<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\BroadcastMessage;
use Illuminate\Notifications\Notification;

class PushNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected $title;
    protected $message;
    protected $data;
    protected $channel;
    protected $type;
    protected $userId;
    protected $organizationId;

    /**
     * Create a new notification instance.
     *
     * @param string $message
     * @param array $data
     * @param array $options
     * @return void
     */
    public function __construct(string $message, array $data = [], array $options = [])
    {
        $this->message = $message;
        $this->data = $data;
        $this->title = $options['title'] ?? null;
        $this->channel = $options['channel'] ?? 'notifications';
        $this->type = $options['type'] ?? 'general';
        $this->userId = $options['user_id'] ?? null;
        $this->organizationId = $options['organization_id'] ?? null;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param mixed $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['broadcast', 'database'];
    }

    /**
     * Get the broadcastable representation of the notification.
     *
     * @param mixed $notifiable
     * @return BroadcastMessage
     */
    public function toBroadcast($notifiable)
    {
        $channelName = $this->getChannelName();

        return new BroadcastMessage([
            'id' => $notifiable->id ?? null,
            'title' => $this->title,
            'message' => $this->message,
            'data' => $this->data,
            'type' => $this->type,
            'user_id' => $this->userId,
            'organization_id' => $this->organizationId,
            'channel' => $channelName,
            'time' => now()->toIso8601String(),
        ]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @param mixed $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'title' => $this->title,
            'message' => $this->message,
            'data' => $this->data,
            'type' => $this->type,
            'user_id' => $this->userId,
            'organization_id' => $this->organizationId,
            'channel' => $this->getChannelName(),
        ];
    }

    /**
     * Get the type of the notification being broadcast.
     *
     * @return string
     */
    public function broadcastType()
    {
        return 'notification.sent';
    }

    /**
     * Get the channel name based on the notification type and recipients.
     *
     * @return string
     */
    protected function getChannelName()
    {
        if ($this->userId) {
            return "user.{$this->userId}";
        } elseif ($this->organizationId) {
            return "organization.{$this->organizationId}";
        }

        return $this->channel;
    }
}
