<?php

namespace App\Notifications;

use Illuminate\Notifications\Notification;
use Illuminate\Notifications\Messages\MailMessage;
use App\Models\Organization;

class SignupConfirmationEmail extends Notification
{
    protected $password;

    /**
     * Create a new notification instance.
     *
     * @param string|null $password
     */
    public function __construct($password = null)
    {
        $this->password = $password;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail'];
    }

    /**
     * Build the mail message for signup confirmation.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $organization = Organization::find($notifiable->organization_id);

        return (new MailMessage)
            ->subject('🎉 Welcome Aboard – Your Account Is Ready!')
            ->greeting('Hi ' . $notifiable->name . ' 👋,')
            ->line('We’re thrilled to have you join our platform. Your account has been successfully created.')
            ->line('Your login email: ' . $notifiable->email)
            ->line('Your password: ' . $this->password)
            ->markdown('emails.signup-confirmation', [
                'notifiable' => $notifiable,
                'organization' => $organization,
                'password' => $this->password,
            ]);
    }
}
