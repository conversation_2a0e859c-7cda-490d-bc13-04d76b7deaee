<?php

namespace App\Providers;

use App\Repositories\BatchRepository;
use App\Repositories\BatchRepositoryInterface;
use App\Services\BatchService;

use App\Repositories\PaymentRepository;
use App\Repositories\PaymentRepositoryInterface;
use App\Services\PaymentService;

use App\Repositories\OfflineExamRepository;
use App\Repositories\OfflineExamRepositoryInterface;
use App\Services\OfflineExamService;

use App\Exceptions\Handler;
use Illuminate\Contracts\Debug\ExceptionHandler;
use Illuminate\Support\ServiceProvider;
use App\Models\CustomPersonalAccessToken;
use Laravel\Sanctum\Sanctum;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->app->singleton(ExceptionHandler::class, Handler::class);
        $this->app->bind(BatchRepositoryInterface::class, BatchRepository::class);
        $this->app->bind(BatchService::class, function ($app) {
            return new BatchService($app->make(BatchRepositoryInterface::class));
        });

        $this->app->bind(PaymentRepositoryInterface::class, PaymentRepository::class);
        $this->app->bind(PaymentService::class, function ($app) {
            return new PaymentService($app->make(PaymentRepositoryInterface::class));
        });

        $this->app->bind(OfflineExamRepositoryInterface::class, OfflineExamRepository::class);
        $this->app->bind(OfflineExamService::class, function ($app) {
            return new OfflineExamService($app->make(OfflineExamRepositoryInterface::class));
        });

    }



    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        Sanctum::usePersonalAccessTokenModel(CustomPersonalAccessToken::class);
    }
}
