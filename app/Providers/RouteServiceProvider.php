<?php

// app/Providers/RouteServiceProvider.php

namespace App\Providers;

use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Route;

class RouteServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        $this->configureRateLimiting();

        $this->loadRoutes();
    }

    /**
     * Configure the rate limiters for the application.
     *
     * @return void
     */
    protected function configureRateLimiting()
    {
        RateLimiter::for('api', function (Request $request) {
            return Limit::perMinute(60)->by(optional($request->user())->id ?: $request->ip());
        });

        RateLimiter::for('custom-limiter', function (Request $request) {
            return [
                Limit::perMinute(100)->by($request->ip()),
                Limit::perHour(1000)->by($request->ip()),
            ];
        });
    }

    /**
     * Load the custom routes.
     *
     * @return void
     */
    protected function loadRoutes()
    {
        Route::middleware('api')
            ->prefix('api/website')
            ->group(base_path('routes/website.php'));

        Route::middleware('api')
            ->prefix('api/admin')
            ->group(base_path('routes/admin.php'));

        Route::middleware('api')
            ->prefix('api/mobile')
            ->group(base_path('routes/mobile.php'));

        Route::middleware('api')
            ->prefix('api')
            ->group(base_path('routes/discussion.php'));
    }
}
