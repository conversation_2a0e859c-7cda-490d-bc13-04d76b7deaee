<?php

namespace App\Models;

use App\Http\Traits\OrganizationScopedTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ConsumerRequest extends Model
{
    use HasFactory;
    use OrganizationScopedTrait;
    use SoftDeletes;

    protected $table = 'consumer_requests';

    protected $fillable = [
        'created_by',
        'organization_id',
        'name',
        'email',
        'phone',
        'address',
        'occupation',
        'organization_name',
        'organization_address',
        'nid_passport',
        'trade_license',
        'web_address',
        'post_code',
    ];
}
