<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class VideoWatchLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'organization_id',
        'user_id',
        'course_id',
        'course_outline_id',
        'chapter_video_id',
        'is_started',
        'is_watched',
    ];



    public function scopeStarted($query)
    {
        return $query->where('is_started', true);
    }

    public function scopeWatched($query)
    {
        return $query->where('is_watched', true);
    }


}

