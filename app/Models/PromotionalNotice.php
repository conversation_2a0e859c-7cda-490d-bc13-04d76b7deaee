<?php

namespace App\Models;

use App\Http\Traits\OrganizationScopedTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class PromotionalNotice extends Model
{
    use HasFactory;
    use OrganizationScopedTrait;
    use SoftDeletes;

    protected $table = 'promotional_notices';

    protected $fillable = [
        'created_by',
        'organization_id',
        'title',
        'description',
        'navigation_link',
        'feature_image',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];
}
