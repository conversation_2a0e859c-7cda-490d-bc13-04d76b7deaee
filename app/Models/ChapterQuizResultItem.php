<?php

namespace App\Models;

// use App\Traits\OrganizationScopedTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ChapterQuizResultItem extends Model
{
    use HasFactory;
    // use OrganizationScopedTrait;

    protected $fillable = [
        'organization_id',
        'chapter_quiz_result_id',
        'chapter_quiz_item_id',
        'chapter_quiz_question_id',
        'chapter_quiz_true_false_id',
        'chapter_quiz_matching_id',
        'chapter_quiz_fill_in_blank_id',
        'type',
        'answer',
        'is_correct',
        'mark_obtained'
    ];

    protected $casts = [
        'is_correct' => 'boolean',
    ];

    public function chapterQuizResult()
    {
        return $this->belongsTo(ChapterQuizResult::class, 'chapter_quiz_result_id');
    }

    public function chapterQuizItem()
    {
        return $this->belongsTo(ChapterQuizItem::class, 'chapter_quiz_item_id');
    }

    public function chapterQuizQuestion()
    {
        return $this->belongsTo(ChapterQuizQuestion::class, 'chapter_quiz_question_id');
    }

    public function trueFalse()
    {
        return $this->belongsTo(ChapterTrueFalseQuestion::class, 'chapter_quiz_true_false_id');
    }

    public function matching()
    {
        return $this->belongsTo(MatchingQuestion::class, 'chapter_quiz_matching_id');
    }

    public function fillInTheBlank()
    {
        return $this->belongsTo(FillInTheBlankQuestion::class, 'chapter_quiz_fill_in_blank_id');
    }

    public function resultTrueFalse()
    {
        return $this->hasOne(ChapterQuizResultTrueFalse::class, 'chapter_quiz_result_item_id');
    }

    public function resultMatching()
    {
        return $this->hasOne(ChapterQuizResultMatching::class, 'chapter_quiz_result_item_id');
    }

    public function resultFillInBlank()
    {
        return $this->hasOne(ChapterQuizResultFillInTheBlank::class, 'chapter_quiz_result_item_id');
    }
}
