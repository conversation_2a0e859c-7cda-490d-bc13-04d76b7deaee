<?php

namespace App\Models;

use App\Http\Traits\OrganizationScopedTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Topic extends Model
{
    use HasFactory;
    use OrganizationScopedTrait;
    use SoftDeletes;

    protected $table = 'topics';

    protected $fillable = [
        'created_by',
        'organization_id',
        'title',
        'hint',
        'country_id',
        'package_type_id',
        'catagory_id',
        'grade_id',
        'school_id',
        'limit',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];
}
