<?php

namespace App\Models;

use App\Http\Traits\OrganizationScopedTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class StudentInformation extends Model
{
    use HasFactory;
    use OrganizationScopedTrait;
    use SoftDeletes;

    protected $table = 'student_informations';

    protected $fillable = [
        'created_by',
        'user_id',
        'name',
        'email',
        'username',
        'student_id',
        'contact_no',
        'student_code',
        'organization_id',
        'education',
        'institute',
        'device_id',
        'alternative_contact_no',
        'gender',
        'blood_group',
        'bio',
        'father_name',
        'mother_name',
        'religion',
        'marital_status',
        'date_of_birth',
        'profession',
        'current_address',
        'permanent_address',
        'interests',
        'division_id',
        'city_id',
        'area_id',
        'nid_no',
        'birth_certificate_no',
        'passport_no',
        'image',
        'intro_video',
        'status',
        'is_foreigner',
        'is_active',
        'rating',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * The "booted" method of the model.
     */
    protected static function booted()
    {
        static::creating(function ($student) {
            // Set default values when creating a new student
            if (!isset($student->is_active)) {
                $student->is_active = true;
            }
            if (!isset($student->status)) {
                $student->status = 'Active';
            }
        });
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function organization()
    {
        return $this->belongsTo(Organization::class, 'organization_id', 'id');
    }

    public function payments () {
        return $this->hasMany(Payment::class, 'user_id', 'user_id');
    }

    public function totalCourse () {
        return $this->hasMany(CourseStudent::class, 'student_id');
    }
}
