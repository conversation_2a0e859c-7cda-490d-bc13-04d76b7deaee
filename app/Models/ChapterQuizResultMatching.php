<?php

namespace App\Models;

// use App\Traits\OrganizationScopedTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ChapterQuizResultMatching extends Model
{
    use HasFactory;
    // use OrganizationScopedTrait;

    protected $fillable = [
        'organization_id',
        'chapter_quiz_result_item_id',
        'chapter_quiz_matching_id',
        'matching_answer_id',
        'selected_right_item',
        'is_correct'
    ];

    protected $casts = [
        'is_correct' => 'boolean'
    ];

    public function resultItem()
    {
        return $this->belongsTo(ChapterQuizResultItem::class, 'chapter_quiz_result_item_id');
    }

    public function matchingQuestion()
    {
        return $this->belongsTo(MatchingQuestion::class, 'chapter_quiz_matching_id');
    }

    public function matchingAnswer()
    {
        return $this->belongsTo(MatchingAnswer::class, 'matching_answer_id');
    }
}
