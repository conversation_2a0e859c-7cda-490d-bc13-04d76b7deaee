<?php

namespace App\Models;

use App\Http\Traits\OrganizationScopedTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class WebsiteSetting extends Model
{
    use HasFactory;
    use OrganizationScopedTrait;
    use SoftDeletes;

    protected $table = 'website_settings';

    protected $fillable = [
        'organization_id',
        'title',
        'title_bn',
        'description',
        'image',
        'banner',
        'color_theme',
        'bg_color',
        'type',
        'style',
        'is_active',
        'created_by',
    ];

    public function organization()
    {
        return $this->belongsTo(Organization::class, 'organization_id');
    }
}
