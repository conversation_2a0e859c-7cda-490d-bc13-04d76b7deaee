<?php

namespace App\Models;

use App\Http\Traits\OrganizationScopedTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CourseCategory extends Model
{
    use HasFactory;
    use OrganizationScopedTrait;
    use SoftDeletes;

    protected $fillable = [
        'created_by',
        'organization_id',
        'name',
        'name_bn',
        'course_id',
        'subject_id',
        'sequence',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    public function course() {
        return $this->belongsTo(Course::class);
    }
    public function courseOutlines()
    {
        return $this->hasMany(CourseOutline::class, 'course_category_id');
    }

    public function courseVideo()
    {
        return $this->hasMany(CourseOutline::class, 'course_category_id')->whereNotNull('chapter_video_id');
    }

    public function courseScript()
    {
        return $this->hasMany(CourseOutline::class, 'course_category_id')->whereNotNull('chapter_script_id');
    }

    public function courseQuiz()
    {
        return $this->hasMany(CourseOutline::class, 'course_category_id')->whereNotNull('chapter_quiz_id');
    }
}
