<?php

namespace App\Models;

use App\Http\Traits\OrganizationScopedTrait;
use App\Notifications\CustomResetPasswordNotification;
use App\Notifications\CustomVerifyEmail;
use App\Notifications\SignupConfirmationEmail;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
// use Rappasoft\LaravelAuthenticationLog\Traits\AuthenticationLoggable;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Str;
use Lara<PERSON>\Sanctum\HasApiTokens;
use <PERSON><PERSON>\Sanctum\NewAccessToken;

use Jenssegers\Agent\Agent;

class User extends Authenticatable implements MustVerifyEmail
{
    use HasApiTokens, HasFactory, Notifiable, OrganizationScopedTrait, SoftDeletes;

    protected $fillable = [
        'created_by',
        'name',
        'email',
        'contact_no',
        'address',
        'username',
        'organization_id',
        'image',
        'password',
        'user_type',
        'phone_verified_at',
        'is_active',
    ];


    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected $casts = [
        'email_verified_at' => 'datetime',
        'phone_verified_at' => 'datetime',
        'is_active' => 'boolean',
    ];

    /**
     * The "booted" method of the model.
     */
    protected static function booted()
    {
        static::creating(function ($user) {
            // Set default values when creating a new user
            if (!isset($user->is_active)) {
                $user->is_active = true;
            }
        });

    }

    public function createToken(string $name, array $abilities = ['*'])
    {
        $agent = new Agent();

        // Get device name
        $device = $agent->device();

        // Get platform (OS)
        $platform = request()->input('device') ?? $agent->platform();

        // Get browser
        $browser = request()->input('phone_name') ?? $agent->browser();

        $token = $this->tokens()->create([
            'name' => $name,
            'token' => hash('sha256', $plainTextToken = Str::random(240)),
            'abilities' => $abilities,
            'ip' => request()->ip(),
            'device' => $platform,
            'browser' => $browser
        ]);

        return new NewAccessToken($token, $token->getKey().'|'.$plainTextToken);
    }

    public function organization()
    {
        return $this->belongsTo(Organization::class, 'organization_id', 'id');
    }

    public function sendEmailVerificationNotification()
    {
        $this->notify(new CustomVerifyEmail);
    }


    public function sendConfirmationEmail($password)
    {
        $this->notify(new SignupConfirmationEmail($password));
    }

    public function sendPasswordResetNotification($token)
    {
        $this->notify(new CustomResetPasswordNotification($token));
    }

    public function purchasedCourses () {
        return $this->hasMany(Payment::class, 'user_id', 'id')
            ->join('courses', 'payments.item_id', '=', 'courses.id')
            ->select('payments.*', 'courses.title as course_title')
            ->orderBy('payments.id', 'DESC');
    }

}
