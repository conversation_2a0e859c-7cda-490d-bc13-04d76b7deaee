<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Currency extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'symbol',
        'status',
        'code',
        'is_default',
        'exchange_rate',
    ];

    protected $casts = [
        'is_default' => 'boolean',
        'exchange_rate' => 'decimal:5'
    ];



    /**
     * Scope a query to only include active currencies.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope a query to only include default currency.
     */
    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    /**
     * Get the formatted currency display.
     */
    public function getFormattedNameAttribute()
    {
        return "{$this->name} ({$this->symbol})";
    }
}

