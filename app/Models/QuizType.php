<?php

namespace App\Models;

use App\Http\Traits\OrganizationScopedTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class QuizType extends Model
{
    use HasFactory;
    use SoftDeletes;
    // use OrganizationScopedTrait;

    protected $fillable = [
        'organization_id',
        'name',
        'name_bn',
        'participation_limit',
        'in_course',
        'is_active',
        'created_by',
    ];
}
