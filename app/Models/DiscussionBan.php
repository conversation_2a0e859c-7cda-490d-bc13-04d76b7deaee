<?php

namespace App\Models;

use App\Http\Traits\OrganizationScopedTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DiscussionBan extends Model
{
    use HasFactory;
    use OrganizationScopedTrait;

    protected $fillable = [
        'organization_id',
        'user_id',
        'course_id', // If null, banned from all discussions
        'banned_by',
        'reason',
        'expires_at', // If null, permanent ban
    ];

    protected $casts = [
        'expires_at' => 'datetime',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function course()
    {
        return $this->belongsTo(Course::class);
    }

    public function bannedBy()
    {
        return $this->belongsTo(User::class, 'banned_by');
    }

    public function isExpired()
    {
        return $this->expires_at && now()->gt($this->expires_at);
    }
}
