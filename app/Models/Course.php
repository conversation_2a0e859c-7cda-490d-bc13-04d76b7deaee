<?php

namespace App\Models;

use App\Http\Traits\OrganizationScopedTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Course extends Model
{
    use HasFactory;
    use OrganizationScopedTrait;
    use SoftDeletes;

    protected $fillable = [
        'created_by',
        'organization_id',
        'title',
        'title_bn',
        'category_id',
        'sub_category_id',
        'gp_product_id',
        'course_type_id',
        'youtube_url',
        'description',
        'thumbnail',
        'icon',
        'number_of_enrolled',
        'regular_price',
        'sale_price',
        'show_price',
        'currency',
        'discount_percentage',
        'minimum_enroll_amount',
        'max_installment_qty',
        'installment_type',
        'monthly_amount',
        'rating',
        'has_life_coach',
        'is_featured',
        'is_active',
        'is_free',
        'sequence',
        'appeared_from',
        'appeared_to',
        'routine_text',
        'is_draft',
        'slug',
        'course_duration',
        'duration_per_day'
    ];


    protected $casts = [
        'is_active' => 'boolean',
        'is_free' => 'boolean',
        'is_draft' => 'boolean',
        'is_featured' => 'boolean',
        'has_life_coach' => 'boolean',
    ];

    public static function boot()
    {
        parent::boot();

        static::creating(function($course) {
            $course->slug = $course->updateOrCreateSlug();
        });

    }

    public function updateOrCreateSlug()
    {
        $slug = strtolower(preg_replace('/\s/', '_', $this->title));
        $originalSlug = $slug;
        $counter = 1;

        while (Course::where('id', '!=', $this->id)->where('slug', $slug)->exists()) {
            $slug = $originalSlug . '_' . $counter;
            $counter++;
        }

        return $slug;
    }

    public function courseCategory()
    {
        return $this->hasMany(CourseCategory::class);
    }

    public function courseOutline()
    {
        return $this->hasMany(CourseOutline::class);
    }

    public function courseRoutine()
    {
        return $this->hasMany(CourseClassRoutine::class);
    }

    public function courseFeature()
    {
        return $this->hasMany(CourseFeature::class);
    }

    public function courseLearningItems()
    {
        return $this->hasMany(CourseLearningItem::class);
    }
    public function courseMentor()
    {
        return $this->hasMany(CourseMentor::class);
    }

    public function courseFaq()
    {
        return $this->hasMany(CourseFaq::class);
    }
    public function externalLibrary()
    {
        return $this->hasMany(CourseExternalLibrary::class);
    }
    public function category()
    {
        return $this->belongsTo(Category::class, 'category_id');
    }
    public function subCategory()
    {
        return $this->belongsTo(SubCategory::class, 'sub_category_id');
    }
    public function mentors()
    {
        return $this->belongsToMany(MentorInformation::class, 'course_mentors', 'course_id', 'mentor_id')
            ->withPivot('is_active'); // Add any other pivot columns you want to access
    }

    public function organization()
    {
        return $this->belongsTo(Organization::class);
    }


    // Relationship: Courses that belong to the same category
    public function relatedCourses()
    {
        return $this->hasMany(Course::class, 'category_id', 'category_id')
                    ->where('id', '!=', $this->id)
                    ->where('is_active', 1); // Exclude the current course
    }




    public function coursePrerequisites()
    {
        return $this->hasMany(CoursePrerequisite::class);
    }

    public function courseRatings()
    {
        return $this->hasMany(CourseRating::class)->orderBy('rating', 'desc');
    }


    public function assignments()
    {
        return $this->hasMany(Assignment::class);
    }


    public function liveClasses()
    {
        return $this->hasMany(ClassSchedule::class);
    }

    public function videoWatchLogs()
    {
        return $this->hasMany(VideoWatchLog::class);
    }

    public function scriptViewLogs()
    {
        return $this->hasMany(ScriptViewLog::class);
    }

    public function chapterQuizResults()
    {
        return $this->hasMany(ChapterQuizResult::class);
    }


    public function assignmentSubmissions()
    {
        return $this->hasMany(AssignmentSubmission::class);
    }

    public function certificateTemplate()
    {
        return $this->hasOne(CertificateTemplate::class, 'course_id', 'id')->where('is_active', 1);
    }

    public function generatedCertificates()
    {
        return $this->hasMany(Certificate::class, 'course_id', 'id')->orderBy('created_at', 'desc');
    }


    public function scopeIsActive($query)
    {
        return $query->where('courses.is_active', 1);
    }

    public function subjects() {
        return $this->hasMany(Subject::class);
    }

    public function batches() {
        return $this->hasMany(Batch::class);
    }

    public function checkIfUserIsEnrolled ($userId, $courseId)
    {

    }

}
