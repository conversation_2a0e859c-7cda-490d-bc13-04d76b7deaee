<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class OfflineExam extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'organization_id',
        'course_id',
        'title',
        'title_bn',
        'description',
        'mcq_mark',
        'written_mark',
        'assignment_mark',
        'presentation_mark',
        'practical_mark',
        'pass_mark',
        'total_mark',
        'duration',
        'exam_date',
    ];


    public function course()
    {
        return $this->belongsTo(Course::class);
    }

    public function students()
    {
        return $this->hasMany(OfflineExamStudents::class);
    }

    public function batches () {
        return $this->hasMany(OfflineExamBatch::class);
    }

}

