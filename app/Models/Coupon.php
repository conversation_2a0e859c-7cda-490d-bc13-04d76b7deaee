<?php

namespace App\Models;

use App\Http\Traits\OrganizationScopedTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Coupon extends Model
{
    use HasFactory;
    use OrganizationScopedTrait;
    use SoftDeletes;

    protected $fillable = [
        'code',
        'discount',
        'discount_type',
        'expiry_date',
        'usage_number',
        'max_usage',
        'is_active',
        'created_by',
    ];


    protected $casts = [
        'is_active' => 'boolean',
        'expiry_date' => 'datetime',
        'discount' => 'float'
    ];

    /**
     * Get the organization that owns the coupon.
     */
    public function organization()
    {
        return $this->belongsTo(Organization::class);
    }

    /**
     * Scope a query to only include active coupons.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Check if the coupon is expired.
     */
    public function isExpired()
    {
        return $this->expiry_date && $this->expiry_date->isPast();
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->organization_id = auth()->user()->organization_id;
            $model->created_by = auth()->id();
        });
    }

    public function usages () {
        return $this->hasMany(Payment::class, 'coupon_id');
    }


}

