<?php

namespace App\Models;

use App\Http\Traits\OrganizationScopedTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Interest extends Model
{
    use HasFactory;
    use OrganizationScopedTrait;
    use SoftDeletes;

    protected $table = 'interests';

    protected $fillable = [
        'created_by',
        'organization_id',
        'tags',
    ];

    // protected $casts = [
    //     'is_active' => 'boolean',
    // ];
}
