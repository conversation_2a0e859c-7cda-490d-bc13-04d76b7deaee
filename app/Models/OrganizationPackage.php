<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class OrganizationPackage extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'plan_name',
        'description',
        'plan_image',
        'price',
        'discount_percentage',
        'sale_price',
        'dollar_price',
        'dollar_discount_percentage',
        'dollar_sale_price',
        'yen_price',
        'yen_discount_percentage',
        'yen_sale_price',
        'krw_price',
        'krw_discount_percentage',
        'krw_sale_price',
        'billing_cycle',
        'features',
        'disabled_features',
        'max_users',
        'max_courses',
        'support_level',
        'trial_period_days',
        'is_active',
        'is_popular',
    ];
}

