<?php

namespace App\Models;

use App\Http\Traits\OrganizationScopedTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ChapterQuizResult extends Model
{
    use HasFactory;
    use OrganizationScopedTrait;
    use SoftDeletes;

    protected $fillable = [
        'created_by',
        'organization_id',
        'user_id',
        'chapter_quiz_id',
        'course_id',
        'submission_status',
        'mark',
        'positive_count',
        'negetive_count',
        'end_time',
        'submitted_at'
    ];

    protected $casts = [
        'end_time' => 'datetime:Y-m-d\TH:i:s\Z',
        'submitted_at' => 'datetime:Y-m-d\TH:i:s\Z',
    ];


    protected static function booted()
    {
        static::updating(function ($result) {
            if ($result->submitted_at) {
                $student = \App\Models\StudentInformation::where('user_id', $result->user_id)->first();
                \App\Models\CourseStudent::where('course_id', $result->course_id)
                    ->where('student_id', $student->id)
                    ->increment('quiz_participation_count');
            }
        });
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function course () {
        return $this->belongsTo(Course::class, 'course_id');
    }
    public function quiz()
    {
        return $this->belongsTo(ChapterQuiz::class, 'chapter_quiz_id');
    }

    public function answers()
    {
        return $this->hasMany(ChapterQuizResultAnswer::class, 'chapter_quiz_result_id')
            ->join('chapter_quiz_questions', 'chapter_quiz_result_answers.question_id', '=', 'chapter_quiz_questions.id')
            ->select(
            "chapter_quiz_result_answers.id",
            "chapter_quiz_result_answers.question_id",
            "chapter_quiz_result_answers.answer1",
            "chapter_quiz_result_answers.answer2",
            "chapter_quiz_result_answers.answer3",
            "chapter_quiz_result_answers.answer4",
            "chapter_quiz_result_answers.is_correct",
            'chapter_quiz_questions.question_text',
            'chapter_quiz_questions.question_text_bn',
            'chapter_quiz_questions.question_image',
            'chapter_quiz_questions.option1',
            'chapter_quiz_questions.option2',
            'chapter_quiz_questions.option3',
            'chapter_quiz_questions.option4',
            'chapter_quiz_questions.option1_image',
            'chapter_quiz_questions.option2_image',
            'chapter_quiz_questions.option3_image',
            'chapter_quiz_questions.option4_image',
            'chapter_quiz_questions.answer1 as set_answer1',
            'chapter_quiz_questions.answer2 as set_answer2',
            'chapter_quiz_questions.answer3 as set_answer3',
            'chapter_quiz_questions.answer4 as set_answer4'
        );
    }

    public function writtenMarks()
    {
        return $this->hasOne(ChapterQuizWrittenMark::class, 'chapter_quiz_result_id');
    }
}
