<?php

namespace App\Models;

use App\Http\Traits\OrganizationScopedTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CourseOutline extends Model
{
    use HasFactory;
    use OrganizationScopedTrait;
    use SoftDeletes;

    protected $fillable = [
        'created_by',
        'organization_id',
        'title',
        'title_bn',
        'course_id',
        'class_level_id',
        'subject_id',
        'chapter_id',
        'chapter_script_id',
        'chapter_video_id',
        'chapter_quiz_id',
        'is_free',
        'sequence',
        'is_active',
        'course_category_id',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_free' => 'boolean',
        'duration' => 'integer',

    ];

    public function course()
    {
        return $this->belongsTo(Course::class);
    }

    public function courseCategory()
    {
        return $this->belongsTo(CourseCategory::class, 'course_category_id');
    }

    public function classLevel()
    {
        return $this->belongsTo(ClassLevel::class);
    }

    public function subject()
    {
        return $this->belongsTo(Subject::class);
    }

    public function chapter()
    {
        return $this->belongsTo(Chapter::class);
    }

    public function script()
    {
        return $this->belongsTo(ChapterScript::class, 'chapter_script_id');
    }

    public function video()
    {
        return $this->belongsTo(ChapterVideo::class, 'chapter_video_id');
    }

    public function quiz()
    {
        return $this->belongsTo(ChapterQuiz::class, 'chapter_quiz_id');
    }



        // Relationship: Courses that belong to the same category
    public function otherContents()
    {
        return $this->hasMany(CourseOutline::class, 'course_category_id', 'course_category_id')
                    ->where('id', '!=', $this->id);
    }

    public function previous()
    {
        return $this->hasOne(CourseOutline::class, 'course_category_id', 'course_category_id')
                    ->where('id', '<', $this->id)
                    ->orderBy('id', 'desc');
    }

}
