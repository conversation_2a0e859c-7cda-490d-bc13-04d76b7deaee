<?php

namespace App\Models;

use App\Http\Traits\OrganizationScopedTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ClassSchedule extends Model
{
    use HasFactory;
    use OrganizationScopedTrait;
    use SoftDeletes;

    protected $fillable = [
        'title',
        'created_by',
        'organization_id',
        'course_student_mapping_id',
        'course_id',
        'batch_id',
        'student_id',
        'mentor_id',
        'class_url',
        'is_physical',
        'schedule_datetime',
        'has_started',
        'has_completed',
        'start_time',
        'end_time',
        'started_at',
        'ended_at',
        'student_end_time',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'has_started' => 'boolean',
        'has_completed' => 'boolean',
        'is_physical' => 'boolean',
    ];

    public function course()
    {
        return $this->belongsTo(Course::class, 'course_id', 'id');
    }

    public function mentor() {
        return $this->belongsTo(MentorInformation::class, 'mentor_id', 'id');
    }


    public function studentJoinHistory()
    {
        return $this->hasMany(StudentJoinHistory::class, 'class_schedule_id');
    }

    public function students() {
        return $this->hasMany(ClassScheduleStudents::class, 'class_schedule_id');
    }

    public function batch () {
        return $this->belongsTo(Batch::class, 'batch_id', 'id');
    }
}
