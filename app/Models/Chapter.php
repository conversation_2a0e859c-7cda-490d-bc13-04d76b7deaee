<?php

namespace App\Models;

use App\Http\Traits\OrganizationScopedTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Chapter extends Model
{
    use HasFactory;
    use OrganizationScopedTrait;
    use SoftDeletes;

    protected $fillable = [
        'created_by',
        'organization_id',
        'name',
        'name_bn',
        'class_level_id',
        'subject_id',
        'chapter_code',
        'price',
        'is_free',
        'icon',
        'color_code',
        'sequence',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_free' => 'boolean',
    ];

    public function scripts()
    {
        return $this->hasMany(ChapterScript::class);
    }

    public function videos()
    {
        return $this->hasMany(ChapterVideo::class);
    }

    public function quiz()
    {
        return $this->hasMany(ChapterQuiz::class);
    }
}
