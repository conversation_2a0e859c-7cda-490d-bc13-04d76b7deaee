<?php

namespace App\Models;

use App\Http\Traits\OrganizationScopedTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class MentorInformation extends Model
{
    use HasFactory;
    use OrganizationScopedTrait;
    use SoftDeletes;

    protected $table = 'mentor_informations';

    protected $fillable = [
        'created_by',
        'user_id',
        'name',
        'email',
        'username',
        'education',
        'institute',
        'contact_no',
        'mentor_code',
        'organization_id',
        'device_id',
        'referral_code',
        'referred_code',
        'alternative_contact_no',
        'blood_group',
        'gender',
        'bio',
        'father_name',
        'mother_name',
        'religion',
        'marital_status',
        'date_of_birth',
        'profession',
        'current_address',
        'permanent_address',
        'division_id',
        'district_id',
        'city_id',
        'area_id',
        'nid_no',
        'birth_certificate_no',
        'passport_no',
        'interests',
        'image',
        'intro_video',
        'status',
        'is_foreigner',
        'is_life_couch',
        'is_host_staff',
        'is_host_certified',
        'is_active',
        'rating',
        'approval_date',
        'host_rank_number',
        'is_featured',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_foreigner' => 'boolean',
        'is_life_couch' => 'boolean',
        'is_host_staff' => 'boolean',
        'is_host_certified' => 'boolean',
        'is_featured' => 'boolean',
    ];

    /**
     * The "booted" method of the model.
     */
    protected static function booted()
    {
        static::creating(function ($mentor) {
            // Set default values when creating a new mentor
            if (!isset($mentor->is_active)) {
                $mentor->is_active = true;
            }
            if (!isset($mentor->status)) {
                $mentor->status = 'Active';
            }
        });
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function organization()
    {
        return $this->belongsTo(Organization::class, 'organization_id', 'id');
    }

    public function courses ()
    {
        return $this->hasMany(CourseMentor::class, 'mentor_id');
    }
    public function courseList ()
    {
        return $this->hasMany(CourseMentor::class, 'mentor_id')->join('courses', 'course_mentors.course_id', '=', 'courses.id')->where('courses.is_active', 1);
    }
    public function studentNumber () {
        return $this->hasMany(CourseMentor::class, 'mentor_id')
        ->join('payments', 'course_mentors.course_id', '=', 'payments.item_id');
    }

    public function totalAssignments () {
        return $this->hasMany(Assignment::class, 'mentor_id', 'id');
    }

    public function assignments () {
        return $this->hasMany(Assignment::class, 'mentor_id', 'id')->whereDate('deadline', '>', now());
    }

    public function totalLiveClasses () {
        return $this->hasMany(ClassSchedule::class, 'mentor_id', 'id');
    }
    public function liveClasses () {
        return $this->hasMany(ClassSchedule::class, 'mentor_id', 'id')->whereDate('schedule_datetime', '>=', now());
    }
}
