<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Attendance extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'course_id',
        'batch_id',
        'student_id',
        'class_schedule_id',
        'is_present',
        'reason',
        'attendance_date',
        'attendance_by',
    ];

    protected $casts = [
        'is_present' => 'boolean',
    ];
}

