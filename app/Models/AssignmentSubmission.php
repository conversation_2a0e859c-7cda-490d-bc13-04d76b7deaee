<?php

namespace App\Models;

use App\Http\Traits\OrganizationScopedTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class AssignmentSubmission extends Model
{
    use HasFactory;
    use OrganizationScopedTrait;
    use SoftDeletes;

    protected $fillable = [
        'created_by',
        'organization_id',
        'course_id',
        'assignment_id',
        'student_id',
        'evaluated_by',
        'ip_address',
        'answer',
        'marks',
        'remarks',
        'status',
    ];



    protected $casts = [
        'course_id' => 'integer',
        'assignment_id' => 'integer',
    ];

    protected static function boot()
    {
        parent::boot();

        static::created(function ($assignmentSubmission) {
            
            CourseStudent::where('course_id', $assignmentSubmission->course_id)
                ->where('student_id', $assignmentSubmission->student_id)
                ->increment('assignment_submission_count');
        });
    }

    public function assignmentAttachments()
    {
        return $this->hasMany(AssignmentAttachment::class, 'assignment_submission_id', 'id');
    }

    public function assignment()
    {
        return $this->belongsTo(Assignment::class, 'assignment_id', 'id');
    }
}
