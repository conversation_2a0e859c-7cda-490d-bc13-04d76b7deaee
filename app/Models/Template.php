<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Template extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'short_description',
        'folder_name',
        'theme_color',
        'title_color',
        'text_color',
        'theme_image',
        'is_active',
    ];

    public function items()
    {
        return $this->hasMany(TemplateItem::class);
    }
}
