<?php

namespace App\Models;

use App\Http\Traits\OrganizationScopedTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DiscussionComment extends Model
{
    use HasFactory;
    use OrganizationScopedTrait;

    protected $fillable = [
        'organization_id',
        'discussion_id',
        'user_id',
        'parent_id',
        'content',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    public function discussion()
    {
        return $this->belongsTo(Discussion::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function parent()
    {
        return $this->belongsTo(DiscussionComment::class, 'parent_id');
    }

    public function replies()
    {
        return $this->hasMany(DiscussionComment::class, 'parent_id');
    }

    public function likes()
    {
        return $this->hasMany(DiscussionLike::class, 'comment_id');
    }

    public function reports()
    {
        return $this->hasMany(DiscussionReport::class, 'comment_id');
    }

    public function isLikedByUser($userId)
    {
        return $this->likes()->where('user_id', $userId)->exists();
    }

    public function isReportedByUser($userId)
    {
        return $this->reports()->where('user_id', $userId)->exists();
    }
}
