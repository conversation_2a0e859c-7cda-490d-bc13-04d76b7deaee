<?php

namespace App\Models;

use App\Http\Traits\OrganizationScopedTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ChapterQuizWrittenQuestion extends Model
{
    use HasFactory;
    use OrganizationScopedTrait;
    use SoftDeletes;

    protected $fillable = [
        'created_by',
        'organization_id',
        'chapter_quiz_id',
        'description',
        'question_attachment',
        'instruction',
        'duration',
        'marks',
        'no_of_question',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];
}
