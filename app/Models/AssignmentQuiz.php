<?php

namespace App\Models;

use App\Http\Traits\OrganizationScopedTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class AssignmentQuiz extends Model
{
    use HasFactory;
    use OrganizationScopedTrait;
    use SoftDeletes;

    protected $fillable = [
        'created_by',
        'organization_id',
        'assignment_id',
        'student_id',
        'chapter_quiz_id',
        'gained_marks',
        'has_completed',
    ];

    protected $casts = [
        'has_completed' => 'boolean',
    ];
}
