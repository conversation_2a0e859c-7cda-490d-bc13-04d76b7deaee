<?php

namespace App\Models;

// use App\Traits\OrganizationScopedTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ChapterQuizResultFillInTheBlank extends Model
{
    use HasFactory;
    // use OrganizationScopedTrait;

    protected $fillable = [
        'organization_id',
        'chapter_quiz_result_item_id',
        'chapter_quiz_fill_in_blank_id',
        'blank_answer_id',
        'user_answer',
        'is_correct'
    ];

    protected $casts = [
        'is_correct' => 'boolean'
    ];

    public function resultItem()
    {
        return $this->belongsTo(ChapterQuizResultItem::class, 'chapter_quiz_result_item_id');
    }

    public function fillInBlankQuestion()
    {
        return $this->belongsTo(FillInTheBlankQuestion::class, 'chapter_quiz_fill_in_blank_id');
    }

    public function blankAnswer()
    {
        return $this->belongsTo(BlankAnswer::class, 'blank_answer_id');
    }
}
