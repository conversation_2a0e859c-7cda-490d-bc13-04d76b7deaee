<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CourseExternalLibrary extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'organization_id',
        'course_id',
        'user_id',
        'title',
        'url',
        'description',
        'is_approved',
        'approved_by',
    ];

    protected $casts = [
        'is_approved' => 'boolean',
    ];
}

