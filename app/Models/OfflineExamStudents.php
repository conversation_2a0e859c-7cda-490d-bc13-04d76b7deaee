<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OfflineExamStudents extends Model
{
    use HasFactory;

    protected $fillable = [
        'course_id',
        'offline_exam_id',
        'student_id',
        'batch_id',
        'mcq_mark',
        'written_mark',
        'assignment_mark',
        'presentation_mark',
        'practical_mark',
        'total_mark',
        'is_passed',
        'marked_by',
    ];


    protected $casts = [
        'is_passed' => 'boolean',
        'marked_by' => 'integer',
    ];

    public function student()
    {
        return $this->belongsTo(StudentInformation::class, 'student_id');
    }

    public function batch()
    {
        return $this->belongsTo(Batch::class, 'batch_id');
    }

    public function offline_exam()
    {
        return $this->belongsTo(OfflineExam::class, 'offline_exam_id');
    }
}

