<?php

namespace App\Models;

use App\Http\Traits\OrganizationScopedTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Package extends Model
{
    use HasFactory;
    use OrganizationScopedTrait;
    use SoftDeletes;

    protected $table = 'packages';

    protected $fillable = [
        'created_by',
        'organization_id',
        'title',
        'description',
        'limit',
        'cycle',
        'promotion_title',
        'promotion_details',
        'feature_image',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];
}
