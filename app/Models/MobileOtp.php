<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Notifications\Notifiable;
use App\Notifications\SendOTPEmail;

class MobileOtp extends Model
{
    use HasFactory, SoftDeletes, Notifiable;

    protected $fillable = [
        'mobile_no',
        'email',
        'otp',
        'organization_id',
        'is_used',
        'used_for',
        'expired_at',
    ];

    protected $casts = [
        'mobile_no' => 'string',
        'email' => 'string',
        'otp' => 'string',
        'organization_id' => 'integer',
        'is_used' => 'boolean',
        'used_for' => 'string',
        'expired_at' => 'datetime',
    ];

    public function sendOTP($otp, $organization)
    {
        $this->notify(new SendOTPEmail($otp, $organization));
    }
    public function routeNotificationForMail($notification)
    {
        return $this->email;
    }

}

