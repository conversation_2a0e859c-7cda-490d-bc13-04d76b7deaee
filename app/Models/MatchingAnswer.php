<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use App\Http\Traits\OrganizationScopedTrait;

class MatchingAnswer extends Model
{
    use OrganizationScopedTrait;

    protected $fillable = [
        'matching_question_id',
        'left_item',
        'right_item',
    ];

    public function matchingQuestion()
    {
        return $this->belongsTo(MatchingQuestion::class);
    }
}
