<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Certificate extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $fillable = [
        'user_id',
        'course_id',
        'course_name',
        'certification_type',
        'certificate_file',
        'code',
        'name',
        'background_image',
        'certification_text',
        'logo',
        'signature',
        'authorize_person',
        'designation'
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function student()
    {
        return $this->belongsTo(User::class, 'user_id')
            ->join('students', 'students.user_id', '=', 'users.id');
    }


    public function course()
    {
        return $this->belongsTo(Course::class, 'course_id');
    }


    public function scopeActive($query)
    {
        return $query->where('status', 1);
    }


}

