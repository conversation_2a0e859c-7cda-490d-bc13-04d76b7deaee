<?php

namespace App\Models;

// use App\Traits\OrganizationScopedTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ChapterQuizResultTrueFalse extends Model
{
    use HasFactory;
    // use OrganizationScopedTrait;

    protected $fillable = [
        'organization_id',
        'chapter_quiz_result_item_id',
        'chapter_quiz_true_false_id',
        'answer',
        'is_correct'
    ];

    protected $casts = [
        'answer' => 'boolean',
        'is_correct' => 'boolean'
    ];

    public function resultItem()
    {
        return $this->belongsTo(ChapterQuizResultItem::class, 'chapter_quiz_result_item_id');
    }

    public function trueFalseQuestion()
    {
        return $this->belongsTo(ChapterTrueFalseQuestion::class, 'chapter_quiz_true_false_id');
    }
}
