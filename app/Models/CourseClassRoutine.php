<?php

namespace App\Models;

use App\Http\Traits\OrganizationScopedTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CourseClassRoutine extends Model
{
    use HasFactory;
    use OrganizationScopedTrait;
    use SoftDeletes;

    protected $fillable = [
        'created_by',
        'organization_id',
        'day',
        'class_title',
        'is_note',
        'is_active',
        'course_id',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_note' => 'boolean',
    ];
}
