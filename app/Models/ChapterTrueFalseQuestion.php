<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Http\Traits\OrganizationScopedTrait;
use App\Models\ChapterQuizItem;

class ChapterTrueFalseQuestion extends Model
{
    use SoftDeletes;
    use OrganizationScopedTrait;

    protected $fillable = [
        'chapter_quiz_id',
        'question_text',
        'question_image',
        'answer',
        'explanation_text',
        'explanation_image',
    ];

    protected $casts = [
        'answer' => 'boolean',
    ];

    protected static function boot()
    {
        parent::boot();

        static::created(function ($item) {
            try {

                // Create a corresponding ChapterQuizItem record with only the fields that exist in the table
                $quizItem = new ChapterQuizItem();
                $quizItem->organization_id = $item->organization_id;
                $quizItem->chapter_quiz_id = $item->chapter_quiz_id;
                $quizItem->chapter_quiz_true_false_id = $item->id;
                $quizItem->type = 'true_false';
                // Disable the OrganizationScopedTrait behavior for this save
                $quizItem->saveQuietly();
            } catch (\Exception $e) {
                // Log the error but don't prevent the question from being created
                \Log::error('Failed to create ChapterQuizItem: ' . $e->getMessage());
            }
        });
    }

    public function chapterQuiz()
    {
        return $this->belongsTo(ChapterQuiz::class);
    }
}
