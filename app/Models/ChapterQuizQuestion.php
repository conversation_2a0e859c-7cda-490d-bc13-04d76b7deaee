<?php

namespace App\Models;

use App\Http\Traits\OrganizationScopedTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ChapterQuizQuestion extends Model
{
    use HasFactory;
    use OrganizationScopedTrait;
    use SoftDeletes;

    protected $table = 'chapter_quiz_questions';

    protected $fillable = [
        'created_by',
        'organization_id',
        'chapter_quiz_id',
        'class_level_id',
        'subject_id',
        'chapter_id',
        'question_text',
        'question_text_bn',
        'question_image',
        'option1',
        'option2',
        'option3',
        'option4',
        'option1_image',
        'option2_image',
        'option3_image',
        'option4_image',
        'answer1',
        'answer2',
        'answer3',
        'answer4',
        'explanation_text',
        'explanation_image',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_free' => 'boolean',
        'answer1' => 'boolean',
        'answer2' => 'boolean',
        'answer3' => 'boolean',
        'answer4' => 'boolean'
    ];


    protected static function boot()
    {
        parent::boot();

        static::created(function ($item) {
            try {
                // Create a corresponding ChapterQuizItem record with only the fields that exist in the table
                $quizItem = new ChapterQuizItem();
                $quizItem->organization_id = $item->organization_id;
                $quizItem->chapter_quiz_id = $item->chapter_quiz_id;
                $quizItem->chapter_quiz_question_id = $item->id;
                $quizItem->type = 'mcq';
                // Disable the OrganizationScopedTrait behavior for this save
                $quizItem->saveQuietly();
            } catch (\Exception $e) {
                // Log the error but don't prevent the question from being created
                \Log::error('Failed to create ChapterQuizItem: ' . $e->getMessage());
            }
        });
    }
}
