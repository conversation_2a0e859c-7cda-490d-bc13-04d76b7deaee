<?php

namespace App\Models;

use App\Http\Traits\OrganizationScopedTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class PackageType extends Model
{
    use HasFactory;
    use OrganizationScopedTrait;
    use SoftDeletes;

    protected $table = 'package_types';

    protected $fillable = [
        'created_by',
        'organization_id',
        'name',
        'price',
        'limit',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];
}
