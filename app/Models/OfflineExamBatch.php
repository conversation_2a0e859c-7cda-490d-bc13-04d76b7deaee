<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\BatchStudent;
use App\Models\OfflineExamStudents;

class OfflineExamBatch extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'course_id',
        'offline_exam_id',
        'batch_id',
    ];

    protected static function booted()
    {
        static::created(function ($offlineExamBatch) {
            // Fetch all students in the batch
            $students = BatchStudent::where('batch_id', $offlineExamBatch->batch_id)->get();

            $list = [];
            foreach ($students as $student) {
                $list[] = [
                    'course_id' => $offlineExamBatch->course_id,
                    'offline_exam_id' => $offlineExamBatch->offline_exam_id,
                    'student_id' => $student->student_id,
                    'batch_id' => $offlineExamBatch->batch_id,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];

            }
            OfflineExamStudents::insert($list);
        });
    }

    public function batch()
    {
        return $this->belongsTo(Batch::class);
    }
}

