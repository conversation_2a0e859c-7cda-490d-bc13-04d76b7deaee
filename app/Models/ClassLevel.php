<?php

namespace App\Models;

use App\Http\Traits\OrganizationScopedTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ClassLevel extends Model
{
    use HasFactory;
    use OrganizationScopedTrait;
    use SoftDeletes;

    protected $table = 'class_levels';

    protected $fillable = [
        'created_by',
        'organization_id',
        'name',
        'name_bn',
        'class_code',
        'price',
        'is_free',
        'icon',
        'color_code',
        'sequence',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_free' => 'boolean',
    ];
}
