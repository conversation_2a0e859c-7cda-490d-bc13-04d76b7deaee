<?php

namespace App\Models;

use App\Http\Traits\OrganizationScopedTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CorrectionRating extends Model
{
    use HasFactory;
    use OrganizationScopedTrait;
    use SoftDeletes;

    protected $table = 'correction_ratings';

    protected $fillable = [
        'created_by',
        'organization_id',
        'expert_id',
        'rating',
        'total_rating',
        'total_correction',
        'rating_avg',
    ];

    protected $casts = [];
}
