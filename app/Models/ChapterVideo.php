<?php

namespace App\Models;

use App\Http\Traits\CourseContentTrait;
use App\Http\Traits\OrganizationScopedTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ChapterVideo extends Model
{
    use CourseContentTrait;
    use HasFactory;
    use OrganizationScopedTrait;
    use SoftDeletes;

    protected $fillable = [
        'created_by',
        'organization_id',
        'title',
        'title_bn',
        'video_code',
        'course_id',
        'course_category_id',
        'class_level_id',
        'subject_id',
        'chapter_id',
        'video_code',
        'author_name',
        'author_details',
        'description',
        'raw_url',
        's3_url',
        'youtube_url',
        'download_url',
        'thumbnail',
        'duration',
        'price',
        'rating',
        'is_free',
        'sequence',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_free' => 'boolean',
    ];
}
