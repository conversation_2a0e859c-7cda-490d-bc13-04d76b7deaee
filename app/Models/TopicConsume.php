<?php

namespace App\Models;

use App\Http\Traits\OrganizationScopedTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class TopicConsume extends Model
{
    use HasFactory;
    use OrganizationScopedTrait;
    use SoftDeletes;

    protected $table = 'topic_consumes';

    protected $fillable = [
        'created_by',
        'organization_id',
        'payment_id',
        'user_id',
        'school_id',
        'package_id',
        'package_type_id',
        'balance',
        'consumme',
        'expiry_date',
        'is_fully_consumed',
    ];

    protected $casts = [
        'is_fully_consumed' => 'boolean',
    ];
}
