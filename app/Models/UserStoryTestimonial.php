<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserStoryTestimonial extends Model
{
    protected $fillable = [
        'user_story_id',
        'name',
        'image',
        'designation',
        'speech',
        'datetime',
        'is_active',
        'sort_order'
    ];

    protected $casts = [
        'datetime' => 'datetime',
        'is_active' => 'boolean',
        'sort_order' => 'integer'
    ];

    /**
     * Get the user story that owns the testimonial.
     */
    public function userStory(): BelongsTo
    {
        return $this->belongsTo(UserStory::class);
    }

    /**
     * Scope a query to only include active testimonials.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to order by sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order');
    }

    /**
     * Get the image URL attribute.
     */
    public function getImageUrlAttribute()
    {
        if ($this->image) {
            return asset('uploads/' . $this->image);
        }
        return null;
    }
}
