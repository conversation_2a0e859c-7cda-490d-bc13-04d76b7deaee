<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use App\Http\Traits\OrganizationScopedTrait;

class BlankAnswer extends Model
{
    use OrganizationScopedTrait;

    protected $fillable = [
        'fill_in_the_blank_question_id',
        'blank_key',
        'blank_answer',
    ];

    public function fillInTheBlankQuestion()
    {
        return $this->belongsTo(FillInTheBlankQuestion::class);
    }
}
