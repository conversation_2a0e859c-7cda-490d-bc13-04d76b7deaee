<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Http\Traits\OrganizationScopedTrait;

class PromotionalItem extends Model
{
    use SoftDeletes, OrganizationScopedTrait;

    protected $fillable = [
        'organization_id',
        'title',
        'title_bn',
        'description',
        'image',
        'type',
        'course_id',
        'chapter_video_id',
        'chapter_script_id',
        'chapter_quiz_id',
        'ebook_id',
        'youtube_url',
        'is_active',
        'created_by',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    public function organization()
    {
        return $this->belongsTo(Organization::class);
    }

    public function course()
    {
        return $this->belongsTo(Course::class);
    }

    public function chapterVideo()
    {
        return $this->belongsTo(ChapterVideo::class);
    }

    public function chapterScript()
    {
        return $this->belongsTo(ChapterScript::class);
    }

    public function chapterQuiz()
    {
        return $this->belongsTo(ChapterQuiz::class);
    }

    public function ebook()
    {
        return $this->belongsTo(Ebook::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }
}
