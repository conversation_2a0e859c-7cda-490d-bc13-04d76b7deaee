<?php

namespace App\Models;

use App\Http\Traits\OrganizationScopedTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ChapterQuizSubjectWiseResult extends Model
{
    use HasFactory;
    use OrganizationScopedTrait;
    use SoftDeletes;

    protected $fillable = [
        'created_by',
        'organization_id',
        'chapter_quiz_result_id',
        'chapter_quiz_id',
        'user_id',
        'quiz_core_subject_id',
        'positive_count',
        'negetive_count',
    ];

    protected $casts = [];
}
