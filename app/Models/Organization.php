<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Organization extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $table = 'organizations';

    protected $fillable = [
        'created_by',
        'name',
        'headline',
        'sub_headline',
        'promotional_video',
        'short_name',
        'details',
        'address',
        'email',
        'contact_no',
        'logo',
        'menu_position',
        'contact_person',
        'is_active',
        'banner',
        'contact_number',
        'hotline_number',
        'last_payment_id',
        'host_url',
        'asset_host',
        'color_theme',
        'template_id',
        'custom_student_number',
        'custom_course_number',
        'custom_user_number',
        'custom_book_number',
        'custom_instructor_number',
        'website',
        'facebook',
        'twitter',
        'linkedin',
        'youtube',
        'footer_logo',
        'organization_code',
        'currencies'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'currencies' => 'array',
    ];


    protected static function boot()
    {
        parent::boot();

        static::creating(function ($organization) {
            if (empty($organization->organization_code)) {
                $organization->organization_code = self::generateUniqueOrganizationCode($organization->name);
            }
        });
    }

    private static function generateUniqueOrganizationCode($name)
    {
        $prefix = strtoupper(substr(preg_replace('/[^A-Za-z]/', '', $name), 0, 1)); // first character (letter only)
        $code = null;

        do {
            $number = str_pad(rand(0, 99999), 5, '0', STR_PAD_LEFT);
            $code = $prefix . $number;
        } while (self::where('organization_code', $code)->exists());

        return $code;
    }

    public function admins () {
        return $this->hasMany(User::class, 'organization_id')->where('user_type', 'OrganizationAdmin');
    }

    public function websiteSettings()
    {
        return $this->hasMany(WebsiteSetting::class, 'organization_id');
    }

    public function template()
    {
        return $this->belongsTo(Template::class, 'template_id');
    }

    public function courses()
    {
        return $this->hasMany(Course::class, 'organization_id');
    }

    public function scripts()
    {
        return $this->hasMany(ChapterScript::class, 'organization_id');
    }

    public function videos()
    {
        return $this->hasMany(ChapterVideo::class, 'organization_id');
    }

    public function quizzes()
    {
        return $this->hasMany(ChapterQuiz::class, 'organization_id');
    }

    public function students()
    {
        return $this->hasMany(StudentInformation::class, 'organization_id');
    }

    public function mentors()
    {
        return $this->hasMany(MentorInformation::class, 'organization_id');
    }

    public function categories() {
        return $this->hasMany(Category::class, 'organization_id');
    }
    public function only_categories() {
        return $this->hasMany(Category::class, 'organization_id')->where('is_content', 0);
    }
    public function topCategory() {
        return $this->hasOne(Category::class, 'organization_id');
    }

    public function lastPayment () {
        return $this->hasOne(OrganizationPayment::class, 'id', 'last_payment_id');
    }

    public function all_payments () {
        return $this->hasMany(OrganizationPayment::class, 'organization_id')->orderBy('created_at', 'desc');
    }

    public function payments () {
        return $this->hasMany(Payment::class, 'organization_id');
    }


    public function activeCategoriesHavingCourses () {
        return $this->hasMany(Category::class, 'organization_id')->whereHas('courses', function($q) {
            $q->where('is_active', 1);
        })->where('is_active', 1);
    }

    public function promotionalItems () {
        return $this->hasMany(PromotionalItem::class, 'organization_id');
    }

    public function paymentGateways()
    {
        return $this->hasMany(OrganizationPaymentGateway::class);
    }

    /**
     * Get the primary currency for this organization.
     */
    public function getPrimaryCurrency()
    {
        if (!$this->currencies) {
            return null;
        }

        $primaryCurrency = collect($this->currencies)->firstWhere('is_primary', true);

        if ($primaryCurrency) {
            return Currency::find($primaryCurrency['currency_id']);
        }

        return null;
    }

    /**
     * Get all active currencies for this organization.
     */
    public function getActiveCurrencies()
    {
        if (!$this->currencies) {
            return collect();
        }

        $activeCurrencyIds = collect($this->currencies)
            ->where('is_active', true)
            ->pluck('currency_id');

        return Currency::whereIn('id', $activeCurrencyIds)->get();
    }

    /**
     * Check if organization has a specific currency.
     */
    public function hasCurrency($currencyId)
    {
        if (!$this->currencies) {
            return false;
        }

        return collect($this->currencies)->contains('currency_id', $currencyId);
    }

    /**
     * Add currency to organization.
     */
    public function addCurrency($currencyId, $isPrimary = false, $isActive = true, $customExchangeRate = null)
    {
        $currencies = $this->currencies ?? [];

        // If setting as primary, remove primary from others
        if ($isPrimary) {
            $currencies = array_map(function ($currency) {
                $currency['is_primary'] = false;
                return $currency;
            }, $currencies);
        }

        // Check if currency already exists
        $existingIndex = array_search($currencyId, array_column($currencies, 'currency_id'));

        if ($existingIndex !== false) {
            // Update existing currency
            $currencies[$existingIndex] = [
                'currency_id' => $currencyId,
                'is_primary' => $isPrimary,
                'is_active' => $isActive,
                'custom_exchange_rate' => $customExchangeRate,
                'assigned_at' => now()->toISOString()
            ];
        } else {
            // Add new currency
            $currencies[] = [
                'currency_id' => $currencyId,
                'is_primary' => $isPrimary,
                'is_active' => $isActive,
                'custom_exchange_rate' => $customExchangeRate,
                'assigned_at' => now()->toISOString()
            ];
        }

        $this->update(['currencies' => $currencies]);
        return $this;
    }

    /**
     * Remove currency from organization.
     */
    public function removeCurrency($currencyId)
    {
        if (!$this->currencies) {
            return $this;
        }

        $currencies = array_filter($this->currencies, function ($currency) use ($currencyId) {
            return $currency['currency_id'] != $currencyId;
        });

        $this->update(['currencies' => array_values($currencies)]);
        return $this;
    }

    /**
     * Get currency details with exchange rate.
     */
    public function getCurrencyDetails($currencyId)
    {
        if (!$this->currencies) {
            return null;
        }

        $currencyData = collect($this->currencies)->firstWhere('currency_id', $currencyId);

        if (!$currencyData) {
            return null;
        }

        $currency = Currency::find($currencyId);

        if (!$currency) {
            return null;
        }

        return [
            'currency' => $currency,
            'is_primary' => $currencyData['is_primary'] ?? false,
            'is_active' => $currencyData['is_active'] ?? true,
            'custom_exchange_rate' => $currencyData['custom_exchange_rate'] ?? null,
            'effective_exchange_rate' => $currencyData['custom_exchange_rate'] ?? $currency->exchange_rate,
            'assigned_at' => $currencyData['assigned_at'] ?? null
        ];
    }

}
