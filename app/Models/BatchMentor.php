<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class BatchMentor extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $fillable = [
        'batch_id',
        'mentor_id',
        'is_active',
        'added_by',
    ];


    public function mentors()
    {
        return $this->belongsTo(MentorInformation::class, 'mentor_id');
    }
}

