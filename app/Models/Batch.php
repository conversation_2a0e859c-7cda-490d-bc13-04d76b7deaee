<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Batch extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $fillable = [
        'name',
        'name_en',
        'description',
        'course_id',
        'organization_id',
        'capacity',
        'is_active',
        'start_date',
        'end_date',
        'color',
        'background_color',
        'image',
        'background_image',
        'status',
    ];

    public function course()
    {
        return $this->belongsTo(Course::class);
    }

    public function students()
    {
        return $this->hasMany(BatchStudent::class);
    }

    public function studentUsers()
    {
        return $this->hasMany(BatchStudent::class)
        ->join('student_informations', 'batch_students.student_id', '=', 'student_informations.id')
        ->select('batch_students.id', 'batch_students.batch_id', 'batch_students.student_id', 'student_informations.user_id');
    }

    public function studentList()
    {
        return $this->hasMany(BatchStudent::class)
            ->join('student_informations', 'batch_students.student_id', '=', 'student_informations.id')
            ->select('batch_students.id', 'batch_students.batch_id', 'batch_students.student_id', 'student_informations.name', 'student_informations.email', 'student_informations.contact_no', 'student_informations.image');
    }
    public function organization()
    {
        return $this->belongsTo(Organization::class);
    }

    public function mentors() {
        return $this->hasMany(BatchMentor::class);
    }

    public function mentorList() {
        return $this->hasMany(BatchMentor::class)
            ->join('mentor_informations', 'batch_mentors.mentor_id', '=', 'mentor_informations.id')
            ->select('batch_mentors.id', 'batch_mentors.batch_id', 'batch_mentors.mentor_id', 'mentor_informations.name', 'mentor_informations.email', 'mentor_informations.contact_no', 'mentor_informations.image');
    }

    public function classSchedules () {
        return $this->hasMany(ClassSchedule::class);
    }

}
