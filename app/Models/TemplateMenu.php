<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TemplateMenu extends Model
{
    use HasFactory;

    protected $fillable = [
        'template_id',
        'name',
        'link',
        'is_authentication_needed',
        'has_submenu',
        'is_course',
        'is_content',
        'icon',
        'headline',
        'description',
        'sequence',
        'is_active',
    ];

    protected $casts = [
        'is_authentication_needed' => 'boolean',
        'has_submenu' => 'boolean',
        'is_course' => 'boolean',
        'is_content' => 'boolean',
        'is_active' => 'boolean',
    ];
}

