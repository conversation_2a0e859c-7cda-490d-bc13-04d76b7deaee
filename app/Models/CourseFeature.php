<?php

namespace App\Models;

use App\Http\Traits\OrganizationScopedTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CourseFeature extends Model
{
    use HasFactory;
    use OrganizationScopedTrait;

    protected $fillable = [
        'organization_id',
        'title',
        'title_bn',
        'icon',
        'course_id',
        'created_by',
        'is_active',
    ];
}
