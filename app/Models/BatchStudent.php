<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Schema\Blueprint;

class BatchStudent extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected static function boot()
    {
        parent::boot();

        static::creating(function (Model $model) {
            $model->added_by = auth()->id();
        });

        static::deleting(function (Model $model) {
            $model->delete();
        });


    }

    protected $casts = [
        'is_active' => 'boolean'
    ];

    public function batch()
    {
        return $this->belongsTo(Batch::class);
    }
}

