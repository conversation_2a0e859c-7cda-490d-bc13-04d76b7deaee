<?php

namespace App\Models;

use App\Http\Traits\CourseContentTrait;
use App\Http\Traits\OrganizationScopedTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ChapterScript extends Model
{
    use CourseContentTrait;
    use HasFactory;
    use OrganizationScopedTrait;
    use SoftDeletes;

    protected $table = 'chapter_scripts';

    protected $fillable = [
        'created_by',
        'organization_id',
        'title',
        'title_bn',
        'description',
        'course_id',
        'course_category_id',
        'script_code',
        'class_level_id',
        'subject_id',
        'chapter_id',
        'raw_url',
        's3_url',
        'thumbnail',
        'price',
        'rating',
        'is_free',
        'sequence',
        'is_active',
    ];


    protected $casts = [
        'is_active' => 'boolean',
        'is_free' => 'boolean',
    ];


    public static function boot()
    {
        parent::boot();

        static::deleting(function ($script) {

            if (file_exists($script->raw_url)) {

                $urlArray = explode('/uploads/', $script->raw_url);
                if (count($urlArray) > 1) {
                    $raw_url = '/uploads/' . $urlArray[1];

                    $filePath = public_path($raw_url);

                    if (file_exists($filePath)) {
                        unlink($filePath);
                    }
                }
            }
        });
    }

    public function course()
    {
        return $this->belongsTo(Course::class, 'course_id');
    }
}
