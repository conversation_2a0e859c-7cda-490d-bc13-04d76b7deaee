<?php

namespace App\Models;

use App\Http\Traits\OrganizationScopedTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class PaymentDetail extends Model
{
    use HasFactory;
    use OrganizationScopedTrait;
    use SoftDeletes;

    protected $table = 'payment_details';

    protected $fillable = [
        'created_by',
        'organization_id',
        'coupon_id',
        'user_id',
        'payment_id',
        'item_id',
        'unit_price',
        'quantity',
        'total',
        'payment_method',
        'trx_id',
        'image',
        'deadline',
        'pay_for',
        'paid_amount',
        'payable_amount',
        'due_amount',
        'is_approved',
        'approved_by'
    ];

    protected $casts = [];


    public function payment()
    {
        return $this->belongsTo(Payment::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function course() {
        return $this->belongsTo(Course::class, 'item_id');
    }

    public function creator () {
        return $this->belongsTo(User::class, 'created_by');
    }
}

