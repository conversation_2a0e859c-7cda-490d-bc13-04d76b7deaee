<?php

namespace App\Models;

use App\Http\Traits\CourseContentTrait;
use App\Http\Traits\OrganizationScopedTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ChapterQuiz extends Model
{
    use CourseContentTrait;
    use HasFactory;
    use OrganizationScopedTrait;
    use SoftDeletes;

    protected $table = 'chapter_quizzes';

    protected $fillable = [
        'created_by',
        'organization_id',
        'title',
        'title_bn',
        'description',
        'quiz_code',
        'class_level_id',
        'quiz_type_id',
        'course_id',
        'course_category_id',
        'subject_id',
        'chapter_id',
        'duration',
        'positive_mark',
        'negative_mark',
        'total_mark',
        'number_of_question',
        'is_free',
        'sequence',
        'sufficient_question',
        'is_active',
        'quiz_attempts'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'sufficient_question' => 'boolean',
        'is_free' => 'boolean',
    ];


    public function writtenQuestions()
    {
        return $this->hasOne(ChapterQuizWrittenQuestion::class);
    }

    public function questions()
    {
        return $this->hasMany(ChapterQuizQuestion::class)->select([
            'id',
            'organization_id',
            'chapter_quiz_id',
            'question_text',
            'question_text_bn',
            'question_image',
            'option1',
            'option2',
            'option3',
            'option4',
            'option1_image',
            'option2_image',
            'option3_image',
            'option4_image',
            'answer1',
            'answer2',
            'answer3',
            'answer4',
            'explanation_text',
            'explanation_image',
        ]);
    }

    public function quizItems()
    {
        return $this->hasMany(ChapterQuizItem::class);
    }
}
