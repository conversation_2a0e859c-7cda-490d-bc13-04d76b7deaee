<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Schema\Blueprint;

class OrganizationPayment extends Model
{
    use HasFactory;

    protected $fillable = [
        'organization_id',
        'user_id',
        'package_id',
        'paid_amount',
        'discount_amount',
        'expiry_date',
        'is_trial_taken',
        'payment_method',
        'bkash_number',
        'bkash_transaction_id',
        'card_brand',
        'card_last_four',
        'currency',
        'tran_id',
        'success_url',
        'fail_url',
        'customer_name',
        'customer_email',
        'customer_phone',
    ];

    protected $casts = [
        'expiry_date' => 'datetime',
    ];
    
    public function package()
    {
        return $this->belongsTo(OrganizationPackage::class, 'package_id');
    }

}

