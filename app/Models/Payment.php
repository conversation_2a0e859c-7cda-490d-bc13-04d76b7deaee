<?php

namespace App\Models;

use App\Http\Traits\OrganizationScopedTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Payment extends Model
{
    use HasFactory;
    use OrganizationScopedTrait;
    use SoftDeletes;

    protected $table = 'payments';

    protected $fillable = [
        'coupon_id',
        'created_by',
        'organization_id',
        'user_id',
        'item_id',
        'item_type',
        'is_promo_applied',
        'promo_id',
        'payable_amount',
        'paid_amount',
        'discount_amount',
        'currency',
        'transaction_id',
        'payment_type',
        'payment_method',
        'status',
        'is_approved',
        'approved_by',
        'is_verified_payment',
    ];

    protected $casts = [
        'is_promo_applied' => 'boolean',
        'is_verified_payment' => 'boolean',
    ];

    public function course ()
    {
        return $this->belongsTo(Course::class, 'item_id');
    }

    public function createdBy ()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function organization () {

        return $this->belongsTo(Organization::class, 'organization_id');
    }

    public function user () {

        return $this->belongsTo(User::class, 'user_id');
    }

    public function student () {

        return $this->belongsTo(StudentInformation::class, 'user_id', 'user_id');
    }
    public function details () {
        return $this->hasMany(PaymentDetail::class, 'payment_id');
    }

}
