<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CourseRating extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'organization_id',
        'course_id',
        'user_id',
        'rating',
        'review',
        'is_active',
    ];


    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

}

