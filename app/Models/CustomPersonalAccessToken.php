<?php

namespace App\Models;

// use <PERSON><PERSON>\Sanctum\NewAccessToken as SanctumPersonalAccessToken;
use <PERSON><PERSON>\Sanctum\PersonalAccessToken as SanctumPersonalAccessToken;

// use Laravel\Sanctum\NewAccessToken;

class CustomPersonalAccessToken extends SanctumPersonalAccessToken
{
    protected $table = 'personal_access_tokens';
    protected $fillable = [
        'name',
        'token',
        'abilities',
        'expires_at',
        'ip',
        'device',
        'browser'
    ];

}
