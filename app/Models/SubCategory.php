<?php

namespace App\Models;

use App\Http\Traits\OrganizationScopedTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class SubCategory extends Model
{
    use HasFactory , OrganizationScopedTrait, SoftDeletes;

    protected $fillable = [
        'category_id',
        'organization_id',
        'name',
        'name_bn',
        'description',
        'link',
        'icon',
        'is_active',
        'sequence',
        'created_by',

    ];


    public function courses()
    {
        return $this->hasMany(Course::class);
    }
}
