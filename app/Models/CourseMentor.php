<?php

namespace App\Models;

use App\Http\Traits\OrganizationScopedTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CourseMentor extends Model
{
    use HasFactory;
    use OrganizationScopedTrait;
    use SoftDeletes;

    protected $fillable = [
        'created_by',
        'organization_id',
        'course_id',
        'mentor_id',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',

    ];

    public function course()
    {
        return $this->belongsTo(Course::class);
    }

    public function mentor()
    {
        return $this->belongsTo(MentorInformation::class);
    }

    public function mentors()
    {
        return $this->belongsToMany(MentorInformation::class, 'course_student_mentor_mapping', 'user_id', 'mentor_id')
            ->withPivot('course_id', 'is_active', 'organization_id');
    }
}
