<?php

namespace App\Models;

use App\Http\Traits\OrganizationScopedTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ChapterQuizResultAnswer extends Model
{
    use HasFactory;
    use OrganizationScopedTrait;
    use SoftDeletes;

    protected $fillable = [
        'created_by',
        'organization_id',
        'chapter_quiz_result_id',
        'question_id',
        'answer1',
        'answer2',
        'answer3',
        'answer4',
        'is_correct',
    ];

    protected $casts = [
        'is_correct' => 'boolean',
        'answer1'  => 'boolean',
        'answer2'  => 'boolean',
        'answer3'  => 'boolean',
        'answer4'  => 'boolean',
    ];
}
