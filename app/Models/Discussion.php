<?php

namespace App\Models;

use App\Http\Traits\OrganizationScopedTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Discussion extends Model
{
    use HasFactory;
    use OrganizationScopedTrait;

    protected $fillable = [
        'organization_id',
        'course_id',
        'user_id',
        'title',
        'content',
        'is_pinned',
        'is_active',
        'is_approved',
        'approved_by',
        'approved_at',
    ];

    protected $casts = [
        'is_pinned' => 'boolean',
        'is_active' => 'boolean',
        'is_approved' => 'boolean',
        'approved_at' => 'datetime',
    ];

    public function course()
    {
        return $this->belongsTo(Course::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function approvedBy()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    public function comments()
    {
        return $this->hasMany(DiscussionComment::class);
    }

    public function likes()
    {
        return $this->hasMany(DiscussionLike::class, 'discussion_id');
    }

    public function reports()
    {
        return $this->hasMany(DiscussionReport::class, 'discussion_id');
    }

    public function isLikedByUser($userId)
    {
        return $this->likes()->where('user_id', $userId)->exists();
    }

    public function isReportedByUser($userId)
    {
        return $this->reports()->where('user_id', $userId)->exists();
    }
}
