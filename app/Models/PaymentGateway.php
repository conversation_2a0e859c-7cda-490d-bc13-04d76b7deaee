<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PaymentGateway extends Model
{
    use HasFactory;

    protected $fillable = [
        'created_by',
        'name',
        'type',
        'logo',
        'short_description',
        'creadential_format',
        'is_active',
    ];
    protected $casts = [
        'is_active' => 'boolean',
    ];


    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }
}
