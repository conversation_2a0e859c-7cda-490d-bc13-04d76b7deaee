<?php

namespace App\Models;

use App\Http\Traits\OrganizationScopedTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class QuizCoreSubjects extends Model
{
    use HasFactory;
    use SoftDeletes;
    // use OrganizationScopedTrait;

    protected $fillable = [
        'created_by',
        'organization_id',
        'name',
        'name_bn',
        'is_optional',
        'optional_subject_id',
        'is_active',
    ];

    protected $casts = [
        'is_optional' => 'boolean',
        'is_active' => 'boolean',
    ];
}
