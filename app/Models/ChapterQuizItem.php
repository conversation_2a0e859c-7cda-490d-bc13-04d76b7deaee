<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Http\Traits\OrganizationScopedTrait;

class ChapterQuizItem extends Model
{
    use HasFactory;
    use OrganizationScopedTrait;

    protected $fillable = [
        'chapter_quiz_id',
        'chapter_quiz_question_id',
        'chapter_quiz_true_false_id',
        'chapter_quiz_matching_id',
        'chapter_quiz_fill_in_blank_id',
        'type',
    ];

    public function chapterQuiz()
    {
        return $this->belongsTo(ChapterQuiz::class);
    }

    public function chapterQuizQuestion()
    {
        return $this->belongsTo(ChapterQuizQuestion::class);
    }

    public function trueFalse()
    {
        return $this->belongsTo(ChapterTrueFalseQuestion::class, 'chapter_quiz_true_false_id');
    }

    public function matching()
    {
        return $this->belongsTo(MatchingQuestion::class, 'chapter_quiz_matching_id');
    }


    public function fillInTheBlank()
    {
        return $this->belongsTo(FillInTheBlankQuestion::class, 'chapter_quiz_fill_in_blank_id');
    }
    // Add other relationships as needed for true_false, matching, and fill_in_blank
}
