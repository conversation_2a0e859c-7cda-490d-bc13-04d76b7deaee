<?php

namespace App\Models;

use App\Http\Traits\OrganizationScopedTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Content extends Model
{
    use HasFactory;
    use OrganizationScopedTrait;
    use SoftDeletes;

    protected $fillable = [
        'created_by',
        'organization_id',
        'title',
        'title_bn',
        'category_id',
        'gp_product_id',
        'youtube_url',
        'description',
        'thumbnail',
        'icon',
        'number_of_enrolled',
        'regular_price',
        'sale_price',
        'discount_percentage',
        'rating',
        'is_active',
        'is_free',
        'sequence',
        'appeared_from',
        'appeared_to',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_free' => 'boolean',
    ];

    public function contentOutline()
    {
        return $this->hasMany(ContentOutline::class);
    }

    public function category()
    {
        return $this->belongsTo(Category::class, 'category_id');
    }

    public function subjects()
    {
        return $this->hasMany(ContentSubject::class);
    }
}
