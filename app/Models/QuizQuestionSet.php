<?php

namespace App\Models;

use App\Http\Traits\OrganizationScopedTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class QuizQuestionSet extends Model
{
    use HasFactory;
    use SoftDeletes;
    // use OrganizationScopedTrait;

    protected $fillable = [
        'organization_id',
        'name',
        'created_by',
    ];
}
