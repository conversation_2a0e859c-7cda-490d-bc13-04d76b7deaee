<?php

namespace App\Models;

use App\Http\Traits\OrganizationScopedTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Subject extends Model
{
    use HasFactory;
    use OrganizationScopedTrait;
    use SoftDeletes;

    protected $table = 'subjects';

    protected $fillable = [
        'created_by',
        'organization_id',
        'name',
        'name_bn',
        'subject_code',
        'course_id',
        'class_level_id',
        'price',
        'is_free',
        'icon',
        'color_code',
        'sequence',
        'is_active',
    ];
    protected $casts = [
        'is_active' => 'boolean',
        'is_free' => 'boolean',
    ];

    public function outlines()
    {
        return $this->hasMany(CourseOutline::class, 'subject_id');
    }

    public function courseCategory()
    {
        return $this->hasMany(CourseCategory::class, 'subject_id');
    }
}
