<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class PaymentTypeOrganization extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'organization_id',
        'name',
        'icon',
    ];


    public function organization()
    {
        return $this->belongsTo(Organization::class);
    }


    public function items()
    {
        return $this->hasMany(PaymentTypeOrganizationItem::class);
    }
}

