<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class PaymentTypeOrganizationItem extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'payment_type_organization_id',
        'field_name',
        'field_value',
        'image',
    ];

    public function paymentType()
    {
        return $this->belongsTo(PaymentTypeOrganization::class);
    }
}

