<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use App\Http\Traits\OrganizationScopedTrait;

class FillInTheBlankQuestion extends Model
{
    use OrganizationScopedTrait;

    protected $fillable = [
        'chapter_quiz_id',
        'question_text',
        'question_image',
        'explanation_text',
        'explanation_image',
        'is_active',
        'question_type'
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    protected static function boot()
    {
        parent::boot();

        static::created(function ($item) {
            try {

                $quizItem = new ChapterQuizItem();
                $quizItem->organization_id = $item->organization_id;
                $quizItem->chapter_quiz_id = $item->chapter_quiz_id;
                $quizItem->chapter_quiz_fill_in_blank_id = $item->id;
                $quizItem->type = 'fill_in_blank';
                $quizItem->saveQuietly();

            } catch (\Exception $e) {
                // Log the error but don't prevent the question from being created
                \Log::error('Failed to create ChapterQuizItem: ' . $e->getMessage());
            }
        });
    }

    public function chapterQuiz()
    {
        return $this->belongsTo(ChapterQuiz::class);
    }

    public function blankAnswers()
    {
        return $this->hasMany(BlankAnswer::class);
    }
}
