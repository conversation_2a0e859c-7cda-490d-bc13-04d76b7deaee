<?php

namespace App\Models;

use App\Http\Traits\OrganizationScopedTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CourseParticipant extends Model
{
    use HasFactory;
    use OrganizationScopedTrait;
    use SoftDeletes;

    protected $fillable = [
        'created_by',
        'organization_id',
        'user_id',
        'item_id',
        'item_price',
        'paid_amount',
        'payment_id',
        'discount',
        'item_type',
        'is_trial_taken',
        'trial_expiry_date',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];


    protected static function booted()
    {
        static::created(function ($participant) {
            $student = \App\Models\StudentInformation::where('user_id', $participant->user_id)->first();


                \App\Models\CourseStudent::create([
                    'course_id' => $participant->item_id,
                    'student_id' => $student->id,
                    'enrolled_date' => now(),
                    'quiz_participation_count' => 0,
                    'video_watch_count' => 0,
                    'script_watch_count' => 0,
                    'assignment_submission_count' => 0,
                    'class_attendance_count' => 0,
                    'total_paid_amount' => $participant->paid_amount ?? 0,
                ]);

        });
    }
}
