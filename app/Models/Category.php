<?php

namespace App\Models;

use App\Http\Traits\OrganizationScopedTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;
class Category extends Model
{
    use HasFactory;
    use OrganizationScopedTrait;
    use SoftDeletes;

    protected $table = 'categories';

    protected $fillable = [
        'created_by',
        'organization_id',
        'name',
        'link',
        'headline',
        'description',
        'sequence',
        'is_authentication_needed',
        'has_submenu',
        'is_course',
        'is_content',
        'is_custom_page',
        'is_footer',
        'is_auth_footer',
        'icon',
        'slug',
        'sequence',
        'is_active',
    ];

    protected $casts = [
        'is_authentication_needed' => 'boolean',
        'has_submenu' => 'boolean',
        'is_course' => 'boolean',
        'is_content' => 'boolean',
        'is_active' => 'boolean',
        'is_custom_page' => 'boolean',
        'is_footer' => 'boolean',
        'is_auth_footer' => 'boolean',
    ];



    protected static function boot()
    {
        parent::boot();

        static::creating(function ($category) {
            $slug = Str::slug($category->name);
            $originalSlug = $slug;
            $count = 1;

            while (Category::where('slug', $slug)->where('organization_id', $category->organization_id)->exists()) {
                $slug = "{$originalSlug}-{$count}";
                $count++;
            }

            $category->slug = $slug;
        });
    }
    public function courses()
    {
        return $this->hasMany(Course::class);
    }

    public function contents()
    {
        return $this->hasMany(Content::class);
    }

    public function subCategories()
    {
        return $this->hasMany(SubCategory::class);
    }

    public function coursesWithOutSubCategories()
    {
        return $this->hasMany(Course::class)->where('sub_category_id', null);
    }

    public function categoryItems()
    {
        return $this->hasMany(CategoryItem::class);
    }


}
