<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class UserStory extends Model
{
    protected $fillable = [
        'title',
        'short_description',
        'color',
        'background',
        'is_active',
        'sort_order'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'sort_order' => 'integer'
    ];

    /**
     * Get the testimonials for the user story.
     */
    public function testimonials(): HasMany
    {
        return $this->hasMany(UserStoryTestimonial::class)->orderBy('sort_order');
    }

    /**
     * Get active testimonials for the user story.
     */
    public function activeTestimonials(): Has<PERSON>any
    {
        return $this->hasMany(UserStoryTestimonial::class)
            ->where('is_active', true)
            ->orderBy('sort_order');
    }

    /**
     * Scope a query to only include active user stories.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to order by sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order');
    }
}
