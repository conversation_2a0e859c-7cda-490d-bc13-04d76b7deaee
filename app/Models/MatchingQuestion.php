<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use App\Http\Traits\OrganizationScopedTrait;
use App\Models\ChapterQuizItem;

class MatchingQuestion extends Model
{
    use OrganizationScopedTrait;

    protected $fillable = [
        'chapter_quiz_id',
        'question_text',
        'explanation_text',
        'explanation_image',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    protected static function boot()
    {
        parent::boot();

        static::created(function ($item) {
            try {

                $quizItem = new ChapterQuizItem();
                $quizItem->organization_id = $item->organization_id;
                $quizItem->chapter_quiz_id = $item->chapter_quiz_id;
                $quizItem->chapter_quiz_matching_id = $item->id;
                $quizItem->type = 'matching';
                $quizItem->saveQuietly();
            } catch (\Exception $e) {

                \Log::error('Failed to create ChapterQuizItem: ' . $e->getMessage());
            }
        });
    }

    public function chapterQuiz()
    {
        return $this->belongsTo(ChapterQuiz::class);
    }

    public function matchingAnswers()
    {
        return $this->hasMany(MatchingAnswer::class);
    }
    public function leftItems()
    {
        return $this->matchingAnswers()->select('id', 'left_item')->get();
    }
    public function randomRightItems()
    {
        return $this->matchingAnswers()->inRandomOrder()->pluck('right_item');
    }
}
