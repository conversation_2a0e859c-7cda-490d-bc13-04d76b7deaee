<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OrganizationPaymentGateway extends Model
{
    use HasFactory;

    protected $fillable = [
        'organization_id',
        'payment_gateway_id',
        'credentials',
        'is_active',
    ];

    protected $casts = [
        'credentials' => 'array',
    ];

    public function organization()
    {
        return $this->belongsTo(Organization::class);
    }

    public function paymentGateway()
    {
        return $this->belongsTo(PaymentGateway::class, 'payment_gateway_id');
    }
}
