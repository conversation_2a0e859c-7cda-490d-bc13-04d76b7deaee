<?php

namespace App\Models;

use App\Http\Traits\OrganizationScopedTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Assignment extends Model
{
    use HasFactory;
    use OrganizationScopedTrait;
    use SoftDeletes;

    protected $fillable = [
        'organization_id',
        'mentor_id',
        'course_id',
        'batch_id',
        'title',
        'title_bn',
        'mark',
        'pass_mark',
        'total_time',
        'instructions',
        'description',
        'supporting_doc',
        'publish_date',
        'deadline',
        'status',
        'created_by',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    public function studentAssignments()
    {
        return $this->hasMany(StudentAssignment::class, 'assignment_id', 'id');
    }

    public function students()
    {
        return $this->hasMany(StudentAssignment::class, 'assignment_id', 'id')
            ->leftJoin('student_informations', 'student_assignments.student_id', '=', 'student_informations.id')
            // ->leftJoin('assignment_submissions', 'student_assignments.student_id', '=', 'assignment_submissions.student_id')
            ->select('student_assignments.*', 'student_informations.name', 'student_informations.image');
    }

    public function course()
    {
        return $this->belongsTo(Course::class, 'course_id', 'id');
    }


    public function assignmentSubmission()
    {
        return $this->hasOne(AssignmentSubmission::class, 'assignment_id', 'id')
        ->leftJoin('users', 'assignment_submissions.evaluated_by', 'users.id')
        ->select('assignment_submissions.*', 'users.name', 'users.image')->orderBy('assignment_submissions.id', 'desc');
    }

    public function mentor()
    {
        return $this->belongsTo(MentorInformation::class, 'mentor_id', 'id');
    }
    // public function assignmentSubmission()
    // {
    //     return $this->assignmentSubmission()
    //         ->leftJoin('users', 'assignment_submissions.evaluated_by', '=', 'users.id')
    //         ->select('assignment_submissions.*, users.name, users.image');
    // }
}
