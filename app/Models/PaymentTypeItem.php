<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class PaymentTypeItem extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'payment_type_id',
        'field_name',
    ];

    public function paymentType()
    {
        return $this->belongsTo(PaymentType::class);
    }
}

