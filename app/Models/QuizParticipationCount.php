<?php

namespace App\Models;

use App\Http\Traits\OrganizationScopedTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class QuizParticipationCount extends Model
{
    use HasFactory;
    use OrganizationScopedTrait;
    use SoftDeletes;

    protected $fillable = [
        'created_by',
        'organization_id',
        'course_id',
        'chapter_quiz_id',
        'user_id',
        'number_of_participation',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];
}
