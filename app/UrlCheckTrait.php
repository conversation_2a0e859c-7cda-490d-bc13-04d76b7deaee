<?php

namespace App;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;


trait UrlCheckTrait
{
    function urlExists($url)
    {
        $client = new Client();

        try {
            $response = $client->head($url);
            return $response->getStatusCode() === 200; // Return true if status is 200
        } catch (RequestException $e) {
            if ($e->hasResponse() && $e->getResponse()->getStatusCode() === 404) {
                // If it's a 404 error, the resource doesn't exist
                return false;
            }
            // Re-throw the exception for other types of errors
            throw $e;
        }
    }
}
