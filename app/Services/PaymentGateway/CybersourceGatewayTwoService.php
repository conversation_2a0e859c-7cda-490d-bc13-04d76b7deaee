<?php
namespace App\Services\PaymentGateway;

use Illuminate\Support\Facades\Http;
use Carbon\Carbon;
use App\Models\OrganizationPaymentGateway;

class CybersourceGatewayTwoService
{
    public function getCredentials($organizationId)
    {
        return OrganizationPaymentGateway::where('organization_id', $organizationId)->where('payment_gateway_id', 3)->firstOrFail();
    }

    public function getCaptureContext($organizationId)
    {
        $creds = $this->getCredentials($organizationId)->credentials;

        $payload = [
            "clientVersion" => "0.15",
            "targetOrigins" => ["http://localhost:4001"], // Replace with your frontend domain
            "allowedCardNetworks" => ["VISA", "MASTERCARD", "AMEX", "DISCOVER"],
            "allowedPaymentTypes" => ["PANENTRY"],
            "country" => "US",
            "locale" => "en_US",
            "clientLibrary" => [
                "name" => "cybersource-flex-microform",
                "version" => "0.15"
            ]
        ];

        $authToken = $this->generateAuthToken($creds, $payload);

        $response = Http::withHeaders([
            'Authorization' => $authToken,
            'Date' => now()->toRfc7231String(),
            'Content-Type' => 'application/json',
        ])->post("https://flex.cybersource.com/flex/v2/capture-contexts", $payload);

        if ($response->successful()) {
            return $response->json();
        }

        // Log the detailed error for debugging
        \Log::error('CyberSource Capture Context Failed', [
            'status' => $response->status(),
            'response' => $response->body(),
            'headers' => $response->headers(),
            'payload' => $payload,
            'auth_token' => $authToken
        ]);

        throw new \Exception("Failed to generate capture context: " . $response->status() . " - " . $response->body());
    }

    public function generateAuthToken($creds, $payload = [])
    {
        $timestamp = now()->toRfc7231String();
        $host = 'flex.cybersource.com';
        $target = '/flex/v2/capture-contexts';
        $payloadJson = json_encode($payload);
        $digest = base64_encode(hash('sha256', $payloadJson, true));

        $signatureString = <<<SIGNATURE
        host: $host
        date: $timestamp
        (request-target): post $target
        digest: SHA-256=$digest
        SIGNATURE;

        $signature = base64_encode(hash_hmac('sha256', $signatureString, base64_decode($creds['shared_secret_key']), true));

        return "Signature keyid=\"{$creds['key']}\", algorithm=\"HmacSHA256\", headers=\"host date (request-target) digest\", signature=\"$signature\"";
    }

    public function processCardToken($organizationId, $token, $amount, $currency = 'USD')
    {
        $creds = $this->getCredentials($organizationId)->credentials;

        $payload = [
            "clientReferenceInformation" => [
                "code" => "payment"
            ],
            "processingInformation" => [
                "commerceIndicator" => "internet"
            ],
            "orderInformation" => [
                "amountDetails" => [
                    "totalAmount" => number_format($amount, 2, '.', ''),
                    "currency" => $currency
                ],
                "billTo" => [
                    "firstName" => "John",
                    "lastName" => "Doe",
                    "address1" => "1 Market St",
                    "locality" => "San Francisco",
                    "administrativeArea" => "CA",
                    "postalCode" => "94105",
                    "country" => "US",
                    "email" => "<EMAIL>"
                ]
            ],
            "paymentInformation" => [
                "tokenizedCard" => [
                    "number" => $token, // this is token.token from frontend
                ]
            ]
        ];

        $authToken = $this->generateAuthToken($creds);

        $response = Http::withHeaders([
            'Authorization' => $authToken,
            'Date' => now()->toRfc7231String(),
            'Content-Type' => 'application/json',
        ])->post('https://apitest.cybersource.com/pts/v2/payments', $payload);

        return $response->json();
    }
}
