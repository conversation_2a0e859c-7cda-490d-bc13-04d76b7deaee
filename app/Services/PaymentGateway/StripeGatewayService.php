<?php

namespace App\Services\PaymentGateway;
use \Stripe\Stripe;
use \Stripe\PaymentIntent;
use \Stripe\PaymentMethod;
use \Stripe\Exception\CardException;
use \Stripe\Exception\ApiErrorException;

class StripeGatewayService
{
    protected $credentials;

    public function __construct(array $credentials)
    {
        $this->credentials = $credentials;
    }

    public function process($request)
    {
        return $this->createPaymentIntent($request);
    }

    public function createPaymentIntent($request)
    {
        Stripe::setApiKey($this->credentials['secret_key']);
        $paymentIntent = PaymentIntent::create([
            'amount' => $request->input('amount', 1000) * 100, // Default to 1000 if not provided
            'currency' => $request->input('currency', 'usd'),
            "payment_method_types"=> ['card'],
            // "automatic_payment_methods" => [
            //     "enabled"=> true,
            //     "allow_redirects" => "never"
            // ],
        ]);
        return $paymentIntent;
    }

    /**
     * Create a payment method with card details
     *
     * @param array $cardDetails
     * @return \Stripe\PaymentMethod
     */
    public function createPaymentMethod($cardDetails)
    {
        Stripe::setApiKey($this->credentials['secret_key']);

        return PaymentMethod::create([
            'type' => 'card',
            'card' => [
                'number' => $cardDetails['card_number'],
                'exp_month' => $cardDetails['exp_month'],
                'exp_year' => $cardDetails['exp_year'],
                'cvc' => $cardDetails['cvc'],
            ],
        ]);
    }

    /**
     * Process a payment with card details (DEPRECATED - Use processPaymentWithPaymentMethod instead)
     *
     * @param array $paymentData
     * @return array
     */
    public function processCardPayment($paymentData)
    {
        Stripe::setApiKey($this->credentials['secret_key']);

        try {
            // Create a payment method with the card details
            $paymentMethod = $this->createPaymentMethod([
                'card_number' => $paymentData['card_number'],
                'exp_month' => $paymentData['exp_month'],
                'exp_year' => $paymentData['exp_year'],
                'cvc' => $paymentData['cvc'],
            ]);

            // Create a payment intent
            $paymentIntent = PaymentIntent::create([
                'amount' => $paymentData['amount'] * 100, // Convert to cents
                'currency' => $paymentData['currency'] ?? 'usd',
                'payment_method' => $paymentMethod->id,
                'confirm' => true,
                'return_url' => $paymentData['return_url'] ?? null,
                'description' => $paymentData['description'] ?? null,
                'metadata' => $paymentData['metadata'] ?? [],
                "automatic_payment_methods" => [
                    "enabled"=> true,
                    "allow_redirects" => "never"
                ],
            ]);

            // Return payment details
            return [
                'success' => true,
                'payment_intent' => $paymentIntent,
                'payment_method' => $paymentMethod,
                'card_brand' => $paymentMethod->card->brand,
                'card_last_four' => $paymentMethod->card->last4,
            ];
        } catch (CardException $e) {
            // Card was declined
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'error_code' => $e->getStripeCode(),
                'decline_code' => $e->getDeclineCode(),
            ];
        } catch (ApiErrorException $e) {
            // Other Stripe API errors
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'error_code' => $e->getStripeCode(),
            ];
        } catch (\Exception $e) {
            // Generic error
            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Process a payment with a payment method ID (created on the client side)
     *
     * @param array $paymentData
     * @return array
     */
    public function processPaymentWithPaymentMethod($paymentData)
    {
        Stripe::setApiKey($this->credentials['secret_key']);

        try {
            // Check if payment method ID is provided
            if (isset($paymentData['payment_method_id'])) {
                // Create a payment intent with the payment method ID
                $paymentIntent = PaymentIntent::create([
                    'amount' => $paymentData['amount'] * 100, // Convert to cents
                    'currency' => $paymentData['currency'] ?? 'usd',
                    'payment_method' => $paymentData['payment_method_id'],
                    'confirm' => true,
                    'return_url' => $paymentData['return_url'] ?? null,
                    'description' => $paymentData['description'] ?? null,
                    'metadata' => $paymentData['metadata'] ?? [],
                    "automatic_payment_methods" => [
                        "enabled"=> true,
                        "allow_redirects" => "never"
                    ],
                ]);

                // Retrieve the payment method to get card details
                $paymentMethod = \Stripe\PaymentMethod::retrieve($paymentData['payment_method_id']);

                // Return payment details
                return [
                    'success' => true,
                    'payment_intent' => $paymentIntent,
                    'payment_method' => $paymentMethod,
                    'card_brand' => $paymentMethod->card->brand,
                    'card_last_four' => $paymentMethod->card->last4,
                ];
            } else {
                // Create a payment intent without a payment method
                $paymentIntent = PaymentIntent::create([
                    'amount' => $paymentData['amount'] * 100, // Convert to cents
                    'currency' => $paymentData['currency'] ?? 'usd',
                    'description' => $paymentData['description'] ?? null,
                    'metadata' => $paymentData['metadata'] ?? [],
                    'payment_method_types' => ['card'], // Allow card payments
                    "automatic_payment_methods" => [
                        "enabled"=> true,
                        "allow_redirects" => "never"
                    ],
                ]);

                // Return payment details
                return [
                    'success' => true,
                    'payment_intent' => $paymentIntent,
                    'client_secret' => $paymentIntent->client_secret,
                ];
            }
        } catch (CardException $e) {
            // Card was declined
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'error_code' => $e->getStripeCode(),
                'decline_code' => $e->getDeclineCode(),
            ];
        } catch (ApiErrorException $e) {
            // Other Stripe API errors
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'error_code' => $e->getStripeCode(),
            ];
        } catch (\Exception $e) {
            // Generic error
            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Create a payment intent without a payment method
     *
     * @param array $paymentData
     * @return array
     */
    public function createEmptyPaymentIntent($paymentData)
    {
        Stripe::setApiKey($this->credentials['secret_key']);

        try {
            // Create a payment intent without a payment method
            $paymentIntent = PaymentIntent::create([
                'amount' => $paymentData['amount'] * 100, // Convert to cents
                'currency' => $paymentData['currency'] ?? 'usd',
                'description' => $paymentData['description'] ?? null,
                'metadata' => $paymentData['metadata'] ?? [],
                'payment_method_types' => ['card'], // Allow card payments
                "automatic_payment_methods" => [
                    "enabled"=> true,
                    "allow_redirects" => "never"
                ],
            ]);

            // Return payment intent details
            return [
                'success' => true,
                'payment_intent' => $paymentIntent,
                'client_secret' => $paymentIntent->client_secret,
            ];
        } catch (ApiErrorException $e) {
            // Stripe API errors
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'error_code' => $e->getStripeCode(),
            ];
        } catch (\Exception $e) {
            // Generic error
            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Verify a payment using the payment intent ID
     *
     * @param string $paymentIntentId
     * @return array
     */
    public function verify($paymentIntentId)
    {
        Stripe::setApiKey($this->credentials['secret_key']);

        try {
            $paymentIntent = PaymentIntent::retrieve($paymentIntentId);

            return [
                'success' => true,
                'payment_intent' => $paymentIntent,
                'status' => $paymentIntent->status,
                'is_paid' => $paymentIntent->status === 'succeeded',
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }
}
