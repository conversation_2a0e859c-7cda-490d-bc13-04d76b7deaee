<?php

namespace App\Services\PaymentGateway;

use CyberSource\ApiClient;
use CyberSource\Configuration;
use CyberSource\Api\PaymentsApi;
use CyberSource\Api\TransactionDetailsApi;
use CyberSource\Model\CreatePaymentRequest;
use CyberSource\Model\Ptsv2paymentsClientReferenceInformation;
use CyberSource\Model\Ptsv2paymentsProcessingInformation;
use CyberSource\Model\Ptsv2paymentsOrderInformation;
use CyberSource\Model\Ptsv2paymentsOrderInformationAmountDetails;
use CyberSource\Model\Ptsv2paymentsOrderInformationBillTo;
use CyberSource\Model\Ptsv2paymentsPaymentInformation;
use CyberSource\Model\Ptsv2paymentsPaymentInformationCard;
use CyberSource\Model\Ptsv2paymentsPaymentInformationFluidData;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;
use GuzzleHttp\Client;

class CybersourceGatewayService
{
    protected $apiClient;
    protected $paymentsApi;
    protected $transactionDetailsApi;
    protected $credentials;
    protected $merchantId;
    protected $apiKey;
    protected $sharedSecret;
    protected $runEnvironment;

    public function __construct(array $credentials)
    {
        $this->credentials = $credentials;

        // Log credentials for debugging (remove sensitive data)
        Log::info('CyberSource credentials received:', [
            'keys' => array_keys($credentials),
            'merchant_id_exists' => isset($credentials['organization_id']) || isset($credentials['merchant_id']),
            'api_key_exists' => isset($credentials['key']) || isset($credentials['api_key']),
            'shared_secret_exists' => isset($credentials['shared_secret_key']) || isset($credentials['shared_secret']),
        ]);

        // Handle different credential formats
        $merchantId = $credentials['organization_id'] ?? $credentials['merchant_id'] ?? null;
        $apiKey = $credentials['key'] ?? $credentials['api_key'] ?? null;
        $sharedSecret = $credentials['shared_secret_key'] ?? $credentials['shared_secret'] ?? null;
        $runEnvironment = $credentials['run_environment'] ?? 'apitest.cybersource.com';

        if (!$merchantId || !$apiKey || !$sharedSecret) {
            Log::error('Missing CyberSource credentials:', [
                'merchantId' => $merchantId ? 'present' : 'missing',
                'apiKey' => $apiKey ? 'present' : 'missing',
                'sharedSecret' => $sharedSecret ? 'present' : 'missing',
            ]);
            throw new \InvalidArgumentException('Missing required CyberSource credentials');
        }

        // Store credentials for later use
        $this->merchantId = $merchantId;
        $this->apiKey = $apiKey;
        $this->sharedSecret = $sharedSecret;
        $this->runEnvironment = $runEnvironment;

        try {
            // Create merchant configuration using CyberSource's approach
            $merchantConfig = new \CyberSource\Authentication\Core\MerchantConfiguration();
            $merchantConfig->setAuthenticationType('HTTP_SIGNATURE');
            $merchantConfig->setMerchantID($merchantId);
            $merchantConfig->setApiKeyID($apiKey);
            $merchantConfig->setSecretKey($sharedSecret);
            $merchantConfig->setRunEnvironment($runEnvironment);

            // Validate merchant configuration
            $merchantConfig->validateMerchantData();

            // Create configuration
            $config = new Configuration();
            $config->setHost($merchantConfig->getHost());

            // Create API client with merchant configuration
            $this->apiClient = new ApiClient($config, $merchantConfig);
            $this->paymentsApi = new PaymentsApi($this->apiClient);
            $this->transactionDetailsApi = new TransactionDetailsApi($this->apiClient);

        } catch (\Exception $e) {
            Log::error('CyberSource configuration error: ' . $e->getMessage());
            throw new \InvalidArgumentException('CyberSource configuration failed: ' . $e->getMessage());
        }
    }

    public function process($request)
    {
        return $this->createPaymentIntent($request);
    }

    /**
     * Test the CyberSource connection and configuration
     */
    public function testConnection()
    {
        try {
            // Create a minimal test payment request
            $transactionId = 'test_' . Str::random(16);

            $clientRefInfo = new Ptsv2paymentsClientReferenceInformation([
                'code' => $transactionId
            ]);

            $amountDetails = new Ptsv2paymentsOrderInformationAmountDetails([
                'totalAmount' => '1.00',
                'currency' => 'USD'
            ]);

            $orderInfo = new Ptsv2paymentsOrderInformation([
                'amountDetails' => $amountDetails
            ]);

            $paymentRequest = new CreatePaymentRequest([
                'clientReferenceInformation' => $clientRefInfo,
                'orderInformation' => $orderInfo
            ]);

            // This should fail with authentication error if credentials are wrong
            // or succeed if credentials are correct
            $response = $this->paymentsApi->createPayment($paymentRequest);

            return [
                'success' => true,
                'message' => 'CyberSource connection successful',
                'response' => $response
            ];
        } catch (\CyberSource\ApiException $e) {
            Log::error('CyberSource connection test failed:', [
                'message' => $e->getMessage(),
                'code' => $e->getCode(),
                'response_body' => json_encode($e->getResponseBody())
            ]);

            return [
                'success' => false,
                'message' => 'CyberSource connection failed',
                'error' => $e->getMessage(),
                'code' => $e->getCode(),
                'response_body' => $e->getResponseBody()
            ];
        } catch (\Exception $e) {
            Log::error('CyberSource connection test error:', [
                'message' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Connection test failed',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Create a payment intent (returns configuration for frontend)
     * CyberSource doesn't have a separate payment intent API like Stripe,
     * so we return the configuration needed for the frontend to make the payment
     */
    public function createPaymentIntent($request)
    {
        // For CyberSource, we don't create a server-side intent
        // Instead, we return the configuration needed for the payment
        $transactionId = 'cybersource_' . Str::random(16);

        return [
            'success' => true,
            'payment_intent' => [
                'id' => $transactionId,
                'amount' => $request->input('amount'),
                'currency' => $request->input('currency', 'USD'),
                'status' => 'requires_payment_method'
            ],
            'transaction_id' => $transactionId,
            'status' => 'requires_payment_method',
            'client_secret' => $transactionId, // Use transaction ID as client secret
            'merchant_id' => $this->merchantId,
            'environment' => $this->runEnvironment
        ];
    }

    /**
     * Process payment with card details
     */
    public function processCardPayment(array $paymentData)
    {
        $transactionId = 'cybersource_' . Str::random(16);

        try {
            $clientRefInfo = new Ptsv2paymentsClientReferenceInformation([
                'code' => $transactionId
            ]);

            $processingInfo = new Ptsv2paymentsProcessingInformation([
                'capture' => true // Capture immediately
            ]);

            $amountDetails = new Ptsv2paymentsOrderInformationAmountDetails([
                'totalAmount' => number_format($paymentData['amount'], 2, '.', ''),
                'currency' => $paymentData['currency'] ?? 'USD'
            ]);

            $orderInfo = new Ptsv2paymentsOrderInformation([
                'amountDetails' => $amountDetails
            ]);

            // Add billing information if available
            if (isset($paymentData['customer_name']) || isset($paymentData['customer_email'])) {
                $billTo = new Ptsv2paymentsOrderInformationBillTo([
                    'firstName' => $paymentData['customer_name'] ?? 'Customer',
                    'lastName' => '',
                    'email' => $paymentData['customer_email'] ?? null,
                ]);
                $orderInfo->setBillTo($billTo);
            }

            // Card information
            $card = new Ptsv2paymentsPaymentInformationCard([
                'number' => $paymentData['card_number'],
                'expirationMonth' => str_pad($paymentData['exp_month'], 2, '0', STR_PAD_LEFT),
                'expirationYear' => $paymentData['exp_year'],
                'securityCode' => $paymentData['cvc']
            ]);

            $paymentInfo = new Ptsv2paymentsPaymentInformation([
                'card' => $card
            ]);

            $paymentRequest = new CreatePaymentRequest([
                'clientReferenceInformation' => $clientRefInfo,
                'processingInformation' => $processingInfo,
                'orderInformation' => $orderInfo,
                'paymentInformation' => $paymentInfo
            ]);

            $response = $this->paymentsApi->createPayment($paymentRequest);

            // Extract card details for response
            $cardBrand = null;
            $cardLastFour = null;
            if (isset($paymentData['card_number'])) {
                $cardLastFour = substr($paymentData['card_number'], -4);
                $cardBrand = $this->getCardBrand($paymentData['card_number']);
            }

            // Handle response properly - CyberSource returns objects, not arrays
            $status = 'completed';
            if (is_object($response) && property_exists($response, 'status')) {
                $status = $response->status;
            } elseif (is_array($response) && isset($response['status'])) {
                $status = $response['status'];
            }

            return [
                'success' => true,
                'payment_intent' => $response,
                'transaction_id' => $transactionId,
                'status' => $status,
                'card_brand' => $cardBrand,
                'card_last_four' => $cardLastFour
            ];
        } catch (\Exception $e) {
            Log::error('CyberSource card payment failed: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Process payment with payment method (for compatibility with Stripe-like flow)
     */
    public function processPaymentWithPaymentMethod(array $paymentData)
    {
        // For CyberSource, we'll treat this similar to card payment
        // In a real implementation, you might have stored payment methods
        return $this->processCardPayment($paymentData);
    }

    /**
     * Create an empty payment intent for later processing
     */
    public function createEmptyPaymentIntent(array $paymentData)
    {
        $transactionId = 'cybersource_' . Str::random(16);

        try {
            $clientRefInfo = new Ptsv2paymentsClientReferenceInformation([
                'code' => $transactionId
            ]);

            $processingInfo = new Ptsv2paymentsProcessingInformation([
                'capture' => false // Authorization only, to be captured later
            ]);

            $amountDetails = new Ptsv2paymentsOrderInformationAmountDetails([
                'totalAmount' => number_format($paymentData['amount'], 2, '.', ''),
                'currency' => $paymentData['currency'] ?? 'USD'
            ]);

            $orderInfo = new Ptsv2paymentsOrderInformation([
                'amountDetails' => $amountDetails
            ]);

            // Create a basic payment request without payment information
            $paymentRequest = new CreatePaymentRequest([
                'clientReferenceInformation' => $clientRefInfo,
                'processingInformation' => $processingInfo,
                'orderInformation' => $orderInfo
            ]);

            // For CyberSource, we'll return the prepared data for later use
            return [
                'success' => true,
                'payment_intent' => [
                    'id' => $transactionId,
                    'amount' => $paymentData['amount'],
                    'currency' => $paymentData['currency'] ?? 'USD',
                    'status' => 'requires_payment_method'
                ],
                'client_secret' => $transactionId, // Use transaction ID as client secret
                'transaction_id' => $transactionId
            ];
        } catch (\Exception $e) {
            Log::error('CyberSource empty payment intent creation failed: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Verify a payment transaction
     */
    public function verify($transactionId)
    {
        try {
            // Use the transaction details API to get payment status
            $response = $this->transactionDetailsApi->getTransaction($transactionId);

            // Handle response properly - CyberSource returns objects, not arrays
            $status = 'unknown';
            if (is_object($response) && property_exists($response, 'status')) {
                $status = $response->status;
            } elseif (is_array($response) && isset($response['status'])) {
                $status = $response['status'];
            }

            $isSuccessful = in_array($status, ['AUTHORIZED', 'CAPTURED', 'SETTLED']);

            return [
                'success' => true,
                'payment_intent' => $response,
                'status' => $status,
                'is_paid' => $isSuccessful
            ];
        } catch (\Exception $e) {
            Log::error('CyberSource payment verification failed: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'is_paid' => false
            ];
        }
    }

    /**
     * Get card brand from card number
     */
    protected function getCardBrand($cardNumber)
    {
        $cardNumber = preg_replace('/\D/', '', $cardNumber);

        if (preg_match('/^4/', $cardNumber)) {
            return 'visa';
        } elseif (preg_match('/^5[1-5]/', $cardNumber)) {
            return 'mastercard';
        } elseif (preg_match('/^3[47]/', $cardNumber)) {
            return 'amex';
        } elseif (preg_match('/^6(?:011|5)/', $cardNumber)) {
            return 'discover';
        }

        return 'unknown';
    }

    /**
     * Authorize payment using token (for Flex Microform or similar)
     */
    public function authorizePayment($token, $amount, $currency = 'USD', $additionalData = [])
    {
        try {
            $transactionId = 'cybersource_auth_' . Str::random(16);

            $clientRefInfo = new Ptsv2paymentsClientReferenceInformation([
                'code' => $transactionId
            ]);

            $processingInfo = new Ptsv2paymentsProcessingInformation([
                'capture' => false // Authorization only
            ]);

            $amountDetails = new Ptsv2paymentsOrderInformationAmountDetails([
                'totalAmount' => number_format($amount, 2, '.', ''),
                'currency' => $currency
            ]);

            // Add billing information
            $billTo = new Ptsv2paymentsOrderInformationBillTo([
                'firstName' => $additionalData['firstName'] ?? 'Test',
                'lastName' => $additionalData['lastName'] ?? 'Customer',
                'address1' => $additionalData['address1'] ?? '123 Main St',
                'locality' => $additionalData['locality'] ?? 'San Francisco',
                'administrativeArea' => $additionalData['administrativeArea'] ?? 'CA',
                'postalCode' => $additionalData['postalCode'] ?? '94105',
                'country' => $additionalData['country'] ?? 'US',
                'email' => $additionalData['email'] ?? '<EMAIL>',
                'phoneNumber' => $additionalData['phoneNumber'] ?? '4158880000'
            ]);

            $orderInfo = new Ptsv2paymentsOrderInformation([
                'amountDetails' => $amountDetails,
                'billTo' => $billTo
            ]);

            // Use token for payment information
            $tokenInfo = new Ptsv2paymentsPaymentInformationFluidData([
                'value' => $token
            ]);

            $paymentInfo = new Ptsv2paymentsPaymentInformation([
                'fluidData' => $tokenInfo
            ]);

            $paymentRequest = new CreatePaymentRequest([
                'clientReferenceInformation' => $clientRefInfo,
                'processingInformation' => $processingInfo,
                'orderInformation' => $orderInfo,
                'paymentInformation' => $paymentInfo
            ]);

            $response = $this->paymentsApi->createPayment($paymentRequest);

            // Handle response properly
            $status = 'pending';
            if (is_object($response) && property_exists($response, 'status')) {
                $status = $response->status;
            } elseif (is_array($response) && isset($response['status'])) {
                $status = $response['status'];
            }

            return [
                'success' => true,
                'transaction_id' => $transactionId,
                'authorization_code' => $response->processorInformation->approvalCode ?? null,
                'status' => $status,
                'response' => $response
            ];

        } catch (\CyberSource\ApiException $e) {
            Log::error('CyberSource authorization failed:', [
                'message' => $e->getMessage(),
                'code' => $e->getCode(),
                'response_body' => json_encode($e->getResponseBody())
            ]);

            return [
                'success' => false,
                'error' => 'Authorization failed: ' . $e->getMessage(),
                'code' => $e->getCode()
            ];
        } catch (\Exception $e) {
            Log::error('CyberSource authorization error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Generate JWT token for Flex Microform
     */
    public function generateFlexToken($targetOrigins = [])
    {
        try {
            // This would typically use CyberSource's Flex API
            // For now, we'll create a basic implementation

            $payload = [
                'iss' => $this->merchantId,
                'aud' => 'flex/v2/tokens',
                'iat' => time(),
                'exp' => time() + 3600, // 1 hour expiration
                'jti' => Str::random(32),
                'targetOrigins' => $targetOrigins ?: ['http://localhost:4001', 'https://localhost:4001']
            ];

            // In a real implementation, you would use CyberSource's JWT library
            // For now, we'll return the configuration needed
            return [
                'success' => true,
                'keyId' => $this->apiKey,
                'token' => base64_encode(json_encode($payload)), // Mock token
                'targetOrigins' => $payload['targetOrigins'],
                'expiresAt' => $payload['exp']
            ];

        } catch (\Exception $e) {
            Log::error('CyberSource token generation failed: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Generate CyberSource captureContext for Flex Microform using actual API
     */
    public function generateCaptureContext($targetOrigins = [])
    {
        try {
            $client = new Client();

            // Prepare the payload for CyberSource Unified Checkout API
            $payload = [
                'targetOrigins' => $targetOrigins ?: ['https://example.com'],
                'clientVersion' => '0.26',
                'allowedCardNetworks' => ['VISA', 'MASTERCARD', 'AMEX', 'DISCOVER'],
                'allowedPaymentTypes' => ['PANENTRY'],
                'country' => 'US',
                'locale' => 'en_US',
                'orderInformation' => [
                    'amountDetails' => [
                        'totalAmount' => '10.00',
                        'currency' => 'USD'
                    ]
                ]
            ];

            $payloadJson = json_encode($payload);
            $currentDate = gmdate('D, d M Y H:i:s T');
            $digest = 'SHA-256=' . base64_encode(hash('sha256', $payloadJson, true));

            // Use the correct CyberSource REST API endpoint for capture contexts
            $apiUrl = 'https://' . $this->runEnvironment . '/up/v1/capture-contexts';
            $host = $this->runEnvironment;

            // Generate HTTP Signature with correct host
            $httpSignature = $this->generateHttpSignature($currentDate, $digest, $host);

            Log::info('CyberSource Flex API call:', [
                'url' => $apiUrl,
                'environment' => $this->runEnvironment,
                'merchant_id' => $this->merchantId,
                'api_key' => substr($this->apiKey, 0, 8) . '...',
                'shared_secret_length' => strlen($this->sharedSecret),
                'payload' => $payload
            ]);

            // Make the API call to CyberSource Flex
            $response = $client->post($apiUrl, [
                'headers' => [
                    'v-c-merchant-id' => $this->merchantId,
                    'Date' => $currentDate,
                    'Digest' => $digest,
                    'Host' => $host,
                    'Signature' => $httpSignature,
                    'Content-Type' => 'application/json',
                ],
                'body' => $payloadJson,
                'timeout' => 30,
                'verify' => true
            ]);

            $responseBody = $response->getBody()->getContents();

            Log::info('CyberSource Flex API response:', [
                'status_code' => $response->getStatusCode(),
                'raw_response' => substr($responseBody, 0, 200) . '...', // Truncate for logging
                'response_length' => strlen($responseBody)
            ]);

            // CyberSource returns the JWT token directly as a string, not wrapped in JSON
            if ($response->getStatusCode() === 201 && !empty($responseBody)) {
                // The response body is the JWT token itself
                $captureContext = trim($responseBody);

                return [
                    'success' => true,
                    'captureContext' => $captureContext,
                    'keyId' => $this->apiKey,
                    'merchantId' => $this->merchantId,
                    'environment' => $this->runEnvironment,
                    'targetOrigins' => $payload['targetOrigins'],
                    // 'clientLibrary' => 'https://testup.cybersource.com/uc/v1/assets/0.26.0/SecureAcceptance.js',
                    'clientLibrary' => 'https://flex.cybersource.com/cybersource/assets/microform/0.11/flex-microform.min.js',
                    'clientLibraryIntegrity' => 'sha256-3Ews7Va7s6LvJlhtFDBNhj2MIBC9+H9tciVKyRBGy/U=',
                    'expiresAt' => time() + 3600, // Assuming 1 hour expiration
                    'issuedAt' => time()
                ];
            } else {
                throw new \Exception('Invalid response from CyberSource Flex API');
            }

        } catch (\GuzzleHttp\Exception\RequestException $e) {
            Log::error('CyberSource Flex API request failed:', [
                'message' => $e->getMessage(),
                'response' => $e->hasResponse() ? $e->getResponse()->getBody()->getContents() : null,
                'request_payload' => $payload
            ]);

            return [
                'success' => false,
                'error' => 'Failed to generate capture context: ' . $e->getMessage()
            ];
        } catch (\Exception $e) {
            Log::error('CyberSource capture context generation failed: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }


    /**
     * Generate HTTP Signature for CyberSource API authentication
     */
    private function generateHttpSignature($date, $digest, $host)
    {
        try {
            // Create the signature string
            $signatureString = "(request-target): post /up/v1/capture-contexts\n";
            $signatureString .= "host: {$host}\n";
            $signatureString .= "date: {$date}\n";
            $signatureString .= "digest: {$digest}\n";
            $signatureString .= "v-c-merchant-id: {$this->merchantId}";

            Log::info('HTTP Signature String:', [
                'signature_string' => $signatureString,
                'host' => $host,
                'merchant_id' => $this->merchantId
            ]);

            // Generate the signature using the shared secret key (decode from base64 first)
            $decodedSecret = base64_decode($this->sharedSecret);
            $signature = base64_encode(hash_hmac('sha256', $signatureString, $decodedSecret, true));

            // Format the signature header
            $signatureHeader = 'keyid="' . $this->apiKey . '", ';
            $signatureHeader .= 'algorithm="HmacSHA256", ';
            $signatureHeader .= 'headers="(request-target) host date digest v-c-merchant-id", ';
            $signatureHeader .= 'signature="' . $signature . '"';

            Log::info('Generated HTTP Signature:', [
                'signature_header' => $signatureHeader
            ]);

            return $signatureHeader;

        } catch (\Exception $e) {
            Log::error('HTTP Signature generation failed: ' . $e->getMessage());
            throw new \Exception('Failed to generate HTTP signature: ' . $e->getMessage());
        }
    }
}
