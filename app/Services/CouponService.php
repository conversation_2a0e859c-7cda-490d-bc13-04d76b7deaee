<?php
namespace App\Services;

use App\Models\Coupon;

class CouponService
{
    public function getAllCoupons()
    {
        return Coupon::all();
    }

    public function createCoupon(array $data)
    {
        return Coupon::create($data);
    }

    public function getCoupon ($coupon) {
        return $coupon->load(['usages', 'usages.user', 'usages.course']);
    }

    public function updateCoupon(Coupon $coupon, array $data)
    {
        $coupon->update($data);
        return $coupon;
    }

    public function deleteCoupon(Coupon $coupon)
    {
        $coupon->delete();
    }

    public function couponCheck (array $data) {
        $coupon = Coupon::where('code', $data['coupon'])
        ->where('organization_id', $data['organization_id'])
        ->where('is_active', 1)
        ->where('expiry_date', '>=', date('Y-m-d'))
        ->whereColumn('usage_number', '<', 'max_usage')
        ->first();

        if (!$coupon) {
            return null;
        }
        return $coupon;
    }

}
