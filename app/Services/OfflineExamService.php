<?php

namespace App\Services;

use App\Repositories\OfflineExamRepositoryInterface;
use Illuminate\Support\Facades\DB;
class OfflineExamService
{
    public function __construct(
        protected OfflineExamRepositoryInterface $offlineExamRepository
    ) {
    }

    public function find(int $id)
    {
        return $this->offlineExamRepository->find($id);
    }

    public function all(): \Illuminate\Database\Eloquent\Collection
    {
        return $this->offlineExamRepository->all();
    }

    public function create(array $data)
    {
        return DB::transaction(function () use ($data) {
            $offlineExam = $this->offlineExamRepository->create($data);
            $batchIds = json_decode($data['batch_ids']);

                foreach ($batchIds as $batchId) {
                    $offlineExamDetail = [
                        'course_id' => $data['course_id'],
                        'offline_exam_id' => $offlineExam->id,
                        'batch_id' => $batchId,
                    ];
                    $offlineExam->batches()->create($offlineExamDetail);
                }

            return $offlineExam;
        });

    }

    public function markOfflineExam ($request) {
        return $this->offlineExamRepository->markOfflineExam($request);
    }

    public function update(array $data, int $id)
    {
        return $this->offlineExamRepository->update($data, $id);
    }

    public function delete(int $id): bool
    {
        return $this->offlineExamRepository->delete($id);
    }
}

