<?php

namespace App\Services;

use App\Models\LabelTranslation;

class LabelTranslationService
{
    public function getAll()
    {
        return LabelTranslation::all();
    }

    public function getTranslations()
    {
        $data = LabelTranslation::all();
        return $data->keyBy('label_key')->map(function($item) {
            return $item->label_value;
        })->toArray();
    }

    public function create(array $data)
    {
        return LabelTranslation::create($data);
    }

    public function getById(int $id)
    {
        return LabelTranslation::findOrFail($id);
    }

    public function update(LabelTranslation $labelTranslation, array $data)
    {
        $labelTranslation->update($data);
        return $labelTranslation;
    }

    public function delete(LabelTranslation $labelTranslation)
    {
        $labelTranslation->delete();
    }

    public function getPaginated(int $perPage = 15)
    {
        return LabelTranslation::paginate($perPage);
    }
}
