<?php

namespace App\Services;

use App\Repositories\BatchRepositoryInterface;
use App\Models\Course;
use App\Http\Traits\HelperTrait;
use Illuminate\Http\UploadedFile;

class BatchService
{
    use HelperTrait;

    public function __construct(
        protected BatchRepositoryInterface $batchRepository
    ) {
    }

    public function create(array $data)
    {
        // return $data;
        $course = Course::findOrFail($data['course_id']);
        $data['organization_id'] = $course->organization_id;

        if (isset($data['image']) && $this->isFileValid($data['image'])) {
            $data['image'] = $this->fileUpload($data['image'], 'batches');
        }

        $student_ids = json_decode($data['student_ids'], true);
        unset($data['student_ids']);


        $mentor_ids = json_decode($data['mentor_ids'], true);
        unset($data['mentor_ids']);

        $batch = $this->batchRepository->create($data);

        $students = [];
        if (gettype($student_ids) == 'array') {

            foreach($student_ids as $student_id) {
                $students[] = [
                    'batch_id' => $batch->id,
                    'student_id' => $student_id
                ];
            }
            $studentData = [
                'batch_id' => $batch->id,
                'students' => $students
            ];
            $batchStudents = $this->batchRepository->addStudents($studentData);
        }

        $mentors = [];
        if (gettype($mentor_ids) == 'array') {
        foreach($mentor_ids as $mentor_id) {
            $mentors[] = [
                'batch_id' => $batch->id,
                'mentor_id' => $mentor_id
            ];
        }
            $mentorData = [
                'batch_id' => $batch->id,
                'mentors' => $mentors
            ];

            $batchMentors = $this->batchRepository->addMentors($mentorData);
        }
        return $batch;
    }

    public function update(array $data, $id)
    {
        $batch = $this->batchRepository->find($id);
        if (isset($data['image']) && $this->isFileValid($data['image'])) {
            $data['image'] = $this->imageUploadService($data['image'], 'batches', $batch->image);

        }

        return $this->batchRepository->update($data, $id);
    }

    public function delete($id)
    {
        return $this->batchRepository->delete($id);
    }

    public function all($request)
    {
        return $this->batchRepository->all($request);
    }

    public function find($id)
    {
        return $this->batchRepository->find($id);
    }


    public function addStudents(array $data)
    {
        $students = [];
        foreach($data['student_ids'] as $student_id) {
            $students[] = [
                'batch_id' => $data['batch_id'],
                'student_id' => $student_id
            ];
        }
        $data['students'] = $students;
        return $this->batchRepository->addStudents($data);
    }

    public function removeStudents(array $data)
    {
        return $this->batchRepository->removeStudents($data);
    }


    public function addMentors(array $data)
    {
        $mentors = [];
        foreach($data['mentor_ids'] as $mentor_id) {
            $mentors[] = [
                'batch_id' => $data['batch_id'],
                'mentor_id' => $mentor_id
            ];
        }
        $data['mentors'] = $mentors;
        return $this->batchRepository->addMentors($data);
    }

    public function mentorBatchList($request) {
        return $this->batchRepository->mentorBatchList($request);
    }

    public function removeMentors(array $data)
    {
        return $this->batchRepository->removeStudents($data);
    }


    public function studentList ($request) {
        return $this->batchRepository->studentList($request);
    }

    /**
     * Check if a file is valid.
     */
    protected function isFileValid($file): bool
    {
        return $file instanceof UploadedFile && $file->isValid();
    }
}
