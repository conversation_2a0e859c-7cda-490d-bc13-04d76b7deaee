<?php

namespace App\Services;

use App\Repositories\PaymentRepositoryInterface;
use App\Models\Course;
use App\Http\Traits\HelperTrait;
use Illuminate\Http\UploadedFile;
use Auth;
class PaymentService
{
    use HelperTrait;

    public function __construct(
        protected PaymentRepositoryInterface $paymentRepository
    ) {
    }

    public function create(array $data)
    {
        $data['features'] = json_encode($data['features']);
        $data['disabled_features'] = json_encode($data['disabled_features']);
        return $this->paymentRepository->create($data);
    }

    public function update(array $data, $id)
    {
        $batch = $this->paymentRepository->find($id);
        if (isset($data['image']) && $this->isFileValid($data['image'])) {
            $data['image_path'] = $this->imageUpload($data['image'], 'images/batches', $batch->image);
        }

        return $this->paymentRepository->update($data, $id);
    }

    public function delete($id)
    {
        return $this->paymentRepository->delete($id);
    }

    public function all($request)
    {
        return $this->paymentRepository->all($request);
    }

    public function find($id)
    {
        return $this->paymentRepository->find($id);
    }


    public function makePaymentOrganization  (array $data) {
        $user = Auth::user();
        $package = $this->paymentRepository->find($data['package_id']);

        $data['organization_id'] = $user->organization_id;
        $data['user_id'] = $user->organization_id;

        $deadline = null;

        if ($package->billing_cycle ==  "monthly") {
            $deadline = now()->addMonth()->format('Y-m-d H:i:s');
        } else if ($package->billing_cycle ==  "yearly") {
            $deadline = now()->addYear()->format('Y-m-d H:i:s');
        }

        $payment = [
            'organization_id' => $user->organization_id,
            'user_id' => $user->id,
            'package_id' => $package->id,
            'expiry_date' => $deadline,
            'paid_amount' => $package->sale_price,
            'discount_amount' => isset($data['discount_amount']) ? $data['discount_amount'] : 0,
            'bkash_number' => $data['bkash_number'],
            'payment_method' => $data['payment_method'],
            'customer_name' => (isset($data['customer_name']) && $data['customer_name']) ? $data['customer_name'] : $user->name,
            'customer_email' => (isset($data['customer_email']) && $data['customer_email']) ? $data['customer_email'] : $user->email,
            'customer_phone' => (isset($data['customer_phone']) && $data['customer_phone']) ? $data['customer_phone'] : $user->email,
        ];

        return $this->paymentRepository->makePaymentOrganization($payment);
    }

    /**
     * Check if a file is valid.
     */
    protected function isFileValid($file): bool
    {
        return $file instanceof UploadedFile && $file->isValid();
    }
}
