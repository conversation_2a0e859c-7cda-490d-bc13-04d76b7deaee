<?php

namespace App\Services;

use Illuminate\Http\Request;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Aws\Exception\AwsException;

class FileUploadService
{
    /**
     * Upload file to AWS S3 or local storage
     *
     * @param Request $request
     * @param string $fieldName
     * @param string $folder
     * @param string|null $oldFile
     * @param bool $useS3
     * @return array|null
     */
    public function uploadFile(Request $request, string $fieldName, string $folder, ?string $oldFile = null, bool $useS3 = true): ?array
    {
        if (!$request->hasFile($fieldName)) {
            return null;
        }

        $file = $request->file($fieldName);

        if ($useS3) {
            return $this->uploadToS3($file, $folder, $oldFile);
        } else {
            return $this->uploadToLocal($file, $folder, $oldFile);
        }
    }

    /**
     * Upload file directly to AWS S3 or local storage
     *
     * @param UploadedFile $file
     * @param string $folder
     * @param string|null $oldFile
     * @param bool $useS3
     * @return array|null
     */
    public function uploadFileDirect(UploadedFile $file, string $folder, ?string $oldFile = null, bool $useS3 = true): ?array
    {
        if (!$file || !$file->isValid()) {
            return null;
        }

        if ($useS3) {
            return $this->uploadToS3($file, $folder, $oldFile);
        } else {
            return $this->uploadToLocal($file, $folder, $oldFile);
        }
    }

    /**
     * Upload file to AWS S3
     *
     * @param UploadedFile $file
     * @param string $folder
     * @param string|null $oldFile
     * @return array|null
     */
    private function uploadToS3(UploadedFile $file, string $folder, ?string $oldFile = null): ?array
    {
        try {
            // Delete old file if exists
            if ($oldFile) {
                $this->deleteFromS3($oldFile);
            }

            // Validate file
            if (!$file->isValid()) {
                throw new \Exception('Invalid file upload');
            }

            $folder = rtrim($folder, '/');
            $extension = $file->getClientOriginalExtension();
            $fileName = 'bb_' . time() . '_' . Str::uuid() . '.' . $extension;
            $filePath = $folder . '/' . $fileName;

            Log::info('Attempting S3 upload via FileUploadService', [
                'original_name' => $file->getClientOriginalName(),
                'folder' => $folder,
                'file_path' => $filePath,
                'size' => $file->getSize(),
                'mime_type' => $file->getMimeType()
            ]);

            // Try different upload methods without ACL (bucket doesn't support ACLs)
            try {
                // Method 1: putFileAs without ACL
                $uploaded = Storage::disk('s3')->putFileAs($folder, $file, $fileName);

                if (!$uploaded) {
                    throw new \Exception('putFileAs returned false');
                }

                Log::info('S3 upload successful using putFileAs: ' . $uploaded);

            } catch (\Exception $e1) {
                Log::warning('putFileAs failed, trying alternative method: ' . $e1->getMessage());

                // Method 2: put with file contents without ACL
                $fileContents = file_get_contents($file->getPathname());
                $uploaded = Storage::disk('s3')->put($filePath, $fileContents);

                if (!$uploaded) {
                    throw new \Exception('Alternative upload method also failed');
                }

                Log::info('S3 upload successful using put method: ' . $uploaded);
            }

            // Verify the file was uploaded
            if (!Storage::disk('s3')->exists($filePath)) {
                throw new \Exception('File upload verification failed - file not found in S3');
            }

            // Get the public URL
            $url = Storage::disk('s3')->url($filePath);

            Log::info('S3 upload completed successfully via FileUploadService', [
                'file_path' => $filePath,
                'url' => $url
            ]);

            return [
                'path' => $filePath,
                'url' => $url,
                'full_url' => $url,
                'storage' => 's3',
                'file_name' => $fileName,
                'original_name' => $file->getClientOriginalName(),
                'size' => $file->getSize(),
                'mime_type' => $file->getMimeType()
            ];

        } catch (AwsException $e) {
            Log::error('S3 Upload AWS error in FileUploadService: ' . $e->getAwsErrorMessage(), [
                'aws_error_code' => $e->getAwsErrorCode(),
                'aws_error_type' => $e->getAwsErrorType()
            ]);
            throw new \Exception('S3 Upload error: ' . $e->getAwsErrorMessage());
        } catch (\Exception $e) {
            Log::error('File upload error in FileUploadService: ' . $e->getMessage(), [
                'folder' => $folder ?? 'unknown'
            ]);
            throw new \Exception('File upload error: ' . $e->getMessage());
        }
    }

    /**
     * Upload file to local storage
     *
     * @param UploadedFile $file
     * @param string $folder
     * @param string|null $oldFile
     * @return array|null
     */
    private function uploadToLocal(UploadedFile $file, string $folder, ?string $oldFile = null): ?array
    {
        try {
            // Delete old file if exists
            if ($oldFile) {
                $this->deleteFromLocal($oldFile);
            }

            $extension = $file->getClientOriginalExtension();
            $fileName = 'bb_' . time() . '_' . Str::uuid() . '.' . $extension;
            $filePath = $folder . '/' . $fileName;

            // Create directory if it doesn't exist
            $uploadPath = public_path('uploads/' . $folder);
            if (!file_exists($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }

            // Move file to local storage
            $file->move($uploadPath, $fileName);

            $url = asset('uploads/' . $filePath);

            Log::info('File uploaded locally: ' . $filePath);

            return [
                'path' => $filePath,
                'url' => $url,
                'full_url' => $url,
                'storage' => 'local',
                'file_name' => $fileName,
                'original_name' => $file->getClientOriginalName(),
                'size' => $file->getSize(),
                'mime_type' => $file->getMimeType()
            ];

        } catch (\Exception $e) {
            Log::error('Local file upload error: ' . $e->getMessage());
            throw new \Exception('Local file upload error: ' . $e->getMessage());
        }
    }

    /**
     * Delete file from AWS S3
     *
     * @param string $filePath
     * @return bool
     */
    public function deleteFromS3(string $filePath): bool
    {
        if (!$filePath) {
            return false;
        }

        try {
            // Remove the base URL if it's a full URL
            if (str_contains($filePath, 'amazonaws.com')) {
                $parsedUrl = parse_url($filePath);
                $filePath = ltrim($parsedUrl['path'], '/');
            }

            if (Storage::disk('s3')->exists($filePath)) {
                Storage::disk('s3')->delete($filePath);
                Log::info('S3 file deleted: ' . $filePath);
                return true;
            }

            return false;
        } catch (AwsException $e) {
            Log::error('S3 Delete AWS error: ' . $e->getAwsErrorMessage());
            return false;
        } catch (\Exception $e) {
            Log::error('S3 Delete error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Delete file from local storage
     *
     * @param string $filePath
     * @return bool
     */
    public function deleteFromLocal(string $filePath): bool
    {
        if (!$filePath) {
            return false;
        }

        try {
            $fullPath = public_path('uploads/' . $filePath);
            if (file_exists($fullPath)) {
                unlink($fullPath);
                Log::info('Local file deleted: ' . $filePath);
                return true;
            }

            return false;
        } catch (\Exception $e) {
            Log::error('Local file delete error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get a temporary signed URL for private S3 files
     *
     * @param string $filePath
     * @param int $expirationMinutes
     * @return string|null
     */
    public function getSignedUrl(string $filePath, int $expirationMinutes = 60): ?string
    {
        if (!$filePath) {
            return null;
        }

        try {
            // Remove the base URL if it's a full URL
            if (str_contains($filePath, 'amazonaws.com')) {
                $parsedUrl = parse_url($filePath);
                $filePath = ltrim($parsedUrl['path'], '/');
            }

            if (!Storage::disk('s3')->exists($filePath)) {
                return null;
            }

            return Storage::disk('s3')->temporaryUrl($filePath, now()->addMinutes($expirationMinutes));

        } catch (AwsException $e) {
            Log::error('S3 Signed URL AWS error: ' . $e->getAwsErrorMessage());
            return null;
        } catch (\Exception $e) {
            Log::error('S3 Signed URL error: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Check if file exists in S3
     *
     * @param string $filePath
     * @return bool
     */
    public function fileExistsInS3(string $filePath): bool
    {
        try {
            return Storage::disk('s3')->exists($filePath);
        } catch (\Exception $e) {
            Log::error('S3 file exists check error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get file size from S3
     *
     * @param string $filePath
     * @return int|null
     */
    public function getFileSizeFromS3(string $filePath): ?int
    {
        try {
            if (Storage::disk('s3')->exists($filePath)) {
                return Storage::disk('s3')->size($filePath);
            }
            return null;
        } catch (\Exception $e) {
            Log::error('S3 file size check error: ' . $e->getMessage());
            return null;
        }
    }
}
