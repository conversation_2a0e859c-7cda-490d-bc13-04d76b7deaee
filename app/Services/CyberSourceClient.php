<?php

namespace App\Services;

use App\Models\OrganizationPaymentGateway;
use Illuminate\Support\Facades\Http;

class CyberSourceClient
{
    protected $config;

    public function __construct(int $organizationId)
    {
        $pg = OrganizationPaymentGateway::get();
        // return $pg;
        $this->config = [
            'merchantId' => "mrueen_2025_1748852656",
            'keyId'      => "149ee39c-a643-4f77-80dd-0271ec3c553a",
            'secretKey'  => "GOIMne99gl6e7s8P7vQrD8j+l+AEa05Sc6hAtn1rIc0=",
            'endpoint'   => 'https://apitest.cybersource.com'
            // 'endpoint'   => $pg->environment === 'sandbox'
            //     ? 'https://apitest.cybersource.com'
            //     : 'https://api.cybersource.com',
        ];


        // {
        //     "key": "149ee39c-a643-4f77-80dd-0271ec3c553a",
        //     "organization_id": "mrueen_2025_1748852656",
        //     "shared_secret_key": "GOIMne99gl6e7s8P7vQrD8j+l+AEa05Sc6hAtn1rIc0="
        // }
    }

    public function createPayment(array $payload): array
    {
        $url = $this->config['endpoint'] . '/pts/v2/payments';
        $date = gmdate("Y-m-d\TH:i:s\Z");
        $body = json_encode($payload);
        $host = parse_url($url, PHP_URL_HOST);
        $digest = base64_encode(hash('sha256', $body, true));

        $sigString =
            "(v-c-merchant-id): {$this->config['merchantId']}\n" .
            "host: {$host}\n" .
            "date: {$date}\n" .
            "(request-target): post /pts/v2/payments\n" .
            "digest: SHA-256={$digest}";

        $signature = base64_encode(
            hash_hmac('sha256', $sigString, base64_decode($this->config['secretKey']), true)
        );

        $headers = [
            'v-c-merchant-id' => $this->config['merchantId'],
            'Date'            => $date,
            'Host'            => $host,
            'Digest'          => "SHA-256={$digest}",
            'Signature'       => "keyid=\"{$this->config['keyId']}\",algorithm=\"HmacSHA256\",headers=\"(v-c-merchant-id) host date (request-target) digest\",signature=\"{$signature}\"",
            'Content-Type'    => 'application/json',
        ];

        $res = Http::withHeaders($headers)->post($url, $payload);
        return $res->throw()->json();
    }

    public function createSession(): array
    {
        $url = $this->config['endpoint'] . '/flex/v1/sessions';
        $res = Http::withBasicAuth($this->config['keyId'], $this->config['secretKey'])
            ->post($url, ['merchantId' => $this->config['merchantId']])
            ->throw();

        return $res->json(); // Should include session.id
    }
}
