<?php

namespace App\Repositories;

use App\Models\OrganizationPackage;
use App\Models\OrganizationPayment;
use App\Models\Organization;


class PaymentRepository implements PaymentRepositoryInterface
{

    public function all($request) {
        return OrganizationPackage::where('is_active', 1)->get();
    }

    public function create(array $data) {
        return OrganizationPackage::create($data);
    }

    public function update(array $data, $id) {
        return OrganizationPackage::find($id)->update($data);
    }

    public function delete($id) {
        return OrganizationPackage::find($id)->delete();
    }

    public function find($id) {
        return OrganizationPackage::find($id);
    }

    public function packageList($request) {
        return OrganizationPackage::where('is_active', 1)->get();
    }


    public function makePaymentOrganization(array $data) {
        $payment = OrganizationPayment::create($data);
        $organization = Organization::find($data['organization_id']);

        $organization->last_payment_id = $payment->id;
        $organization->save();

        return $payment;
    }
}

