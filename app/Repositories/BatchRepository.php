<?php

namespace App\Repositories;

use App\Models\Batch;
use App\Models\MentorInformation;
use Auth;

class BatchRepository implements BatchRepositoryInterface
{
    public function all($request)
    {
        $results = Batch::query();
        $fillable = (new Batch)->getFillable();
        $results->whereHas('course');
        $results = $results->where(function($query) use ($request, $fillable) {
            if ($request->search) {
                $search = $request->input('search');
                foreach ($this->searchKeys as $key) {
                    $query->orWhere($key, 'like', '%'.$search.'%');
                }
            }

            foreach ($request->except(['search', 'pagination', 'itemsPerPage', 'currentPage']) as $key => $value) {
                if(in_array($key, $fillable)) {
                    if ($value) {
                        $query->where($key, $value);
                    }
                }
            }
        });
        if (Auth::user()->organization_id) {
            $results = $results->where('organization_id', Auth::user()->organization_id);
        }
        if ($request->input('pagination') == 'false') {
            return $results->get();
        } else {
            $itemsPerPage = $request->input('items_per_page', 12);
            $currentPage = $request->input('current_page', 0);
            $results = $results->paginate($itemsPerPage, ['*'], 'page', $currentPage );
            return [
                'data' => $results,
                'total' => $results->total(),
                'per_page' => $results->perPage(),
                'current_page' => $results->currentPage() - 1,
                'last_page' => $results->lastPage() - 1,
                'from' => $results->firstItem() - 1,
                'to' => $results->lastItem() - 1
            ];
        }
    }

    public function create(array $data)
    {
        return Batch::create($data);
    }

    public function update(array $data, $id)
    {
        $batch = Batch::findOrFail($id);
        $batch->update($data);
        return $batch;
    }

    public function delete($id)
    {
        $batch = Batch::findOrFail($id);
        $batch->delete();
    }

    public function find($id)
    {
        return Batch::findOrFail($id);
    }

    public function addStudents (array $data)
    {
        $batch = Batch::findOrFail($data['batch_id']);
        $existingStudentIds = $batch->students->pluck('student_id')->toArray();
        $newStudents = array_filter($data['students'], function ($student) use ($existingStudentIds) {
            return !in_array($student['student_id'], $existingStudentIds);
        });
        $batch->students()->insert($newStudents);
        return $batch;
    }

    public function removeStudents (array $data)
    {
        $batch = Batch::findOrFail($data['batch_id']);
        $batch->students()->whereIn('student_id', $data['student_ids'])->delete();
        return $batch;
    }

    public function addMentors (array $data)
    {
        $batch = Batch::findOrFail($data['batch_id']);
        $batch->mentors()->delete();
        $batch->mentors()->insert($data['mentors']);
        return $batch;
    }

    public function mentorBatchList ($request) {
        $results = Batch::query();
        $mentor = MentorInformation::where('user_id', Auth::user()->id)->first();
        if (!$mentor) {
            return [];
        }
        if ($request->course_id) {
            $results = $results->where('course_id', $request->course_id);
        }
        $results = $results->whereHas('mentors', function ($query) use ($request, $mentor) {
            $query->where('mentor_id', $mentor->id);
        });
        return $results->get();
    }

    public function removeMentors (array $data)
    {
        $batch = Batch::findOrFail($data['batch_id']);
        $batch->mentors()->whereIn('mentor_id', $data['mentor_ids'])->delete();
        return $batch;
    }


    public function studentList ($request) {
       return $results = Batch::findOrFail($request->batch_id)->studentList;

    }
}
