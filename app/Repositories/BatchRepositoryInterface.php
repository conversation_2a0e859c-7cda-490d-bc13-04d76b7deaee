<?php

namespace App\Repositories;

interface BatchRepositoryInterface
{
    public function all($request);

    public function create(array $data);

    public function update(array $data, $id);

    public function delete($id);

    public function find($id);

    public function addStudents(array $data);

    public function removeStudents(array $data);

    public function addMentors(array $data);

    public function removeMentors(array $data);

    public function mentorBatchList($request);

    public function studentList ($request);
}
