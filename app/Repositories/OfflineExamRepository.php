<?php

namespace App\Repositories;

use App\Models\OfflineExam;
use App\Models\OfflineExamStudents;


class OfflineExamRepository implements OfflineExamRepositoryInterface
{
    public function find ($id)
    {
        return OfflineExam::find($id);
    }

    public function all ()
    {
        return OfflineExam::get();
    }

    public function create (array $data)
    {
        $offlineExam = new OfflineExam();
        $offlineExam->fill($data);
        $offlineExam->save();
        return $offlineExam;
    }

    public function update (array $data, $id)
    {
        return OfflineExam::find($id)->update($data);
    }

    public function delete ($id)
    {
        $offlineExam = OfflineExam::find($id);

        if ($offlineExam) {
            $offlineExam->students()->delete();
            $offlineExam->batches()->delete();
            return $offlineExam->delete();
        }

        return false;

    }

    public function offlineExamRepository ($request) {
        return $data = $request->all();
        // return OfflineExam::where('id', $data['id'])->first();
    }

    public function markOfflineExam($request) {
        $courseId = $request->id;

        $students = $request->students;

        foreach ($students as $student) {
            OfflineExamStudents::where('id', $student['id'])->where('offline_exam_id', $request->id)
                ->update([
                    'mcq_mark' => $student['mcq_mark'],
                    'written_mark' => $student['written_mark'],
                    'total_mark' => $student['mcq_mark'] + $student['written_mark']
                ]);
        }

        return true;
    }

}

