<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OfflineExamDetailsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $students = $request->batch_id ? $this->students->where('batch_id', $request->batch_id) : $this->students;
        return [
            "id" => $this->id,
            "organization_id" => $this->organization_id,
            "course_id" => $this->course_id,
            "title" => $this->title,
            "title_bn" => $this->title_bn,
            "description" => $this->description,
            "mcq_mark" => $this->mcq_mark,
            "written_mark" => $this->written_mark,
            "assignment_mark" => $this->assignment_mark,
            "presentation_mark" => $this->presentation_mark,
            "practical_mark" => $this->practical_mark,
            "pass_mark" => $this->pass_mark,
            "total_mark" => $this->total_mark,
            "duration" => $this->duration,
            "exam_date" => $this->exam_date,
            "deleted_at" => $this->deleted_at,
            "created_at" => $this->created_at,
            "updated_at" => $this->updated_at,
            "exam_students" => OfflineExamStudentListResource::collection($students),
            "batches" => OfflineExamBatchesListResource::collection($this->batches),

        ];
    }
}
