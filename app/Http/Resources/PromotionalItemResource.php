<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class PromotionalItemResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'title_bn' => $this->title_bn,
            'description' => $this->description,
            'image' => $this->image,
            'type' => $this->type,
            'course' => $this->whenLoaded('course', function () {
                return [
                    'id' => $this->course->id,
                    'title' => $this->course->title,
                    'thumbnail' => $this->course->thumbnail,
                    'slug' => $this->course->slug,
                ];
            }),
            'chapter_video' => $this->whenLoaded('chapterVideo'),
            'chapter_script' => $this->whenLoaded('chapterScript'),
            'chapter_quiz' => $this->whenLoaded('chapterQuiz'),
            'ebook' => $this->whenLoaded('ebook', function () {
                return [
                    'id' => $this->ebook->id,
                    'title' => $this->ebook->title,
                    'image' => $this->ebook->image,
                ];
            }),
            'youtube_url' => $this->youtube_url,
            'is_active' => $this->is_active,
            'created_by' => $this->whenLoaded('createdBy', function () {
                return [
                    'id' => $this->createdBy->id,
                    'name' => $this->createdBy->name,
                    'image' => $this->createdBy->image,
                    'user_type' => $this->createdBy->user_type,
                ];
            }),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
