<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CategoryDetailsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            "id" => $this->id,
            "name" => $this->name,
            "link" => $this->link,
            "has_submenu" => $this->has_submenu,
            "is_course" => $this->is_course,
            "is_content" => $this->is_content,
            "sequence" => $this->sequence,
            "icon" => $this->icon,
            "headline" => $this->headline,
            "description" => $this->description,
            "organization_id" => $this->organization_id,
            "sub_categories" => $this->subCategories,
            "category_items" => $this->categoryItems,
            "is_active" => $this->is_active,
            "deleted_at" => $this->deleted_at,
            "created_by" => $this->created_by,
            "created_at" => $this->created_at->format("Y-m-d H:i:s"),
            "updated_at" => $this->updated_at->format("Y-m-d H:i:s")
        ];
    }
}
