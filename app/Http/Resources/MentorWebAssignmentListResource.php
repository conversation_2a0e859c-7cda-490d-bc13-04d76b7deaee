<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class MentorWebAssignmentListResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'organization_id' => $this->organization_id,
            'mentor_id' => $this->mentor_id,
            'course_title' => $this->course->title,
            'title' => $this->title,
            'title_bn' => $this->title_bn,
            'publish_date' => $this->publish_date,
            'deadline' => $this->deadline,
            'status' => $this->status,
        ];
    }
}
