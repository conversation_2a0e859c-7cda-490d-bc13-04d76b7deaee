<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OfflineExamStudentListResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            "id" => $this->id,
            "course_id" => $this->course_id,
            "offline_exam_id" => $this->offline_exam_id,
            "student_id" => $this->student_id,
            "student_name" => $this->student?->name,
            "contact_no" => $this->student?->contact_no,
            "email" => $this->student?->email,
            "image" => $this->student?->image,
            "batch_id" => $this->batch_id,
            "mcq_mark" => $this->mcq_mark,
            "written_mark" => $this->written_mark,
            "assignment_mark" => $this->assignment_mark,
            "presentation_mark" => $this->presentation_mark,
            "practical_mark" => $this->practical_mark,
            "total_mark" => $this->total_mark,
            "is_passed" => $this->is_passed,
            "marked_by" => $this->marked_by
        ];
    }
}
