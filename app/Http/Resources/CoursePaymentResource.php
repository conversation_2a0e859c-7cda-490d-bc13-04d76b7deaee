<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use App\Models\Payment;
class CoursePaymentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $data = [
            'id' => $this->id,
            'organization_id' => $this->organization_id,
            'title' => $this->title,
            'title_bn' => $this->title_bn,
            'category' => $this->category?->name,
            'description' => $this->description,
            'youtube_url' => $this->youtube_url,
            'thumbnail' => $this->thumbnail,
            'regular_price' => $this->regular_price,
            'sale_price' => $this->sale_price,
            'currency' => $this->currency,
            'discount_percentage' => $this->discount_percentage,
            'monthly_amount' => $this->monthly_amount,
            'minimum_enroll_amount' => $this->minimum_enroll_amount,
            'max_installment_qty' => $this->max_installment_qty,
            'installment_type' => $this->installment_type,
            'rating' => $this->rating,
            'is_free' => $this->is_free,
            // 'gateways' => $this->organization?->paymentGateways
            'gateways' => $this->organization?->paymentGateways->map(function ($gateway) {
                return [
                    'id' => $gateway->id,
                    'organization_id' => $gateway->organization_id,
                    'payment_gateway_id' => $gateway->payment_gateway_id,
                    'credentials' => $gateway->credentials,
                    'is_active' => $gateway->is_active,
                    'created_at' => $gateway->created_at,
                    'updated_at' => $gateway->updated_at,
                    'payment_gateway' => $gateway->paymentGateway, // <-- full object
                ];
            }),
        ];



        $user = auth('sanctum')->user();
        if ($user) {
            $payment = Payment::where('user_id', $user->id)->where('item_id', $this->id)->first();

            $data['is_purchased'] = $payment ? true : false;

        }

        return $data;
    }
}
