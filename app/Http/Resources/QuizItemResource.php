<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class QuizItemResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $data = [
            'id' => $this->id,
            'chapter_quiz_id' => $this->chapter_quiz_id,
            'type' => $this->type,
        ];

        // Add the appropriate question data based on the type
        switch ($this->type) {
            case 'mcq':
                if ($this->chapterQuizQuestion) {
                    $data['question'] = [
                        'id' => $this->chapterQuizQuestion->id,
                        'question_text' => $this->chapterQuizQuestion->question_text,
                        'question_text_bn' => $this->chapterQuizQuestion->question_text_bn,
                        'question_image' => $this->chapterQuizQuestion->question_image,
                        'option1' => $this->chapterQuizQuestion->option1,
                        'option2' => $this->chapterQuizQuestion->option2,
                        'option3' => $this->chapterQuizQuestion->option3,
                        'option4' => $this->chapterQuizQuestion->option4,
                        'option1_image' => $this->chapterQuizQuestion->option1_image,
                        'option2_image' => $this->chapterQuizQuestion->option2_image,
                        'option3_image' => $this->chapterQuizQuestion->option3_image,
                        'option4_image' => $this->chapterQuizQuestion->option4_image,
                        'answer1' => $this->chapterQuizQuestion->answer1,
                        'answer2' => $this->chapterQuizQuestion->answer2,
                        'answer3' => $this->chapterQuizQuestion->answer3,
                        'answer4' => $this->chapterQuizQuestion->answer4,
                        'explanation_text' => $this->chapterQuizQuestion->explanation_text,
                        'explanation_image' => $this->chapterQuizQuestion->explanation_image,
                        'is_active' => $this->chapterQuizQuestion->is_active,
                    ];
                }
                break;
            case 'true_false':
                if ($this->trueFalse) {
                    $data['question'] = [
                        'id' => $this->trueFalse->id,
                        'question_text' => $this->trueFalse->question_text,
                        'question_image' => $this->trueFalse->question_image,
                        'answer' => $this->trueFalse->answer,
                        'explanation_text' => $this->trueFalse->explanation_text,
                        'explanation_image' => $this->trueFalse->explanation_image,
                    ];
                }
                break;
            case 'matching':
                if ($this->matching) {
                    $data['question'] = [
                        'id' => $this->matching->id,
                        'question_text' => $this->matching->question_text,
                        'explanation_text' => $this->matching->explanation_text,
                        'explanation_image' => $this->matching->explanation_image,
                        'is_active' => $this->matching->is_active,
                        'matching_answers' => $this->matching->matchingAnswers->map(function ($answer) {
                            return [
                                'id' => $answer->id,
                                'left_item' => $answer->left_item,
                                'right_item' => $answer->right_item,
                            ];
                        }),
                    ];
                }
                break;
            case 'fill_in_blank':
                if ($this->fillInTheBlank) {
                    $data['question'] = [
                        'id' => $this->fillInTheBlank->id,
                        'question_text' => $this->fillInTheBlank->question_text,
                        'question_image' => $this->fillInTheBlank->question_image,
                        'explanation_text' => $this->fillInTheBlank->explanation_text,
                        'explanation_image' => $this->fillInTheBlank->explanation_image,
                        'is_active' => $this->fillInTheBlank->is_active,
                        'blank_answers' => $this->fillInTheBlank->blankAnswers->map(function ($answer) {
                            return [
                                'id' => $answer->id,
                                'blank_key' => $answer->blank_key,
                                'blank_answer' => $answer->blank_answer,
                            ];
                        }),
                    ];
                }
                break;
        }

        return $data;
    }
}
