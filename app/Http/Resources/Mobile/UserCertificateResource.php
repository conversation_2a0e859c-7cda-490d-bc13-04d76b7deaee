<?php

namespace App\Http\Resources\Mobile;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Auth;

class UserCertificateResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {



        $data = [
            'id' => $this->id,
            'user_id' => $this->user_id,
            'course_id' => $this->course_id,
            'course_name' => $this->course_name,
            'certification_type' => $this->certification_type,
            'background_image' => $this->background_image,
            'logo' => $this->logo,
            'certification_text' => $this->certification_text,
            'signature' => $this->signature,
            'authorize_person' => $this->authorize_person,
            'designation' => $this->designation,
            'certificate_file' => $this->certificate_file ? $this->certificate_file : 'certificates/certificate.pdf',
            'code' => $this->code,
            'name' => $this->name,
            'created_at' => $this->created_at
        ];

        return $data;
    }
}


