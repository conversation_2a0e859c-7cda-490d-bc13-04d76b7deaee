<?php

namespace App\Http\Resources\Mobile;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class LiveClassStudentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [

            'id' => $this->student_id,
            'name' => $this->student?->name,
            'image' => $this->student?->image,
            'email' => $this->student?->email,
            'contact_no' => $this->student?->contact_no,
            'class_schedule_id' => $this->class_schedule_id,
            'join_time' => $this->join_time,
            'is_joined' => $this->is_joined ? true : false,
            'is_selected' => true
        ];
    }
}
