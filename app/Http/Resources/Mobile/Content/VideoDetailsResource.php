<?php

namespace App\Http\Resources\Mobile\Content;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class VideoDetailsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            "id" => $this->id,
            "title" => $this->title,
            "title_bn" => $this->title_bn,
            "video_code" => $this->video_code,
            "author_name" => $this->author_name,
            "author_details" => $this->author_details,
            "description" => $this->description,
            "raw_url" => $this->raw_url,
            "s3_url" => $this->s3_url,
            "youtube_url" => $this->youtube_url,
            "download_url" => $this->download_url,
            "thumbnail" => $this->thumbnail,
            "duration" => (double)$this->duration,
            "rating" => $this->rating,
            "created_by" => $this->created_by,
            // "created_at" => $this->created_at->format("Y-m-d H:i:s"),
            // "updated_at" => $this->updated_at->format("Y-m-d H:i:s"),
        ];
    }
}
