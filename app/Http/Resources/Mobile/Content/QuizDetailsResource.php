<?php

namespace App\Http\Resources\Mobile\Content;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class QuizDetailsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'title_bn' => $this->title_bn,
            'description' => $this->description,
            'quiz_code' => $this->quiz_code,
            'duration' => $this->duration,
            'positive_mark' => $this->positive_mark,
            'negative_mark' => $this->negative_mark,
            'total_mark' => $this->total_mark,
            'number_of_question' => $this->number_of_question
        ];
    }
}
