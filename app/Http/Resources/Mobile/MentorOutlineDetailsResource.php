<?php

namespace App\Http\Resources\Mobile;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class MentorOutlineDetailsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            "id" => $this->id,
            "organization_id" => $this->organization_id,
            "title" => $this->title,
            "title_bn" => $this->title_bn,
            "course_id" => $this->course_id,
            "course_category_id" => $this->course_category_id,
            'type' => $this->chapter_script_id ? 'script' : ($this->chapter_video_id ? 'video' : 'quiz'),
            "script" => $this->chapter_script_id ? $this->script : null,
            "video" => $this->chapter_video_id ? $this->video : null,
            "quiz" => $this->chapter_quiz_id ? $this->quiz : null,
            "is_free" => $this->is_free
        ];
    }
}
