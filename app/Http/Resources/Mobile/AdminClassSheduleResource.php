<?php

namespace App\Http\Resources\Mobile;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use DateTime;
use DateTimeZone;
use Carbon\Carbon;
class AdminClassSheduleResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
            $current_time = (new DateTime('now', new DateTimeZone('UTC')))->format('Y-m-d\TH:i:s\Z');

            $scheduleDatetime = Carbon::parse($this->schedule_datetime);
            $currentTime = Carbon::parse($current_time);
        return [
            'id' => $this->id,
            'title' => $this->title,
            'course' => $this->course->title,
            'course_id' => $this->course_id,
            'mentor_id' => $this->mentor_id,
            'batch_id' => $this->batch_id,
            'batch_name' => $this->batch?->name,
            'mentor_name' => $this->mentor?->name,
            'mentor_image' => $this->mentor?->image,
            // 'class_url' => $this->class_url,
            'schedule_datetime' => $this->schedule_datetime,
            'current_time' => $current_time,
            'needs_to_start' => $currentTime->greaterThanOrEqualTo($scheduleDatetime) ? true : false,
            'has_started' => $this->has_started ? true : false,
            'has_completed' => $this->has_completed ? true : false,
            'start_time' => date('H:i:s', strtotime($this->start_time)),
            'end_time' => date('H:i:s', strtotime($this->end_time)),
            'duration' => (int) ((strtotime($this->end_time) - strtotime($this->start_time)) / 60),
            'student_end_time' => $this->student_end_time,
            'is_active' => $this->is_active ? true : false,
            'students_number' => $this->students->count()
        ];
    }
}

