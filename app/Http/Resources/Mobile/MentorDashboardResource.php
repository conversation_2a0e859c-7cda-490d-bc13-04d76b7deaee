<?php

namespace App\Http\Resources\Mobile;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

use App\Http\Resources\Mobile\Mentor\CourseListResource;
use App\Http\Resources\Mobile\MentorClassSheduleResource;

class MentorDashboardResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'upcoming_assignment_count' => $this->assignments->count(),
            'course_count' => $this->courses->count(),
            'student_count' => $this->studentNumber->count(),
            'live_class_count' => $this->liveClasses->count(),
            'courses' => MentorDashboardCourseListResource::collection($this->courses),
            'upcoming_class_routine' => MentorClassSheduleResource::collection($this->liveClasses->take(5))
        ];
    }
}
