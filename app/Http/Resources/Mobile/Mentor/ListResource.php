<?php

namespace App\Http\Resources\Mobile\Mentor;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ListResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            "id" => $this->mentor?->id,
            "user_id" => $this->mentor?->user_id,
            "name" => $this->mentor?->name,
            "image" => $this->mentor?->image ? $this->mentor?->image : 'user.png',
            'designation' => $this->mentor?->profession ?? $this->mentor?->education

        ];
    }
}
