<?php

namespace App\Http\Resources\Mobile\Mentor;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CourseListResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'organization_id' => $this->organization_id,
            'title' => $this->title,
            'title_bn' => $this->title_bn,
            'category_name' => $this->category?->name,
            'description' => $this->description,
            'thumbnail' => $this->thumbnail,
            'number_of_enrolled' => $this->number_of_enrolled,
            'rating' => $this->rating,
            'has_life_coach' => $this->has_life_coach
        ];
    }
}
