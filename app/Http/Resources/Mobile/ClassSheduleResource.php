<?php

namespace App\Http\Resources\Mobile;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ClassSheduleResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            // 'id' => $this->id,
            'id' => $this->class_schedule_id ?? $this->id,
            'title' => $this->title,
            'course' => $this->course->title,
            'mentor' => $this->mentor?->name,
            'class_url' => $this->class_url,
            'schedule_datetime' => $this->schedule_datetime,
            'has_started' => $this->has_started ? true : false,
            // 'has_started' => $this->has_started || $this->schedule_datetime <= now()->format('Y-m-d H:i:s'),
            'has_completed' => $this->has_completed ? true : false,
            'start_time' => date('H:i:s', strtotime($this->start_time)),
            'end_time' => date('H:i:s', strtotime($this->end_time)),
            'duration' => (int) ((strtotime($this->end_time) - strtotime($this->start_time)) / 60),
            'student_end_time' => $this->student_end_time,
            'is_active' => $this->is_active ? true : false,
        ];
    }
}
