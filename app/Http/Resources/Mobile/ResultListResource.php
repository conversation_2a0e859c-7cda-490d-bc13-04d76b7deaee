<?php

namespace App\Http\Resources\Mobile;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ResultListResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'user_id' => $this->user_id,
            'chapter_quiz_id' => $this->chapter_quiz_id,
            'quiz_title' => $this->quiz->title,
            'total_mark' => $this->quiz->total_mark,
            'course_id' => $this->course_id,
            'course_title' => $this->course->title,
            'submission_status' => $this->submission_status,
            'mark' => $this->mark,
            'written_mark_given' => $this->writtenMarks->marks_givenby_id ? $this->writtenMarks->mark : null,
            'written_mark' => $this->quiz->writtenQuestions?->marks,
            'positive_count' => $this->positive_count,
            'negetive_count' => $this->negetive_count,
            'end_time' => $this->end_time,
            'submitted_at' => $this->submitted_at,
            'deleted_at' => $this->deleted_at,
            'created_by' => $this->created_by,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
