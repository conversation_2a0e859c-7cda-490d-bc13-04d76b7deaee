<?php

namespace App\Http\Resources\Mobile\Student;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DetailsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'user_id' => $this->user_id,
            'name' => $this->name,
            'email' => $this->email,
            'contact_no' => $this->contact_no,
            'student_id' => $this->student_code,
            'alternative_contact_no' => $this->alternative_contact_no,
            'gender' => $this->gender,
            'blood_group' => $this->blood_group,
            'bio' => $this->bio,
            'father_name' => $this->father_name,
            'mother_name' => $this->mother_name,
            'religion' => $this->religion,
            'marital_status' => $this->marital_status,
            'date_of_birth' => $this->date_of_birth,
            'current_address' => $this->current_address,
            'permanent_address' => $this->permanent_address,
            'interests' => $this->interests,
            'nid_no' => $this->nid_no,
            'birth_certificate_no' => $this->birth_certificate_no,
            'passport_no' => $this->passport_no,
            'image' => $this->image,
            'status' => $this->status,
            'education' => $this->education,
            'institute' => $this->institute,
        ];
    }
}
