<?php

namespace App\Http\Resources\Mobile;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use App\Http\Resources\Mobile\Course\ListResource;
use App\Http\Controllers\Mobile\CourseController;
class CategoryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $courseController = new CourseController($request);
        return [
            'id' => $this->id,
            'name' => $this->name,
            'icon' => $this->icon,
            'headline' => $this->headline,
            'bought_items' => $courseController->getPurchasedCourseIds(),
            'courses' => ListResource::collection($this->courses->where('is_active', 1)->take(4)),
        ];
    }
}
