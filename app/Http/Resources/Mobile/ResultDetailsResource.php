<?php

namespace App\Http\Resources\Mobile;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use App\Http\Resources\Mobile\ResultAnswerResource;
class ResultDetailsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'organization_id' => $this->organization_id,
            'user_id' => $this->user_id,
            'chapter_quiz_id' => $this->chapter_quiz_id,
            'course_id' => $this->course_id,
            'submission_status' => $this->submission_status,
            'mark' => $this->mark,
            'positive_count' => $this->positive_count,
            'negetive_count' => $this->negetive_count,
            'end_time' => $this->end_time,
            'submitted_at' => $this->submitted_at,
            'deleted_at' => $this->deleted_at,
            'created_by' => $this->created_by,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'quiz' => $this->quiz,
            'answers' => ResultAnswerResource::collection($this->answers),
        ];
    }
}
