<?php

namespace App\Http\Resources\Mobile;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Auth;
use App\Models\VideoWatchLog;
use App\Models\ScriptViewLog;
use App\Models\ChapterQuizResult;
use App\Models\AssignmentSubmission;
use App\Models\StudentJoinHistory;
use App\Models\StudentInformation;
class CertificateListResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {

        $user = Auth::user();

        $totalItemsNumber = $this->course?->courseOutline->count() + $this->course->assignments->count() + $this->course->liveClasses->count();
        $videoCount = VideoWatchLog::where('course_id', $this->course->id)->where('user_id', Auth::user()->id)->where('is_watched', 1)->count();
        $scriptCount = ScriptViewLog::where('course_id', $this->course->id)->where('user_id', Auth::user()->id)->where('is_watched', 1)->count();

        $quizParticipationNumber = $this->course->chapterQuizResults->where('user_id', $user->id)->where('submission_status', 'Submitted')->unique('chapter_quiz_id')->count();
        $completedItemsNumber = $videoCount + $scriptCount + $quizParticipationNumber;



        $student = StudentInformation::where('user_id', $user->id)->first();



        $uniqueAssignmentCount = AssignmentSubmission::where('student_id', $student->id)
                                ->where('course_id', $this->course->id)
                                ->distinct('assignment_id') // Assuming 'assignment_id' identifies the assignment uniquely
                                ->count('assignment_id');

        $classJoinCount = StudentJoinHistory::where('student_join_histories.student_id', $student->id)
        ->join('class_schedules', 'class_schedules.id', '=', 'student_join_histories.class_schedule_id')
        ->where('class_schedules.course_id', $this->course_id)
        ->distinct('class_schedule_id')
        ->count('class_schedule_id');


        $completedItemsNumber = $completedItemsNumber + $uniqueAssignmentCount + $classJoinCount;



        $progress = (int) $totalItemsNumber ? ($completedItemsNumber / $totalItemsNumber) * 100 : 0;



        $data = [
            'id' => $this->id,
            'course_id' => $this->item_id,
            'organization' => $this->organization->name,
            // 'course' => $this->course,
            'course_name' => $this->course->title,
            'candidate_name' => $request->user()->name,
            // 'certificate' => $this->course->certificate,
            'background_image' => $this->course->certificateTemplate?->background_image,
            'logo' => $this->course->certificateTemplate?->logo,
            'certification_text' => $this->course->certificateTemplate?->certification_text,
            'signature' => $this->course->certificateTemplate?->signature,
            'authorize_person' => $this->course->certificateTemplate?->authorize_person,
            'designation' => $this->course->certificateTemplate?->designation,
            "progress" => $progress,
            'generated_certificate' => new UserCertificateResource($this->course->generatedCertificates->where('user_id', Auth::user()->id)->first()),

        ];

        return $data;
    }
}

