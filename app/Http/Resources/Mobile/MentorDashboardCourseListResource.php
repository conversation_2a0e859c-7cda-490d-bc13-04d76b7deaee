<?php

namespace App\Http\Resources\Mobile;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class MentorDashboardCourseListResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->course_id,
            'title' => $this->course->title,
            'thumbnail' => $this->course->thumbnail,
            'assignment_count' => $this->course->assignments->count(),
            'live_class_count' => $this->course->liveClasses->count(),
            'total_exam' => $this->course->courseOutline->whereNotNull('chapter_quiz_id')->count(),
            'total_video' => $this->course->courseOutline->whereNotNull('chapter_video_id')->count(),
            'total_script' => $this->course->courseOutline->whereNotNull('chapter_script_id')->count(),
        ];
    }
}
