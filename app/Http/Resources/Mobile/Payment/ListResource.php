<?php

namespace App\Http\Resources\Mobile\Payment;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ListResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'organization_id' => $this->organization_id,
            'user_id' => $this->user_id,
            'course_id' => $this->item_id,
            'course_name' => $this->course,
            // 'course_name' => $this->course->title,
            'promo_id' => $this->promo_id,
            'payable_amount' => $this->payable_amount,
            'paid_amount' => $this->paid_amount,
            'discount_amount' => $this->discount_amount,
            'currency' => $this->currency,
            'invoice_id' => $this->transaction_id,
            'payment_type' => $this->payment_type,
            'payment_method' => $this->payment_method,
            'status' => $this->status,
            'enrollment_date' => $this->created_at,
        ];
    }
}
