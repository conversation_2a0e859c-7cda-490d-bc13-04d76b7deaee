<?php

namespace App\Http\Resources\Mobile;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use App\Http\Resources\Mobile\Course\OutlineResource;
use App\Http\Resources\Mobile\Content\ScriptDetailsResource;
use App\Http\Resources\Mobile\Content\QuizDetailsResource;
use App\Models\VideoWatchLog;
use App\Models\ScriptViewLog;
use App\Models\ChapterQuizResult;

class OtherContentsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $data = [
            'id' => $this->id,
            'organization_id' => $this->organization_id,
            'title' => $this->title,
            'type' => $this->chapter_script_id ? 'script' : ($this->chapter_video_id ? 'video' : 'quiz'),
            'script' => null,
            'video' => $this->chapter_video_id ? $this->video : null,
            'quiz' =>  null,
            'is_free' => $this->is_free ? true : false,
            'is_active' =>  true,
            "is_completed" => false
        ];

        $isCompleted = false;
        $user = auth('sanctum')->user();

        if ($user) {

            if ($this->chapter_video_id) {
                $videoLog =VideoWatchLog::where('user_id', $user->id)->where('course_outline_id', $this->id)->where('is_watched', 1)->first();
                $isCompleted = $videoLog ? true : false;
            }
            if ($this->chapter_script_id) {
                $scriptLog =ScriptViewLog::where('user_id', $user->id)->where('course_outline_id', $this->id)->where('is_watched', 1)->first();
                $isCompleted = $scriptLog ? true : false;
            }
            if ($this->chapter_quiz_id) {
                $quizLog =ChapterQuizResult::where('user_id', $user->id)->where('chapter_quiz_id', $this->chapter_quiz_id)->where('submission_status', 'Submitted')->first();
                $isCompleted = $quizLog ? true : false;
            }
            // $videoLog =VideoWatchLog::where('user_id', $user->id)->where('course_outline_id', $this->id)->first();
            $data['is_completed'] = $isCompleted;

            $previous = $this->previous ? new OutlineResource($this->previous) : null;
            if ($previous) {
                $data['is_locked'] = !$previous->is_completed;

            } else {
                $data['is_locked'] = false;
            }

            if ($data['is_free']) {
                $data['is_locked'] = false;
            }
        }
        return $data;
    }
}
