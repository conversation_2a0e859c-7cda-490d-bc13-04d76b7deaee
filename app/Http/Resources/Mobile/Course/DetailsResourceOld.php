<?php

namespace App\Http\Resources\Mobile\Course;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use App\Http\Resources\Mobile\Course\ListResource;
use App\Http\Resources\Mobile\Course\CategoryResource;
use App\Http\Resources\Mobile\Course\RatingResource;
use App\Http\Resources\Mobile\ClassSheduleResource;
use App\Http\Resources\Mobile\Mentor\ListResource as MentorListResource;
use App\Models\Payment;
use App\Models\StudentInformation;
use App\Models\ClassSchedule;
use App\Models\StudentAssignment;
use App\Models\AssignmentSubmission;
use App\Models\StudentJoinHistory;
use DateTime;
use DateTimeZone;

use App\Http\Controllers\Mobile\ContentWatchLogController;

class DetailsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        // $salePrice = $this->sale_price ?? $this->regular_price;
        // $salePriceIfDiscounted = $this->discount_percentage ? $salePrice - ($salePrice * $this->discount_percentage / 100) : $salePrice;
        $data = [
                "id" => $this->id,
                "category_id" => $this->category_id,
                "category_name" => $this->category->name,
                "title" => $this->title,
                "title_bn" => $this->title_bn,
                "description" => $this->description,
                "thumbnail" => $this->thumbnail,
                "trailer_video" => $this->youtube_url ? explode('=', $this->youtube_url)[1] : null,
                "number_of_enrolled" => $this->number_of_enrolled,
                "regular_price" => $this->regular_price,
                "sale_price" => $this->sale_price,
                "minimum_enroll_amount" => $this->minimum_enroll_amount,
                "installment_type" => $this->installment_type,
                "monthly_amount" => $this->monthly_amount,
                "max_installment_qty" => $this->max_installment_qty,
                "discount_percentage" => $this->discount_percentage,
                "has_life_coach" => $this->has_life_coach,
                "is_free" => $this->is_free,
                "is_active" => $this->is_active,
                "is_featured" => $this->is_featured,
                "created_by" => $this->created_by,
                "created_at" => $this->created_at->format("Y-m-d H:i:s"),
                "updated_at" => $this->updated_at->format("Y-m-d H:i:s"),
                "subjects" => SubjectListResource::collection($this->subjects),
                "outlines" => CategoryResource::collection($this->courseCategory->whereNull('subject_id')),
                "features" => $this->courseFeature->select('id', 'title', 'title_bn', 'icon'),
                "prerequisites" => $this->coursePrerequisites->select('id', 'title', 'title_bn', 'icon'),
                "learning_outcomes" => $this->courseLearningItems->select('id', 'title', 'title_bn', 'icon'),
                "mentors" => MentorListResource::collection($this->courseMentor),
                "faqs" => $this->courseFaq->select('id', 'title', 'answer'),
                'related_courses' => ListResource::collection($this->relatedCourses),
                "rating" => $this->rating,
                "rating_count" => $this->courseRatings->count(),
                "rating_list" => RatingResource::collection($this->courseRatings->take(4))
            ];

        $user = auth('sanctum')->user();
        if ($user?->user_type == 'Student') {
        $data['is_enrolled'] = false;
        $data['is_enrolled_pending'] = false;
        $payment = Payment::where('user_id', $user->id)->where('item_id', $this->id)->first();
            if (!empty($payment)) {

            if ($payment?->is_approved == 1) {

                $contentController = new ContentWatchLogController($request);
                $data['latest_activities'] = $contentController->getCourseLastAccessedContent($user, $this->id);

                $student = StudentInformation::where('user_id', $user->id)->first();

                $totalItemsNumber = $this->courseOutline->count() + $this->assignments->count() + $this->liveClasses->count();

                $quizParticipationNumber = $this->chapterQuizResults->where('user_id', $user->id)->where('submission_status', 'Submitted')->unique('chapter_quiz_id')->count();

                $uniqueAssignmentCount = AssignmentSubmission::where('student_id', $student->id)
                                        ->where('course_id', $this->id)
                                        ->distinct('assignment_id') 
                                        ->count('assignment_id');

                $classJoinCount = StudentJoinHistory::where('student_id', $student->id)
                ->distinct('class_schedule_id')
                ->count('assignment_id');


                $completedItemsNumber = $this->videoWatchLogs->where('user_id', $user->id)->where('is_watched', 1)->count() +
                $this->scriptViewLogs->where('user_id', $user->id)->where('is_watched', 1)->count() +
                $quizParticipationNumber + $uniqueAssignmentCount + $classJoinCount;



                $data['progress'] = (int) $totalItemsNumber ? ($completedItemsNumber / $totalItemsNumber) * 100 : 0;


                $data['is_enrolled'] = true;

                $data['class_schedules'] = $this->classSchedules;


                $ongoingClass = ClassSchedule::join('class_schedule_students', 'class_schedules.id', '=', 'class_schedule_students.class_schedule_id')
                    ->where('class_schedule_students.student_id', $student->id)
                    ->where('class_schedules.course_id', $this->id)
                    ->where('class_schedules.schedule_datetime', '>=', now()->format('Y-m-d H:i:s'))
                    ->first();

                $data['ongoing_class'] = new ClassSheduleResource($ongoingClass);


                $classList = ClassSchedule::join('class_schedule_students', 'class_schedules.id', '=', 'class_schedule_students.class_schedule_id')
                    ->where('class_schedule_students.student_id', $student->id)
                    ->where('class_schedules.course_id', $this->id)
                    ->get();

                $data['class_schedules'] = ClassSheduleResource::collection($classList);


                $assignments = StudentAssignment::select(
                    'assignments.*'

                )
                ->where('student_assignments.student_id', $student->id)
                ->where('assignments.status', '!=', 'Unpublished')
                ->where('assignments.course_id', $this->id)
                ->leftJoin('assignments', 'assignments.id', 'student_assignments.assignment_id')
                // ->where('assignments.publish_date', '>=', now()->format('Y-m-d H:i:s'))
                ->limit(1)
                ->get();
                foreach ($assignments as $key => $assignment) {
                    $assignment->is_active = $assignment->is_active ? true : false;

                    if ($assignment) {
                        if ($assignment->publish_date) {
                            $assignment->publish_date = date('Y-m-d H:i:s', strtotime('+6 hours', strtotime($assignment->publish_date)));
                            $assignment->publish_date = (new DateTime($assignment->publish_date))->setTimezone(new DateTimeZone('UTC'))->format('Y-m-d\TH:i:s.u\Z');
                        }

                        if ($assignment->deadline) {
                            $assignment->deadline = date('Y-m-d H:i:s', strtotime('+6 hours', strtotime($assignment->deadline)));
                            $assignment->deadline = (new DateTime($assignment->deadline))->setTimezone(new DateTimeZone('UTC'))->format('Y-m-d\TH:i:s.u\Z');
                        }
                    }
                }

                $data['assignments'] = $assignments;
            } else {
                $data['is_enrolled_pending'] = true;
            }
        }

        } else {

            $classList = ClassSchedule::where('class_schedules.course_id', $this->id)
            ->get();

        $data['class_schedules'] = ClassSheduleResource::collection($classList);
        }
        return $data;

    }
}
