<?php

namespace App\Http\Resources\Mobile\Course;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Laravel\Sanctum\PersonalAccessToken;

use App\Models\VideoWatchLog;
use App\Models\ScriptViewLog;
use App\Models\ChapterQuizResult;

class OutlineResource extends JsonResource
{
    protected array $completedItems = [];
    protected array $allOutlines = [];
    protected bool $isEnrolled = false;

    public function withCompletedItems(array $items): self
    {
        $this->completedItems = $items;
        return $this;
    }

    public function withAllOutlines(array $outlines): self
    {
        $this->allOutlines = $outlines;
        return $this;
    }

    public function withEnrollmentStatus(bool $isEnrolled): self
    {
        $this->isEnrolled = $isEnrolled;
        return $this;
    }

    public function toArray(Request $request): array
    {
        $id = $this->chapter_script_id ?? $this->chapter_video_id ?? $this->chapter_quiz_id;
        $type = $this->chapter_script_id ? 'script' : ($this->chapter_video_id ? 'video' : 'quiz');
        $key = $type . '_' . $id;

        $isCompleted = in_array($this->id, $this->completedItems);

        // Default locked status - for non-enrolled users or general case
        $isLocked = true;

        if ($this->isEnrolled) {
            // Find the position of this outline in all outlines
            $currentIndex = array_search($this->id, array_column($this->allOutlines, 'id'));

            if ($currentIndex === 0) {
                // First content is always unlocked for enrolled users
                $isLocked = false;
            } elseif ($currentIndex !== false && $currentIndex > 0) {
                // Check if previous content is completed
                $previousOutlineId = $this->allOutlines[$currentIndex - 1]['id'] ?? null;
                $isPreviousCompleted = $previousOutlineId ? in_array($previousOutlineId, $this->completedItems) : false;

                // Unlock if previous content is completed
                $isLocked = false; // !$isPreviousCompleted;
            }
        }

        $data = [
            'id' => $this->id,
            'organization_id' => $this->organization_id,
            'title' => $this->title,
            'title_bn' => $this->title_bn,
            'course_id' => $this->course_id,
            'type' => $type,
            'element_id' => $id,
            'is_free' => $this->is_free,
            'is_active' => true,
            'is_locked' => $this->is_free ? false : ($isCompleted ? false : $isLocked), // Free content is never locked
            'is_completed' => $isCompleted,
        ];

        return $data;
    }
}
