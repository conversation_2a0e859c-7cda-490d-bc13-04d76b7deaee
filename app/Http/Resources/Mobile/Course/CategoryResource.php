<?php

namespace App\Http\Resources\Mobile\Course;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CategoryResource extends JsonResource
{
    protected array $completedItems = [];
    protected bool $isEnrolled = false;

    public function withCompletedItems(array $items): self
    {
        $this->completedItems = $items;
        return $this;
    }

    public function withEnrollmentStatus(bool $isEnrolled): self
    {
        $this->isEnrolled = $isEnrolled;
        return $this;
    }

    /**
     * Check if user is enrolled in the course
     */


    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $user = auth('sanctum')->user();
        $courseId = $this->course_id ?? $this->id; // Adjust based on your relationship

        // Get all outlines ordered by their sequence/order
        $outlines = $this->courseOutlines->sortBy('order')->values();

        $outlineResources = $outlines->map(function ($outline, $index) use ($user, $courseId, $outlines) {
            $isEnrolled = $this->isEnrolled;
            $isFirstItem = $index === 0;
            $isPreviousCompleted = false;

            if ($index > 0) {
                $previousOutlineId = $outlines[$index - 1]->id;
                $isPreviousCompleted = in_array($previousOutlineId, $this->completedItems);
            }

            $isLocked = !$outline->is_free && (
                !$isEnrolled || ($index > 0 && !$isPreviousCompleted)
            );

            if ($isEnrolled && $isFirstItem) {
                $isLocked = false;
            }

            return (new OutlineResource($outline))
                ->withCompletedItems($this->completedItems)
                ->additional([
                    'is_locked' => $isLocked,
                    'is_completed' => in_array($outline->id, $this->completedItems),
                ]);
        });

        // Calculate completion status for the category
        $completedCount = $outlines->filter(function ($outline) {
            return in_array($outline->id, $this->completedItems);
        })->count();

        $data = [
            "id" => $this->id,
            "name" => $this->name,
            "name_bn" => $this->name_bn,
            "is_active" => $this->is_active,
            "videos_number" => $this->courseOutlines->whereNotNull('chapter_video_id')->count(),
            "scripts_number" => $this->courseOutlines->whereNotNull('chapter_script_id')->count(),
            "quizs_number" => $this->courseOutlines->whereNotNull('chapter_quiz_id')->count(),
            "total_items" => $outlines->count(),
            "completed_items" => $completedCount,
            "progress_percentage" => $outlines->count() > 0
                ? round(($completedCount / $outlines->count()) * 100)
                : 0,
            "is_completed" => $completedCount === $outlines->count(),
            "contents" => $outlineResources,
        ];

        return $data;
    }
}
