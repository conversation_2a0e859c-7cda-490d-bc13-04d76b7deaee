<?php

namespace App\Http\Resources\Mobile\Course;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use App\Http\Resources\Mobile\Mentor\ListResource as MentorListResource;
use Auth;

use App\Models\VideoWatchLog;
use App\Models\ScriptViewLog;
use App\Models\ChapterQuizResult;
use App\Models\AssignmentSubmission;
use App\Models\StudentJoinHistory;
use App\Models\StudentInformation;

class MyCourseListResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $user = Auth::user();
        $student = StudentInformation::where('user_id', $user->id)->first();

        $totalItemsNumber = $this->courseOutline->count() + $this->assignments->count() + $this->liveClasses->count();




        $totalItemsNumber = $this->courseOutline->count() + $this->assignments->count() + $this->liveClasses->count();

        $quizParticipationNumber = $this->chapterQuizResults->where('user_id', $user->id)->where('submission_status', 'Submitted')->unique('chapter_quiz_id')->count();

        $uniqueAssignmentCount = AssignmentSubmission::where('student_id', $student->id)
                                ->where('course_id', $this->id)
                                ->distinct('assignment_id') // Assuming 'assignment_id' identifies the assignment uniquely
                                ->count('assignment_id');


        $classJoinCount = StudentJoinHistory::where('student_join_histories.student_id', $student->id)
        ->join('class_schedules', 'class_schedules.id', '=', 'student_join_histories.class_schedule_id')
        ->where('class_schedules.course_id', $this->id)
        ->distinct('class_schedule_id')
        ->count('class_schedule_id');

        $completedItemsNumber = $this->videoWatchLogs->where('user_id', $user->id)->where('is_watched', 1)->count() +
        $this->scriptViewLogs->where('user_id', $user->id)->where('is_watched', 1)->count() +
        $quizParticipationNumber + $uniqueAssignmentCount + $classJoinCount;



        $progress = (int) $totalItemsNumber ? ($completedItemsNumber / $totalItemsNumber) * 100 : 0;




        return [
            "id" => $this->id,
            // "organization_id" => $this->organization_id,
            "title" => $this->title,
            // "title_bn" => $this->title_bn,
            // "description" => $this->description,
            "thumbnail" => $this->thumbnail,
            // "rating" => $this->rating,
            // "progress" => (int) $totalItemsNumber ? ($completedItemsNumber / $totalItemsNumber) * 100 : 0,
            "progress" => $progress,
        ];
    }
}

