<?php

namespace App\Http\Resources\Mobile\Course;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class RatingResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'organization_id' => $this->organization_id,
            'course_id' => $this->course_id,
            'user_id' => $this->user_id,
            'name' => $this->user ? $this->user->name : null,
            'image' => $this->user ? $this->user->image : null,
            'rating' => $this->rating,
            'review' => $this->review,
            'created_at' => $this->created_at
        ];
    }
}
