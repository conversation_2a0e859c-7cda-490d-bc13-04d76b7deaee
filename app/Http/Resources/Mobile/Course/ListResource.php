<?php

namespace App\Http\Resources\Mobile\Course;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use App\Http\Resources\Mobile\Mentor\ListResource as MentorListResource;
class ListResource extends JsonResource
{


    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            "id" => $this->id,
            "organization_id" => $this->organization_id,
            "title" => $this->title,
            "title_bn" => $this->title_bn,
            "description" => $this->description,
            "thumbnail" => $this->thumbnail,
            "regular_price" => $this->regular_price,
            "sale_price" => $this->sale_price,
            "show_price" => $this->show_price ? true : false,
            "currency" => $this->currency,
            "discount_percentage" => $this->discount_percentage,
            "minimum_enroll_amount" => $this->minimum_enroll_amount,
            "installment_type" => $this->installment_type,
            "monthly_amount" => $this->monthly_amount,
            "max_installment_qty" => $this->max_installment_qty,
            "rating" => $this->rating,
            "is_free" => $this->is_free,
            "slug" => $this->slug,
            "is_featured" => $this->is_featured,
            "course_duration" => $this->course_duration,
            "duration_per_day" => $this->duration_per_day,
            "mentors" => count($this->courseMentor) > 0 ? MentorListResource::collection($this->courseMentor): [],
            "organization_name" => $this->organization?->name,
        ];
    }
}
