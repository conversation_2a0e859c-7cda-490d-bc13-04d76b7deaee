<?php

namespace App\Http\Resources\Mobile\Course;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use App\Http\Resources\Mobile\Course\ListResource;
use App\Http\Resources\Mobile\Course\CategoryResource;
use App\Http\Resources\Mobile\Course\RatingResource;
use App\Http\Resources\Mobile\ClassSheduleResource;
use App\Http\Resources\Mobile\Mentor\ListResource as MentorListResource;
use App\Models\Payment;
use App\Models\StudentInformation;
use App\Models\ClassSchedule;
use App\Models\StudentAssignment;
use DateTime;
use DateTimeZone;

class MentorDetailsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $salePrice = $this->sale_price ?? $this->regular_price;
        $salePriceIfDiscounted = $this->discount_percentage ? $salePrice - ($salePrice * $this->discount_percentage / 100) : $salePrice;
        return [
                "id" => $this->id,
                "title" => $this->title,
                "title_bn" => $this->title_bn,
                "description" => $this->description,
                "thumbnail" => $this->thumbnail,
                "trailer_video" => $this->youtube_url ? explode('=', $this->youtube_url)[1] : null,
                "number_of_enrolled" => $this->number_of_enrolled,
                "regular_price" => $this->regular_price,
                "sale_price" => $salePriceIfDiscounted,
                "discount_percentage" => $this->discount_percentage,
                "has_life_coach" => $this->has_life_coach,
                "is_free" => $this->is_free,
                "is_active" => $this->is_active,
                "is_featured" => $this->is_featured,
                "created_by" => $this->created_by,
                "created_at" => $this->created_at->format("Y-m-d H:i:s"),
                "updated_at" => $this->updated_at->format("Y-m-d H:i:s"),
                // "outlines" => CategoryResource::collection($this->courseCategory),
                "features" => $this->courseFeature->select('id', 'title', 'title_bn', 'icon'),
                "prerequisites" => $this->coursePrerequisites->select('id', 'title', 'title_bn', 'icon'),
                "learning_outcomes" => $this->courseLearningItems->select('id', 'title', 'title_bn', 'icon'),
                "mentors" => MentorListResource::collection($this->courseMentor),
                "faqs" => $this->courseFaq->select('id', 'title', 'answer'),
                // 'related_courses' => ListResource::collection($this->relatedCourses),
                "rating" => $this->rating,
                "rating_count" => $this->courseRatings->count(),
                "rating_list" => RatingResource::collection($this->courseRatings->take(4))
            ];

    }
}
