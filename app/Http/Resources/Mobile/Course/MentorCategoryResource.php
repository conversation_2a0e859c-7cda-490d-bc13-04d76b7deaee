<?php

namespace App\Http\Resources\Mobile\Course;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use App\Http\Resources\Mobile\Course\MentorOutlinesResource;

class MentorCategoryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $data = [
            "id" => $this->id,
            "name" => $this->name,
            "course_title" => $this->course->title,
            "is_active" => $this->is_active,
            "videos_number" => $this->courseOutlines->whereNotNull('chapter_video_id')->count(),
            "scripts_number" => $this->courseOutlines->whereNotNull('chapter_script_id')->count(),
            "quizs_number" => $this->courseOutlines->whereNotNull('chapter_quiz_id')->count(),
            "contents" => MentorOutlinesResource::collection($this->courseOutlines)
        ];

        return $data;
    }
}
