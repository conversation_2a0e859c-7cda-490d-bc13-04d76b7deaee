<?php

namespace App\Http\Resources\Mobile\Course;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SubjectListResource extends JsonResource
{
    protected $extra = [];

    public function withExtras(array $data)
    {
        $this->extra = $data;
        return $this;
    }

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'name' => $this->name,
            'name_bn' => $this->name_bn,
            'subject_code' => $this->subject_code,
            'course_id' => $this->course_id,
            'icon' => $this->icon,
            'color_code' => $this->color_code,
            "outlines" => $this->outlineList() //CategoryResource::collection($this->courseCategory),
        ];
    }

    public function outlineList()
    {
        return $this->resource->courseCategory->map(function ($category) {
            return (new CategoryResource($category))
                ->withCompletedItems($this->extra['completedItems'] ?? []);
        });
    }
}
