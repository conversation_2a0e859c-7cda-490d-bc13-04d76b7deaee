<?php

namespace App\Http\Resources\Mobile;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class MentorOutlineQuizDetailsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
                "id" => $this->quiz->id,
                "organization_id" => $this->quiz->organization_id,
                "title" => $this->quiz->title,
                "description" => $this->quiz->description,
                "course_id" => $this->quiz->course_id,
                "duration" => $this->quiz->duration,
                "positive_mark" => $this->quiz->positive_mark,
                "negative_mark" => $this->quiz->negative_mark,
                "total_mark" => $this->quiz->total_mark,
                "number_of_question" => $this->quiz->number_of_question,
                "sufficient_question" => $this->quiz->sufficient_question,
                "questions" => $this->quiz->questions,
                "is_free" => $this->quiz->is_free ? true : false,
                "sequence" => $this->quiz->sequence,
                "is_active" => $this->quiz->is_active ? true : false,
                "created_by" => $this->quiz->created_by,
                "created_at" => $this->quiz->created_at,
                "updated_at" => $this->quiz->updated_at,
        ];
    }
}
