<?php

namespace App\Http\Resources\Mobile;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class MentorDetailsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'organization' => $this->organization->name,
            'user_id' => $this->user_id,
            'name' => $this->name,
            'username' => $this->username,
            'email' => $this->email,
            'contact_no' => $this->contact_no,
            'mentor_code' => $this->mentor_code,
            'referral_code' => $this->referral_code,
            'education' => $this->education,
            'institute' => $this->institute,
            'referred_code' => $this->referred_code,
            'alternative_contact_no' => $this->alternative_contact_no,
            'gender' => $this->gender,
            'blood_group' => $this->blood_group,
            'bio' => $this->bio,
            'father_name' => $this->father_name,
            'mother_name' => $this->mother_name,
            'religion' => $this->religion,
            'marital_status' => $this->marital_status,
            'date_of_birth' => $this->date_of_birth,
            'profession' => $this->profession,
            'current_address' => $this->current_address,
            'permanent_address' => $this->permanent_address,
            'nid_no' => $this->nid_no,
            'birth_certificate_no' => $this->birth_certificate_no,
            'passport_no' => $this->passport_no,
            'image' => $this->image,
            'intro_video' => explode('=', $this->intro_video)[1] ?? '',
            'status' => $this->status,
            // 'is_foreigner' => $this->is_foreigner,
            // 'is_life_couch' => $this->is_life_couch,
            'is_host_staff' => $this->is_host_staff ? true : false,
            'is_host_certified' => $this->is_host_certified ? true : false,
            'is_active' => $this->is_active ? true : false,
            'rating' => $this->rating,
            'approval_date' => $this->approval_date,
            'host_rank_number' => $this->host_rank_number,
            'is_featured' => $this->is_featured ? true : false,
            'live_class_count' => $this->totalLiveClasses->count(),
            'assign_course_count' => $this->courses->count(),
            'assignment_count' => $this->totalAssignments->count(),
        ];
    }
}



