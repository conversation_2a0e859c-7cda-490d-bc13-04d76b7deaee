<?php

namespace App\Http\Resources\Mobile\Dashboard;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class LastOutlineResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'course_category_id' => $this->course_category_id,
            'module_name' => $this->courseCategory->name,
            'course_name' => $this->course->title,
            'organization_id' => $this->organization_id,
            'title' => $this->title,
            'title_bn' => $this->title_bn,
            'course_id' => $this->course_id,
            'type' => $this->chapter_script_id ? 'script' : ($this->chapter_video_id ? 'video' : 'quiz'),
            'element_id' => $this->chapter_script_id ?? $this->chapter_video_id ?? $this->chapter_quiz_id,
            'is_free' => $this->is_free,
            'is_active' => $this->is_active
        ];
    }
}
