<?php

namespace App\Http\Resources\Mobile;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use App\Http\Resources\Mobile\Course\OutlineResource;

class VideoDetailsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $data = [
            'id' => $this->id,
            'organization_id' => $this->organization_id,
            'title' => $this->title,
            'title_bn' => $this->title_bn,
            'course_id' => $this->course_id,
            'type' => $this->chapter_script_id ? 'script' : ($this->chapter_video_id ? 'video' : 'quiz'),
            'script' => $this->chapter_script_id ? new ScriptDetailsResource($this->script) : null,
            'video' => $this->chapter_video_id ? $this->video : null,
            'quiz' => $this->chapter_quiz_id ? new QuizDetailsResource($this->quiz) : null,
            'element_id' => $this->chapter_script_id ?? $this->chapter_video_id ?? $this->chapter_quiz_id,
            'is_free' => $this->is_free ? true : false,
            'is_active' => $this->is_active ? true : false
        ];

        $data['video']->youtube_url = $this->video->youtube_url ? explode('=', $this->video->youtube_url)[1] : null;
        // $data['video']->youtube_url = $this->video->youtube_url;
        $data['video']->duration = (int) $this->video->duration;

        $contents = [ [
            'id' => $this->id,
            'organization_id' => $this->organization_id,
            'title' => $this->title,
            'course_id' => $this->course_id,
            'type' => $this->chapter_script_id ? 'script' : ($this->chapter_video_id ? 'video' : 'quiz'),
            'script' => null,
            'video' => $this->chapter_video_id ? $this->video : null,
            'quiz' => null,
            'element_id' => $this->chapter_script_id ?? $this->chapter_video_id ?? $this->chapter_quiz_id,
            'is_active' => true,
            'is_completed' => false,
            'is_locked' => false
        ]];


        $others = OtherContentsResource::collection($this->otherContents);

        foreach ($others as $key => $value) {
            if ($value?->video?->duration) {
                $value->video->youtube_url = $value->video->youtube_url ? explode('=', $value->video->youtube_url)[1] : null;
                $value->video->duration = (int) $value->video->duration;
            }
            $contents[] = $value;
        }

        $data['all_contents'] = $contents;

        return $data;
        // 'next_contents' => OtherContentsResource::collection($this->otherContents),
    }
}
