<?php

namespace App\Http\Resources\Mobile;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use DateTime;
use DateTimeZone;
use Carbon\Carbon;
class MentorClassDetailsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $current_time = (new DateTime('now', new DateTimeZone('UTC')))->modify('+6 hours')->format('Y-m-d\TH:i:s\Z');

        $scheduleDatetime = Carbon::parse($this->schedule_datetime);
        $currentTime = Carbon::parse($current_time);
        return [
            'id' => $this->id,
            'title' => $this->title,
            'course_id' => $this->course_id,
            'course' => $this->course->title,
            'class_url' => $this->class_url,
            'schedule_datetime' => $this->schedule_datetime,
            // 'schedule_datetime' => (new DateTime($this->schedule_datetime))->setTimezone(new DateTimeZone('UTC'))->format('Y-m-d\TH:i:s\Z'),
            'current_time' => $current_time,
            'needs_to_start' => $currentTime->greaterThanOrEqualTo($scheduleDatetime) ? true : false,
            'has_started' => $this->has_started ? true : false,
            'has_completed' => $this->has_completed ? true : false,
            'start_time' => date('H:i:s', strtotime($this->start_time)),
            'end_time' => date('H:i:s', strtotime($this->end_time)),
            'duration' => (int) ((strtotime($this->end_time) - strtotime($this->start_time)) / 60),
            'student_end_time' => $this->student_end_time,
            'is_active' => $this->is_active ? true : false,
            'students' => LiveClassStudentResource::collection($this->students)
        ];
    }
}

