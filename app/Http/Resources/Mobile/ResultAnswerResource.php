<?php

namespace App\Http\Resources\Mobile;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ResultAnswerResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'question_id' => $this->question_id,
            'answer1' => (bool) $this->answer1,
            'answer2' => (bool) $this->answer2,
            'answer3' => (bool) $this->answer3,
            'answer4' => (bool) $this->answer4,
            'is_correct' => (bool) $this->is_correct,
            'question_text' => $this->question_text,
            'question_text_bn' => $this->question_text_bn,
            'question_image' => $this->question_image,
            'option1' => $this->option1,
            'option2' => $this->option2,
            'option3' => $this->option3,
            'option4' => $this->option4,
            'option1_image' => $this->option1_image,
            'option2_image' => $this->option2_image,
            'option3_image' => $this->option3_image,
            'option4_image' => $this->option4_image,
            'set_answer1' => (bool) $this->set_answer1,
            'set_answer2' => (bool) $this->set_answer2,
            'set_answer3' => (bool) $this->set_answer3,
            'set_answer4' => (bool) $this->set_answer4,
        ];
    }
}
