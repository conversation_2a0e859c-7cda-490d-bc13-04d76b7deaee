<?php

namespace App\Http\Resources\Website;


use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use App\Http\Resources\Mobile\Mentor\ListResource as MentorListResource;
class CourseListResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {

        return [
            "id" => $this->id,
            "organization_id" => $this->organization_id,
            "title" => $this->title,
            "title_bn" => $this->title_bn,
            "description" => $this->description,
            "thumbnail" => $this->thumbnail,
            "regular_price" => $this->regular_price,
            "sale_price" => $this->sale_price,
            "currency" => $this->currency,
            "show_price" => $this->show_price ? true : false,
            "discount_percentage" => $this->discount_percentage,
            "rating" => $this->rating,
            "is_free" => $this->is_free,
            "is_featured" => $this->is_featured,
            "course_duration" => $this->course_duration,
            "duration_per_day" => $this->duration_per_day,
            "mentors" => MentorListResource::collection($this->courseMentor),
            "organization_name" => $this->organization->name
        ];
    }
}
