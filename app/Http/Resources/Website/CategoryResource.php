<?php

namespace App\Http\Resources\Website;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CategoryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'icon' => $this->icon,
            'headline' => $this->headline,
            // 'sub_categories' => SubCategoryResource::collection($this->subCategories),
            'sub_categories' => SubCategoryResource::collection(
                $this->subCategories->filter(function ($subCategory) {
                    return $subCategory->courses->where('is_active', 1)->isNotEmpty();
                })
            ),
            'courses' => CourseListResource::collection($this->coursesWithOutSubCategories)
        ];
    }
}
