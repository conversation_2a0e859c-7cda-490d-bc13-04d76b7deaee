<?php

namespace App\Http\Resources\Website;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use App\Http\Resources\PromotionalItemResource;
use App\Http\Controllers\Mobile\CourseController;

class LandingPageResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {

        $courseController = new CourseController($request);
        $boughtCourseIds = $courseController->getPurchasedCourseIds();
        $data = [
            'id' => $this->id,
            'name' => $this->name,
            'categories' => $this->only_categories->select('id', 'name', 'icon', 'headline'),
            'category_with_courses' => CategoryResource::collection($this->only_categories->take(4))->where('is_active', 1)->where('is_content', 0),
            'promotional_items' => PromotionalItemResource::collection(
                $this->promotionalItems->loadMissing([
                    'course', 'ebook'
                ])
                ),
            'bought_items' => $boughtCourseIds

        ];


        return $data;
    }
}
