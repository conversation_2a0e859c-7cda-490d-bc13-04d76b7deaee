<?php

namespace App\Http\Resources\Website;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use App\Http\Resources\Mobile\Content\VideoDetailsResource;
use App\Http\Resources\Mobile\Content\ScriptDetailsResource;
use App\Http\Resources\Mobile\Content\QuizDetailsResource;

class ContentDetailsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'organization_id' => $this->organization_id,
            'title' => $this->title,
            'title_bn' => $this->title_bn,
            'course_id' => $this->course_id,
            'type' => $this->chapter_script_id ? 'script' : ($this->chapter_video_id ? 'video' : 'quiz'),
            'script' => $this->chapter_script_id ? new ScriptDetailsResource($this->script) : null,
            'video' => $this->chapter_video_id ? new VideoDetailsResource($this->video) : null,
            'quiz' => $this->chapter_quiz_id ? new QuizDetailsResource($this->quiz) : null,
            'element_id' => $this->chapter_script_id ?? $this->chapter_video_id ?? $this->chapter_quiz_id,
            'is_free' => $this->is_free,
            'is_active' => $this->is_active
        ];
    }
}
