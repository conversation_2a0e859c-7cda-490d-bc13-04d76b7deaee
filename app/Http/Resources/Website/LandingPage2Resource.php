<?php

namespace App\Http\Resources\Website;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

use App\Http\Resources\PromotionalItemResource;
class LandingPage2Resource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $data = [

            'id' => $this->id,
            'name' => $this->name,
            'category_with_courses' => CategoryResource::collection($this->activeCategoriesHavingCourses),
            'promotional_items' => PromotionalItemResource::collection(
                $this->promotionalItems->loadMissing([
                    'course', 'ebook'
                ])
            )

        ];

        return $data;
    }
}


class CategoryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $data = [
            'id' => $this->id,
            'name' => $this->name,
            'icon' => $this->icon,
            'headline' => $this->headline,
            'courses' => CourseListResource::collection($this->courses->where('is_active', 1)),
        ];

        return $data;
    }
}
