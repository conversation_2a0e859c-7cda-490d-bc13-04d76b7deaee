<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OfflineExamListResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'organization_id' => $this->organization_id,
            'course_id' => $this->course_id,
            'course_title' => $this->course?->title,
            'title' => $this->title,
            'title_bn' => $this->title_bn,
            'description' => $this->description,
            'mcq_mark' => $this->mcq_mark,
            'written_mark' => $this->written_mark,
            'assignment_mark' => $this->assignment_mark,
            'presentation_mark' => $this->presentation_mark,
            'practical_mark' => $this->practical_mark,
            'pass_mark' => $this->pass_mark,
            'total_mark' => $this->total_mark,
            'duration' => $this->duration,
            'exam_date' => $this->exam_date,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
