<?php

namespace App\Http\Resources\Web\Organization;

use Illuminate\Http\Request;
use App\Models\Payment;
use App\Models\Course;
use Illuminate\Http\Resources\Json\JsonResource;
use App\Http\Resources\Mobile\CategoryResource;
use App\Http\Resources\Mobile\Organization\MyCourseListResource;
use App\Http\Resources\PromotionalItemResource;

class DetailsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $data = [
            'id' => $this->id,
            'name' => $this->name,
            'top_category' => new CategoryResource($this->topCategory),
            'categories' => $this->categories->select('id', 'name', 'icon', 'headline'),
            'other_categories' => CategoryResource::collection($this->categories->skip(1)->take(3)),
            'promotional_items' => PromotionalItemResource::collection(
                $this->promotionalItems->loadMissing([
                    'course', 'ebook'
                ])
            ),
        ];

            $user = auth('sanctum')->user();
            if ($user) {

            $list = Payment::where('user_id', $user->id)->where('organization_id', $user->organization_id)->pluck('item_id')->toArray();
            $courseList = Course::whereIn('id', $list)->first();

                $data['my_courses'] = new MyCourseListResource($courseList);
            }
        return $data;
    }
}
