<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PendingPaymentListResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'organization_id' => $this->organization_id,
            'payment_id' => $this->payment_id,
            'user_id' => $this->user_id,
            'user_name' => $this->user?->name,
            'user_image' => $this->user?->image ?? 'user.png',
            'item_id' => $this->item_id,
            'course_title' => $this->course?->title,
            'payment_method' => $this->payment_method,
            'trx_id' => $this->trx_id,
            'image' => $this->image,
            'deadline' => $this->deadline,
            'pay_for' => $this->pay_for,
            'paid_amount' => $this->paid_amount,
            'payable_amount' => $this->payable_amount,
            'due_amount' => $this->due_amount,
            'is_approved' => $this->is_approved,
            'approved_by' => $this->approved_by,
            'created_by' => $this->created_by,
            'created_by_name' => $this->creator?->name,
            'created_by_image' => $this->creator?->image,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
