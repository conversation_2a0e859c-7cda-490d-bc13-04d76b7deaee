<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ChapterQuizResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'title_bn' => $this->title_bn,
            'description' => $this->description,
            'quiz_code' => $this->quiz_code,
            'class_level_id' => $this->class_level_id,
            'quiz_type_id' => $this->quiz_type_id,
            'course_id' => $this->course_id,
            'course_category_id' => $this->course_category_id,
            'subject_id' => $this->subject_id,
            'chapter_id' => $this->chapter_id,
            'duration' => $this->duration,
            'positive_mark' => $this->positive_mark,
            'negative_mark' => $this->negative_mark,
            'total_mark' => $this->total_mark,
            'number_of_question' => $this->number_of_question,
            'is_free' => $this->is_free,
            'sequence' => $this->sequence,
            'sufficient_question' => $this->sufficient_question,
            'is_active' => $this->is_active,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'quiz_items' => QuizItemResource::collection($this->whenLoaded('quizItems')),
            'written_questions' => $this->whenLoaded('writtenQuestions', function () {
                return [
                    'id' => $this->writtenQuestions->id,
                    'chapter_quiz_id' => $this->writtenQuestions->chapter_quiz_id,
                    'description' => $this->writtenQuestions->description,
                    'question_attachment' => $this->writtenQuestions->question_attachment,
                    'instruction' => $this->writtenQuestions->instruction,
                    'duration' => $this->writtenQuestions->duration,
                    'marks' => $this->writtenQuestions->marks,
                    'no_of_question' => $this->writtenQuestions->no_of_question,
                    'is_active' => $this->writtenQuestions->is_active,
                ];
            }),
        ];
    }
}
