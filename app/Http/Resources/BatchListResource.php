<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class BatchListResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'name_en' => $this->name_en,
            'course_id' => $this->course_id,
            'course_title' => $this->course->title,
            'description' => $this->description,
            'organization_id' => $this->organization_id,
            'capacity' => $this->capacity,
            'is_active' => $this->is_active ? true : false,
            'start_date' => $this->start_date,
            'end_date' => $this->end_date,
            'background_color' => $this->background_color,
            'image' => $this->image,
            'status' => $this->status,
            'created_at' => $this->created_at,
            'total_students' => $this->students->count(),
            'total_mentors' => $this->mentors->count()
        ];
    }
}

