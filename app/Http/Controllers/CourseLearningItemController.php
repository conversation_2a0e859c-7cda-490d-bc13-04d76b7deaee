<?php

namespace App\Http\Controllers;

use App\Models\CourseLearningItem;
use Illuminate\Http\Request;

class CourseLearningItemController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(CourseLearningItem $courseLearningItem)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(CourseLearningItem $courseLearningItem)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, CourseLearningItem $courseLearningItem)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(CourseLearningItem $courseLearningItem)
    {
        //
    }
}
