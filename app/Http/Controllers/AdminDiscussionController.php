<?php

namespace App\Http\Controllers;

use App\Http\Requests\Discussion\Admin\BanUserRequest;
use App\Http\Requests\Discussion\Admin\HandleReportRequest;
use App\Http\Requests\Discussion\Admin\UnbanUserRequest;
use App\Models\Course;
use App\Models\Discussion;
use App\Models\DiscussionBan;
use App\Models\DiscussionComment;
use App\Models\DiscussionReport;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AdminDiscussionController extends Controller
{
    /**
     * List all discussions with filtering options
     */
    public function index(Request $request)
    {
        $courseId = $request->course_id;
        $status = $request->status; // 'active', 'inactive', 'pending'
        $search = $request->search;

        $query = Discussion::with(['user:id,name,email', 'course:id,title'])
            ->withCount(['comments', 'likes', 'reports']);

        // Filter by course
        if ($courseId) {
            $query->where('course_id', $courseId);
        }

        // Filter by status
        if ($status) {
            if ($status === 'active') {
                $query->where('is_active', true)->where('is_approved', true);
            } elseif ($status === 'inactive') {
                $query->where('is_active', false);
            } elseif ($status === 'pending') {
                $query->where('is_approved', false);
            }
        }

        // Search by title or content
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                    ->orWhere('content', 'like', "%{$search}%");
            });
        }

        $discussions = $query->orderBy('created_at', 'desc')->paginate(15);

        return $this->apiResponse($discussions, 'Discussions retrieved successfully', true, 200);
    }

    /**
     * Get a specific discussion with all comments and reports
     */
    public function show($id)
    {
        $discussion = Discussion::with([
            'user:id,name,email',
            'course:id,title',
            'comments' => function ($query) {
                $query->with(['user:id,name,email', 'replies.user:id,name,email'])
                    ->withCount(['likes', 'reports']);
            },
            'reports.user:id,name,email',
        ])
        ->withCount(['comments', 'likes', 'reports'])
        ->find($id);

        if (!$discussion) {
            return $this->apiResponse([], 'Discussion not found', false, 404);
        }

        return $this->apiResponse($discussion, 'Discussion retrieved successfully', true, 200);
    }

    /**
     * Delete a discussion
     */
    public function deleteDiscussion($id)
    {
        $discussion = Discussion::find($id);

        if (!$discussion) {
            return $this->apiResponse([], 'Discussion not found', false, 404);
        }

        // Soft delete by marking as inactive
        $discussion->update([
            'is_active' => false,
        ]);

        return $this->apiResponse([], 'Discussion deleted successfully', true, 200);
    }

    /**
     * Delete a comment
     */
    public function deleteComment($id)
    {
        $comment = DiscussionComment::find($id);

        if (!$comment) {
            return $this->apiResponse([], 'Comment not found', false, 404);
        }

        // Soft delete by marking as inactive
        $comment->update([
            'is_active' => false,
        ]);

        return $this->apiResponse([], 'Comment deleted successfully', true, 200);
    }

    /**
     * Ban a user from discussions
     */
    public function banUser(BanUserRequest $request)
    {
        $userId = $request->user_id;
        $courseId = $request->course_id;
        $reason = $request->reason;
        $durationDays = $request->duration_days;
        $adminId = Auth::id();

        // Calculate expiry date if duration is provided
        $expiresAt = $durationDays ? now()->addDays($durationDays) : null;

        // Check if user is already banned
        $existingBan = DiscussionBan::where('user_id', $userId)
            ->where(function ($query) use ($courseId) {
                if ($courseId) {
                    $query->where('course_id', $courseId);
                } else {
                    $query->whereNull('course_id');
                }
            })
            ->where(function ($query) {
                $query->whereNull('expires_at')
                    ->orWhere('expires_at', '>', now());
            })
            ->first();

        if ($existingBan) {
            // Update existing ban
            $existingBan->update([
                'reason' => $reason ?? $existingBan->reason,
                'expires_at' => $expiresAt ?? $existingBan->expires_at,
                'banned_by' => $adminId,
            ]);

            $ban = $existingBan;
            $message = 'User ban updated successfully';
        } else {
            // Create new ban
            $ban = DiscussionBan::create([
                'user_id' => $userId,
                'course_id' => $courseId,
                'reason' => $reason,
                'expires_at' => $expiresAt,
                'banned_by' => $adminId,
            ]);

            $message = 'User banned successfully';
        }

        return $this->apiResponse($ban, $message, true, 200);
    }

    /**
     * Unban a user from discussions
     */
    public function unbanUser(UnbanUserRequest $request)
    {
        $userId = $request->user_id;
        $courseId = $request->course_id;

        // Find active bans
        $bans = DiscussionBan::where('user_id', $userId)
            ->where(function ($query) use ($courseId) {
                if ($courseId) {
                    $query->where('course_id', $courseId);
                } else {
                    $query->whereNull('course_id');
                }
            })
            ->where(function ($query) {
                $query->whereNull('expires_at')
                    ->orWhere('expires_at', '>', now());
            })
            ->get();

        if ($bans->isEmpty()) {
            return $this->apiResponse([], 'User is not banned', false, 404);
        }

        // Set expiry to now to effectively unban
        foreach ($bans as $ban) {
            $ban->update([
                'expires_at' => now(),
            ]);
        }

        return $this->apiResponse([], 'User unbanned successfully', true, 200);
    }

    /**
     * List all reports with filtering options
     */
    public function listReports(Request $request)
    {
        $status = $request->status; // 'pending', 'reviewed', 'rejected'
        $courseId = $request->course_id;

        $query = DiscussionReport::with([
            'user:id,name,email',
            'discussion.course:id,title',
            'comment.discussion.course:id,title',
        ]);

        // Filter by status
        if ($status) {
            $query->where('status', $status);
        }

        // Filter by course
        if ($courseId) {
            $query->where(function ($q) use ($courseId) {
                $q->whereHas('discussion', function ($q) use ($courseId) {
                    $q->where('course_id', $courseId);
                })->orWhereHas('comment.discussion', function ($q) use ($courseId) {
                    $q->where('course_id', $courseId);
                });
            });
        }

        $reports = $query->orderBy('created_at', 'desc')->paginate(15);

        return $this->apiResponse($reports, 'Reports retrieved successfully', true, 200);
    }

    /**
     * Handle a report (mark as reviewed or rejected)
     */
    public function handleReport(HandleReportRequest $request, $id)
    {
        $report = DiscussionReport::find($id);

        if (!$report) {
            return $this->apiResponse([], 'Report not found', false, 404);
        }

        $adminId = Auth::id();
        $status = $request->status;

        $report->update([
            'status' => $status,
            'reviewed_by' => $adminId,
            'reviewed_at' => now(),
        ]);

        return $this->apiResponse($report, 'Report marked as ' . $status, true, 200);
    }

    /**
     * List all banned users
     */
    public function listBannedUsers(Request $request)
    {
        $courseId = $request->course_id;

        $query = DiscussionBan::with(['user:id,name,email', 'course:id,title', 'bannedBy:id,name,email'])
            ->where(function ($query) {
                $query->whereNull('expires_at')
                    ->orWhere('expires_at', '>', now());
            });

        // Filter by course
        if ($courseId) {
            $query->where(function ($q) use ($courseId) {
                $q->where('course_id', $courseId)
                    ->orWhereNull('course_id');
            });
        }

        $bans = $query->orderBy('created_at', 'desc')->paginate(15);

        return $this->apiResponse($bans, 'Banned users retrieved successfully', true, 200);
    }

    /**
     * Toggle pin status of a discussion
     */
    public function togglePin($id)
    {
        $discussion = Discussion::find($id);

        if (!$discussion) {
            return $this->apiResponse([], 'Discussion not found', false, 404);
        }

        $discussion->update([
            'is_pinned' => !$discussion->is_pinned,
        ]);

        $message = $discussion->is_pinned ? 'Discussion pinned successfully' : 'Discussion unpinned successfully';
        return $this->apiResponse($discussion, $message, true, 200);
    }

    /**
     * Approve a pending discussion
     */
    public function approveDiscussion($id)
    {
        $discussion = Discussion::find($id);

        if (!$discussion) {
            return $this->apiResponse([], 'Discussion not found', false, 404);
        }

        if ($discussion->is_approved) {
            return $this->apiResponse([], 'Discussion is already approved', false, 422);
        }

        $adminId = Auth::id();

        $discussion->update([
            'is_approved' => true,
            'approved_by' => $adminId,
            'approved_at' => now(),
        ]);

        return $this->apiResponse($discussion, 'Discussion approved successfully', true, 200);
    }
}
