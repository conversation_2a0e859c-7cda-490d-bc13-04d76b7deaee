<?php

namespace App\Http\Controllers;

use App\Models\Attendance;
use App\Models\BatchStudent;
use App\Models\ClassScheduleStudents;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Exception;
use Illuminate\Support\Facades\Auth;

class AttendanceController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function studentList(Request $request)
    {
        $classScheduleId = $request->class_schedule_id;

        $studentList = ClassScheduleStudents::where('class_schedule_students.class_schedule_id', $request->class_schedule_id)
            ->join('student_informations', 'class_schedule_students.student_id', '=', 'student_informations.id')
            ->leftJoin('attendances', function ($join) use ($classScheduleId) {
                $join->on('class_schedule_students.student_id', '=', 'attendances.student_id')
                     ->where('attendances.class_schedule_id', $classScheduleId);
            })
            ->select(
                'student_informations.id',
                'student_informations.student_id',
                'student_informations.name',
                'student_informations.email',
                'student_informations.image',
                'student_informations.contact_no',
                DB::raw('IFNULL(attendances.id, NULL) as attendance_id'),
                'attendances.attendance_date',
                'attendances.is_present'
            )
            ->get();

            foreach ($studentList as $student) {
                $student->is_present = (bool)$student->is_present;
            }


        return $this->apiResponse($studentList, 'Student List', true, 200);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function saveAttendance (Request $request)
    {
        $attendance_date = $request->attendance_date ?? date('Y-m-d');
        $created_at = date('Y-m-d H:i:s');
        $updateData = [];
        $insertData = [];

        $previousAttendanceList = Attendance::where('course_id', $request->course_id)
        ->where('class_schedule_id', $request->class_schedule_id)->get();

        if (count($previousAttendanceList) > 0) {

            foreach ($previousAttendanceList as $prevAttendance) {
                $filteredStudent = array_filter($request->students, function ($student) use ($prevAttendance) {
                    return $student['id'] == $prevAttendance->student_id;
                });

                if (count($filteredStudent) > 0) {
                    $filteredStudent = array_values($filteredStudent)[0];

                    if ($prevAttendance->is_present != $filteredStudent['is_present']) {
                        $updateData[] = [
                            'id' => $prevAttendance->id,
                            'is_present' => $filteredStudent['is_present']
                        ];
                    }
                }

            }

            // return $this->apiResponse($updateData, 'Student Attendance Processed Successfully', true, 201);
        } else {

            foreach ($request->students as $student) {

                $insertData[] = [
                    'course_id' => $request->course_id,
                    'batch_id' => $request->batch_id,
                    'class_schedule_id' => $request->class_schedule_id,
                    'attendance_date' => $attendance_date,
                    'student_id' => $student['id'],
                    'is_present' => $student['is_present'],
                    'created_at' => $created_at,
                    'attendance_by' => Auth::user()->id
                ];

            }
        }


        DB::beginTransaction();
        try {
            if (!empty($insertData)) {
                Attendance::insert($insertData);
            }
            if (!empty($updateData)) {
                foreach ($updateData as $data) {
                    Attendance::where('id', $data['id'])->update(['is_present' => $data['is_present']]);
                }
            }

            // Update CourseStudent class attendance count for present students
            $this->updateCourseStudentAttendanceCount($request->course_id, $request->students);

            DB::commit();
            return $this->apiResponse([], 'Student Attendance Saved Successfully', true, 201);
        } catch (Exception $e) {
            DB::rollBack();
            return $this->apiResponse([], $e->getMessage(), false, 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Attendance $attendance)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Attendance $attendance)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Attendance $attendance)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Attendance $attendance)
    {
        //
    }

    /**
     * Update CourseStudent class attendance count for present students
     */
    private function updateCourseStudentAttendanceCount($courseId, $students)
    {
        try {
            foreach ($students as $student) {
                if ($student['is_present']) {
                    \App\Models\CourseStudent::where('course_id', $courseId)
                        ->where('student_id', $student['id'])
                        ->increment('class_attendance_count');
                }
            }
        } catch (\Exception $e) {
            \Log::error('Failed to update CourseStudent attendance count: ' . $e->getMessage());
        }
    }
}

