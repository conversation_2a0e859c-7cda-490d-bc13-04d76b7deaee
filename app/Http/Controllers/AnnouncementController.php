<?php

namespace App\Http\Controllers;

use App\Models\Announcement;
use App\Models\AnnouncementUser;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Http\Requests\AnnouncementCreateRequest;
use App\Http\Requests\AnnouncementUpdateRequest;
use App\Http\Resources\AnnouncementListResource;
use App\Http\Resources\AnnouncementDetailsResource;
use App\Http\Traits\CommonTrait;

class AnnouncementController extends Controller
{
    use CommonTrait;
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Announcement::query();
        $user = Auth::user();
        if ($user->organization_id) {
            $query->where('organization_id', $user->organization_id);
        }

        if ($request->has('search')) {
            $query->where('title', 'LIKE', "%{$request->search}%")
            ->orWhere('descrition', 'LIKE', "%{$request->search}%");
        }

        if ($request->has('filter_date')) {
            $query->where('start_date', '<=', $request->filter_date)
            ->where('end_date', '>=', $request->filter_date);
        }

        if ($request->has('start_date')) {
            $query->whereDate('start_date', $request->start_date);
        }

        if ($request->has('end_date')) {
            $query->whereDate('end_date', $request->end_date);
        }

        if ($request->has('is_active')) {
            $query->where('is_active', $request->is_active);
        }

        if ($request->has('created_by')) {
            $query->where('created_by', $request->created_by);
        }
        $query->orderBy('created_at', 'desc');
        $announcements = $request->has('pagination') && $request->pagination == 'false'
            ? $query->get()
            : $query->paginate($request->per_page ?? 10);

        return $this->apiResponse(AnnouncementListResource::collection($announcements), 'Announcement List', true, 200);
    }


    function getAnnouncementList (Request $request) {

        if ($request->page == 'full') {
            $announcements = Announcement::where('start_date', '<=', now()->format('Y-m-d'))->orderBy('created_at', 'desc')->paginate($request->per_page ?? 10);
        } else {
            $announcements = Announcement::where('start_date', '<=', now()->format('Y-m-d'))
            ->where('end_date', '>=', now()->format('Y-m-d'))
            ->orderBy('created_at', 'desc')
            ->get();
        }

        return $this->apiResponse(AnnouncementListResource::collection($announcements), 'Announcement List', true, 200);
    }
    /**
     * Store a newly created announcement and send notifications to organization users.
     *
     * @param AnnouncementCreateRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(AnnouncementCreateRequest $request)
    {
        $generatedData = $request->generatedData();
        $user = Auth::user();
        $organizationId = $user->organization_id;

        // Create the announcement
        $announcement = Announcement::create($generatedData['announcement']);

        // Process specific users if provided
        $specificUserIds = [];
        if (!empty($generatedData['announcement_users'])) {
            $announcementUsers = collect($generatedData['announcement_users'])->map(function ($user) use ($announcement, &$specificUserIds) {
                $user['announcement_id'] = $announcement->id;
                $specificUserIds[] = $user['user_id'];
                return $user;
            })->toArray();

            AnnouncementUser::insert($announcementUsers);
        }

        // Prepare notification data
        $notificationTitle = 'New Announcement';
        $notificationMessage = "New announcement: {$announcement->title}";
        $notificationData = [
            'announcement_id' => $announcement->id,
            'title' => $announcement->title,
            'description' => substr($announcement->description, 0, 100) . (strlen($announcement->description) > 100 ? '...' : ''),
            'image' => $announcement->image,
            'created_at' => $announcement->created_at->format('Y-m-d H:i:s')
        ];

        // If specific users are targeted, send individual notifications
        if (!empty($specificUserIds)) {
            foreach ($specificUserIds as $userId) {
                $this->sendUserNotification(
                    $userId,
                    $notificationMessage,
                    $notificationTitle,
                    'announcement',
                    $notificationData
                );
            }
        } else {
            // Otherwise, send to the entire organization
            $this->sendOrganizationNotification(
                $organizationId,
                $notificationMessage,
                $notificationTitle,
                'announcement',
                $notificationData
            );
        }

        return $this->apiResponse(
            new AnnouncementDetailsResource($announcement),
            'Announcement Created Successfully',
            true,
            201
        );
    }

    /**
     * Display the specified resource.
     */
    public function show(Announcement $announcement)
    {
        if (!$announcement) {
            return $this->apiResponse(null, 'Announcement not found', false, 404);
        }

        return $this->apiResponse(new AnnouncementDetailsResource($announcement), 'Announcement details fetched', true, 200);
    }

    /**
     * Update the specified resource in storage and send notifications.
     *
     * @param AnnouncementUpdateRequest $request
     * @param Announcement $announcement
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(AnnouncementUpdateRequest $request, Announcement $announcement)
    {
        $validated = $request->validated();
        $user = Auth::user();
        $organizationId = $user->organization_id;

        $announcement->update($validated);

        // Process specific users if provided
        $specificUserIds = [];
        if (isset($validated['users'])) {
            $announcement->users()->delete();
            foreach ($validated['users'] as $userId) {
                $announcement->users()->create(['user_id' => $userId]);
                $specificUserIds[] = $userId;
            }
        }

        // Prepare notification data
        $notificationTitle = 'Announcement Updated';
        $notificationMessage = "Announcement updated: {$announcement->title}";
        $notificationData = [
            'announcement_id' => $announcement->id,
            'title' => $announcement->title,
            'description' => substr($announcement->description, 0, 100) . (strlen($announcement->description) > 100 ? '...' : ''),
            'image' => $announcement->image,
            'updated_at' => now()->format('Y-m-d H:i:s')
        ];

        // If specific users are targeted, send individual notifications
        if (!empty($specificUserIds)) {
            foreach ($specificUserIds as $userId) {
                $this->sendUserNotification(
                    $userId,
                    $notificationMessage,
                    $notificationTitle,
                    'announcement_update',
                    $notificationData
                );
            }
        } else {
            // Otherwise, send to the entire organization
            $this->sendOrganizationNotification(
                $organizationId,
                $notificationMessage,
                $notificationTitle,
                'announcement_update',
                $notificationData
            );
        }

        return $this->apiResponse(new AnnouncementDetailsResource($announcement), 'Announcement updated successfully', true, 201);
    }


    /**
     * Add user to announcement
     */
    public function addUser(Request $request, Announcement $announcement)
    {
        $announcement->users()->attach($request->user_id);

        return response()->json($announcement->load('users'));
    }


    /**
     * Remove user from announcement
     */
    public function removeUser(Request $request, Announcement $announcement)
    {
        $announcement->users()->detach($request->user_id);

        return response()->json($announcement->load('users'));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Announcement $announcement)
    {
        // $announcement->users()->delete();
        $announcement->delete();

        return response()->json(null, 204);
    }
}

