<?php

namespace App\Http\Controllers;

use App\Models\PaymentTypeOrganization;
use App\Models\PaymentTypeOrganizationItem;
use Illuminate\Http\Request;
use App\Http\Requests\PaymentTypeOrganizationCreateRequest;
use App\Http\Requests\PaymentTypeOrganizationUpdateRequest;
use Illuminate\Support\Facades\Auth;

class PaymentTypeOrganizationController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $user = Auth::user();
        $paymentTypeOrganizations = PaymentTypeOrganization::with('items')->where('organization_id', $user->organization_id)->get();
        return $this->apiResponse($paymentTypeOrganizations, 'Payment type organizations fetched successfully', true, 200);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(PaymentTypeOrganizationCreateRequest $request)
    {
        $validated = $request->validated();

        $validated['organization_id'] = Auth::user()->organization_id;
        if ($request->hasFile('icon')) {
            $validated['icon'] = $this->imageUpload($request, 'icon', 'payment_icon');
        }
        $paymentTypeOrganization = PaymentTypeOrganization::create($validated);

        if (isset($validated['items'])) {
            foreach ($validated['items'] as $index => $item) {
                if (isset($item['image'])) {
                    $destination = 'payment_methods';
                    $image = $item['image'];
                    $imageName = 'bb_'.time().$index.'.'.$image->getClientOriginalExtension();
                    $imageDestination = public_path('uploads/'.$destination);
                    $image->move($imageDestination, $imageName);

                    $imageUrl = $destination.'/'.$imageName;

                    $item['image'] = $imageUrl;
                }

                // Create the item for the payment type organization
                $paymentTypeOrganization->items()->create($item);
            }
        }


        return $this->apiResponse($paymentTypeOrganization->load('items'), 'Payment type organization created successfully', true, 201);
    }

    /**
     * Display the specified resource.
     */
    public function show($id)
    {

        $paymentTypeOrganization = PaymentTypeOrganization::with('items')->find($id);

        // Ensure related items are loaded
        $paymentTypeOrganization->load('items');

        if (!$paymentTypeOrganization) {
            return $this->apiResponse(null, 'Payment type organization not found', false, 404);
        }

        return $this->apiResponse($paymentTypeOrganization, 'Payment type organization details fetched successfully', true, 200);
    }


    /**
     * Update the specified resource in storage.
     */

    public function update(PaymentTypeOrganizationUpdateRequest $request, $id)
    {
        $validated = $request->validated();

        $paymentTypeOrganization = PaymentTypeOrganization::with('items')->find($id);
        $paymentTypeOrganization->update($validated);


        if (isset($validated['items'])) {
            foreach ($validated['items'] as $item) {
                if (isset($item['id'])) {
                    $paymentTypeOrganization->items()->where('id', $item['id'])->update($item);
                } else {
                    $paymentTypeOrganization->items()->create($item);
                }
            }

            $itemIds = array_column($validated['items'], 'id');
            $paymentTypeOrganization->items()->whereNotIn('id', $itemIds)->delete();
        }

        $paymentTypeOrganization->load('items');

        return $this->apiResponse(
            $paymentTypeOrganization,
            'Payment type organization updated successfully',
            true,
            200
        );
    }


    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        $paymentTypeOrganization = PaymentTypeOrganization::with('items')->find($id);

        $paymentTypeOrganization->items()->delete();
        $paymentTypeOrganization->delete();

        return $this->apiResponse(null, 'Payment type organization deleted successfully', true, 200);
    }
}

