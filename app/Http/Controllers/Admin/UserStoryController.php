<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\UserStory;
use App\Models\UserStoryTestimonial;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;

class UserStoryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        // Get only the first user story with its testimonials
        $userStory = UserStory::with(['activeTestimonials' => function($query) {
            $query->orderBy('sort_order');
        }])->active()->ordered()->first();

        if (!$userStory) {
            return $this->apiResponse(null, 'No user stories found', false, 404);
        }

        return $this->apiResponse($userStory, 'User story retrieved successfully', true, 200);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'short_description' => 'nullable|string',
            'color' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'background' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'is_active' => 'boolean',
            'sort_order' => 'integer|min:0',
            'testimonials' => 'array',
            'testimonials.*.name' => 'required|string|max:255',
            'testimonials.*.designation' => 'nullable|string|max:255',
            'testimonials.*.speech' => 'required|string',
            'testimonials.*.datetime' => 'nullable|date',
            'testimonials.*.image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'testimonials.*.is_active' => 'boolean',
            'testimonials.*.sort_order' => 'integer|min:0'
        ]);

        DB::transaction(function () use ($request) {
            $userStory = UserStory::create([
                'title' => $request->title,
                'short_description' => $request->short_description,
                'color' => $request->color,
                'background' => $request->background,
                'is_active' => $request->boolean('is_active', true),
                'sort_order' => $request->integer('sort_order', 0)
            ]);

            if ($request->has('testimonials')) {
                foreach ($request->testimonials as $index => $testimonialData) {
                    $imagePath = null;
                    if (isset($testimonialData['image'])) {
                        $imagePath = $testimonialData['image']->store('testimonials', 'public');
                    }

                    $userStory->testimonials()->create([
                        'name' => $testimonialData['name'],
                        'designation' => $testimonialData['designation'] ?? null,
                        'speech' => $testimonialData['speech'],
                        'datetime' => $testimonialData['datetime'] ?? null,
                        'image' => $imagePath,
                        'is_active' => $testimonialData['is_active'] ?? true,
                        'sort_order' => $testimonialData['sort_order'] ?? $index
                    ]);
                }
            }
        });

        return $this->apiResponse(null, 'User story created successfully', true, 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(UserStory $userStory): JsonResponse
    {
        $userStory->load(['testimonials' => function($query) {
            $query->orderBy('sort_order');
        }]);

        return $this->apiResponse($userStory, 'User story retrieved successfully', true, 200);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, UserStory $userStory): JsonResponse
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'short_description' => 'nullable|string',
            'color' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'background' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'is_active' => 'boolean',
            'sort_order' => 'integer|min:0',
            'testimonials' => 'array',
            'testimonials.*.id' => 'nullable|exists:user_story_testimonials,id',
            'testimonials.*.name' => 'required|string|max:255',
            'testimonials.*.designation' => 'nullable|string|max:255',
            'testimonials.*.speech' => 'required|string',
            'testimonials.*.datetime' => 'nullable|date',
            'testimonials.*.image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'testimonials.*.is_active' => 'boolean',
            'testimonials.*.sort_order' => 'integer|min:0',
            'testimonials.*.delete' => 'boolean'
        ]);

        DB::transaction(function () use ($request, $userStory) {
            $userStory->update([
                'title' => $request->title,
                'short_description' => $request->short_description,
                'color' => $request->color,
                'background' => $request->background,
                'is_active' => $request->boolean('is_active', true),
                'sort_order' => $request->integer('sort_order', 0)
            ]);

            if ($request->has('testimonials')) {
                $existingTestimonialIds = [];

                foreach ($request->testimonials as $index => $testimonialData) {
                    if (isset($testimonialData['delete']) && $testimonialData['delete']) {
                        if (isset($testimonialData['id'])) {
                            $testimonial = UserStoryTestimonial::find($testimonialData['id']);
                            if ($testimonial && $testimonial->image) {
                                Storage::disk('public')->delete($testimonial->image);
                            }
                            $testimonial?->delete();
                        }
                        continue;
                    }

                    $imagePath = null;
                    if (isset($testimonialData['image'])) {
                        $imagePath = $testimonialData['image']->store('testimonials', 'public');
                    }

                    if (isset($testimonialData['id'])) {
                        // Update existing testimonial
                        $testimonial = UserStoryTestimonial::find($testimonialData['id']);
                        if ($testimonial) {
                            $updateData = [
                                'name' => $testimonialData['name'],
                                'designation' => $testimonialData['designation'] ?? null,
                                'speech' => $testimonialData['speech'],
                                'datetime' => $testimonialData['datetime'] ?? null,
                                'is_active' => $testimonialData['is_active'] ?? true,
                                'sort_order' => $testimonialData['sort_order'] ?? $index
                            ];

                            if ($imagePath) {
                                if ($testimonial->image) {
                                    Storage::disk('public')->delete($testimonial->image);
                                }
                                $updateData['image'] = $imagePath;
                            }

                            $testimonial->update($updateData);
                            $existingTestimonialIds[] = $testimonial->id;
                        }
                    } else {
                        // Create new testimonial
                        $newTestimonial = $userStory->testimonials()->create([
                            'name' => $testimonialData['name'],
                            'designation' => $testimonialData['designation'] ?? null,
                            'speech' => $testimonialData['speech'],
                            'datetime' => $testimonialData['datetime'] ?? null,
                            'image' => $imagePath,
                            'is_active' => $testimonialData['is_active'] ?? true,
                            'sort_order' => $testimonialData['sort_order'] ?? $index
                        ]);
                        $existingTestimonialIds[] = $newTestimonial->id;
                    }
                }

                // Delete testimonials that are not in the request
                $userStory->testimonials()
                    ->whereNotIn('id', $existingTestimonialIds)
                    ->each(function ($testimonial) {
                        if ($testimonial->image) {
                            Storage::disk('public')->delete($testimonial->image);
                        }
                        $testimonial->delete();
                    });
            }
        });

        return $this->apiResponse($userStory->fresh(['testimonials']), 'User story updated successfully', true, 200);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(UserStory $userStory): JsonResponse
    {
        DB::transaction(function () use ($userStory) {
            // Delete all testimonial images
            $userStory->testimonials->each(function ($testimonial) {
                if ($testimonial->image) {
                    Storage::disk('public')->delete($testimonial->image);
                }
            });

            $userStory->delete();
        });

        return $this->apiResponse(null, 'User story deleted successfully', true, 200);
    }
}
