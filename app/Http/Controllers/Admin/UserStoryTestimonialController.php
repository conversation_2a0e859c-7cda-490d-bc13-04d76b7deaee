<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\UserStory;
use App\Models\UserStoryTestimonial;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Http\JsonResponse;

class UserStoryTestimonialController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        $query = UserStoryTestimonial::with('userStory')->orderBy('sort_order');

        // Filter by user story if provided
        if ($request->has('user_story_id')) {
            $query->where('user_story_id', $request->user_story_id);
        }

        // Filter by active status if provided
        if ($request->has('is_active')) {
            $query->where('is_active', $request->boolean('is_active'));
        }

        // Search by name or designation
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('designation', 'like', "%{$search}%");
            });
        }

        $testimonials = $query->paginate($request->integer('per_page', 15));

        return $this->apiResponse($testimonials, 'Testimonials retrieved successfully', true, 200);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'user_story_id' => 'required|exists:user_stories,id',
            'name' => 'required|string|max:255',
            'designation' => 'nullable|string|max:255',
            'speech' => 'required|string',
            'datetime' => 'nullable|date',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_active' => 'boolean',
            'sort_order' => 'integer|min:0'
        ]);

        $imagePath = null;
        if ($request->hasFile('image')) {
            $imagePath = $this->imageUpload($request, 'image', 'testimonials');
        }

        $testimonial = UserStoryTestimonial::create([
            'user_story_id' => $request->user_story_id,
            'name' => $request->name,
            'designation' => $request->designation,
            'speech' => $request->speech,
            'datetime' => $request->datetime,
            'image' => $imagePath,
            'is_active' => $request->boolean('is_active', true),
            'sort_order' => $request->integer('sort_order', 0)
        ]);

        $testimonial->load('userStory');

        return $this->apiResponse($testimonial, 'Testimonial created successfully', true, 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(UserStoryTestimonial $testimonial): JsonResponse
    {
        $testimonial->load('userStory');

        return $this->apiResponse($testimonial, 'Testimonial retrieved successfully', true, 200);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id)
    {
        $request->validate([
            'user_story_id' => 'required|exists:user_stories,id',
            'name' => 'required|string|max:255',
            'designation' => 'nullable|string|max:255',
            'speech' => 'required|string',
            'datetime' => 'nullable|date',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_active' => 'boolean',
            'sort_order' => 'integer|min:0'
        ]);

        $updateData = [
            'user_story_id' => $request->user_story_id,
            'name' => $request->name,
            'designation' => $request->designation,
            'speech' => $request->speech,
            'datetime' => $request->datetime,
            'is_active' => $request->boolean('is_active', true),
            'sort_order' => $request->integer('sort_order', 0)
        ];

        $testimonial = UserStoryTestimonial::find($id);
        if (!$testimonial) {
            return $this->apiResponse(null, 'Testimonial not found', false, 404);
        }

        // Handle image upload
        if ($request->hasFile('image')) {

            $updateData['image'] =  $this->imageUpload($request, 'image', 'testimonials', $testimonial->image);
        }

        $testimonial->update($updateData);
        $testimonial->load('userStory');

        return $this->apiResponse($testimonial, 'Testimonial updated successfully', true, 200);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(UserStoryTestimonial $testimonial): JsonResponse
    {
        // Delete image if exists
        if ($testimonial->image) {
            $this->deleteImage($testimonial->image);
        }

        $testimonial->delete();

        return $this->apiResponse(null, 'Testimonial deleted successfully', true, 200);
    }

    /**
     * Get user stories list for dropdown/select options.
     */
    public function getUserStories(): JsonResponse
    {
        $userStories = UserStory::active()
            ->ordered()
            ->select('id', 'title')
            ->get();

        return $this->apiResponse($userStories, 'User stories retrieved successfully', true, 200);
    }

    /**
     * Bulk update sort order for testimonials.
     */
    public function updateSortOrder(Request $request): JsonResponse
    {
        $request->validate([
            'testimonials' => 'required|array',
            'testimonials.*.id' => 'required|exists:user_story_testimonials,id',
            'testimonials.*.sort_order' => 'required|integer|min:0'
        ]);

        foreach ($request->testimonials as $testimonialData) {
            UserStoryTestimonial::where('id', $testimonialData['id'])
                ->update(['sort_order' => $testimonialData['sort_order']]);
        }

        return $this->apiResponse(null, 'Sort order updated successfully', true, 200);
    }

    /**
     * Toggle active status of testimonial.
     */
    public function toggleStatus(UserStoryTestimonial $testimonial): JsonResponse
    {
        $testimonial->update(['is_active' => !$testimonial->is_active]);

        return $this->apiResponse([
            'id' => $testimonial->id,
            'is_active' => $testimonial->is_active
        ], 'Testimonial status updated successfully', true, 200);
    }
}
