<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Currency;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;

class CurrencyController extends Controller
{
    /**
     * Display a listing of currencies.
     */
    public function index(Request $request): JsonResponse
    {
        $query = Currency::query();

        // Search by name, code, or symbol
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%")
                  ->orWhere('symbol', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        // Filter by default currency
        if ($request->has('is_default')) {
            $query->where('is_default', $request->boolean('is_default'));
        }

        $currencies = $query->orderBy('name')->paginate($request->integer('per_page', 15));

        return response()->json([
            'status' => true,
            'message' => 'Currencies retrieved successfully',
            'data' => $currencies
        ]);
    }

    /**
     * Store a newly created currency.
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'symbol' => 'required|string|max:10',
            'code' => 'required|string|max:3|unique:currencies,code',
            'status' => 'required|in:active,inactive',
            'is_default' => 'boolean',
            'exchange_rate' => 'nullable|numeric|min:0'
        ]);

        DB::transaction(function () use ($request) {
            // If this currency is being set as default, remove default from others
            if ($request->boolean('is_default')) {
                Currency::where('is_default', true)->update(['is_default' => false]);
            }

            Currency::create([
                'name' => $request->name,
                'symbol' => $request->symbol,
                'code' => strtoupper($request->code),
                'status' => $request->status,
                'is_default' => $request->boolean('is_default', false),
                'exchange_rate' => $request->input('exchange_rate', 0)
            ]);
        });

        return response()->json([
            'status' => true,
            'message' => 'Currency created successfully'
        ], 201);
    }


    /**
     * Display the specified currency.
     */
    public function show(Currency $currency): JsonResponse
    {
        return response()->json([
            'status' => true,
            'message' => 'Currency retrieved successfully',
            'data' => $currency
        ]);
    }

    /**
     * Update the specified currency.
     */
    public function update(Request $request, Currency $currency): JsonResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'symbol' => 'required|string|max:10',
            'code' => 'required|string|max:3|unique:currencies,code,' . $currency->id,
            'status' => 'required|in:active,inactive',
            'is_default' => 'boolean',
            'exchange_rate' => 'required|numeric|min:0.00001'
        ]);

        DB::transaction(function () use ($request, $currency) {
            // If this currency is being set as default, remove default from others
            if ($request->boolean('is_default') && !$currency->is_default) {
                Currency::where('is_default', true)->update(['is_default' => false]);
            }

            $currency->update([
                'name' => $request->name,
                'symbol' => $request->symbol,
                'code' => strtoupper($request->code),
                'status' => $request->status,
                'is_default' => $request->boolean('is_default', false),
                'exchange_rate' => $request->exchange_rate
            ]);
        });

        return response()->json([
            'status' => true,
            'message' => 'Currency updated successfully',
            'data' => $currency->fresh()
        ]);
    }

    /**
     * Remove the specified currency.
     */
    public function destroy(Currency $currency): JsonResponse
    {
        // Check if currency is being used by any organization
        $organizationsUsingCurrency = \App\Models\Organization::whereNotNull('currencies')
            ->get()
            ->filter(function ($org) use ($currency) {
                return $org->hasCurrency($currency->id);
            });

        if ($organizationsUsingCurrency->count() > 0) {
            return response()->json([
                'status' => false,
                'message' => 'Cannot delete currency that is assigned to organizations'
            ], 422);
        }

        // Check if it's the default currency
        if ($currency->is_default) {
            return response()->json([
                'status' => false,
                'message' => 'Cannot delete the default currency'
            ], 422);
        }

        $currency->delete();

        return response()->json([
            'status' => true,
            'message' => 'Currency deleted successfully'
        ]);
    }
}
