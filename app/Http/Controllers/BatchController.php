<?php

namespace App\Http\Controllers;

use App\Models\Batch;
use Illuminate\Http\Request;
use App\Services\BatchService;
use App\Http\Requests\BatchCreateRequest;
use App\Http\Requests\BatchUpdateRequest;
use App\Http\Requests\BatchStudentAddRequest;
use App\Http\Requests\BatchMentorAddRequest;
use App\Http\Traits\HelperTrait;
use App\Http\Resources\BatchListResource;
use App\Http\Resources\BatchListWithMentorResource;
use App\Http\Resources\BatchDetailsResource;

class BatchController extends Controller
{
    use HelperTrait;
    public function __construct(
        protected BatchService $batchService
      ) {}

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $resource = new BatchListResource($request);

        if ($request->input('mentor') == 1) {
            $resource = new BatchListWithMentorResource($request);
        }
        $data = $this->batchService->all($request);
        if ($request->input('pagination') != 'false') {
            $data['data'] = $resource::collection($data['data']);
        } else {
            $data = $resource::collection($data);
        }

        return $this->apiResponse($data, 'Batch list fetched successfully', true, 200);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(BatchCreateRequest $request)
    {
        $batch = $this->batchService->create($request->validated());

        return $this->apiResponse(new BatchDetailsResource($batch), 'Batch created successfully', true, 201);
    }

    public function addStudents(BatchStudentAddRequest $request) {
        $batch = $this->batchService->addStudents($request->validated());
        return $this->apiResponse(new BatchDetailsResource($batch), 'Batch students added successfully', true, 201);
    }


    public function removeStudents(BatchStudentAddRequest $request) {
        $batch = $this->batchService->removeStudents($request->validated());
        return $this->apiResponse(new BatchDetailsResource($batch), 'Batch students removed successfully', true, 201);
    }

    public function addMentors(BatchMentorAddRequest $request) {
        $batch = $this->batchService->addMentors($request->validated());
        return $this->apiResponse(new BatchDetailsResource($batch), 'Batch mentors added successfully', true, 201);
    }

    public function removeMentors(Request $request) {
        $batch = $this->batchService->removeMentors($request->validated());
        return $this->apiResponse(new BatchDetailsResource($batch), 'Batch mentors removed successfully', true, 201);
    }


    public function mentorBatchList (Request $request) {
        $data = $this->batchService->mentorBatchList($request);
        return $this->apiResponse($data, 'Batch list fetched successfully', true, 200);
    }
    /**
     * Display the specified resource.
     */
    public function show(Batch $batch)
    {
        $batch = $this->batchService->find($batch->id);
        $batch = new BatchDetailsResource($batch);

        return $this->apiResponse($batch, 'Batch details fetched successfully', true, 200);

    }


    /**
     * Update the specified resource in storage.
     */
    public function update(BatchUpdateRequest $request, Batch $batch)
    {
        $batch = $this->batchService->update($request->validated(), $batch->id);
        return $this->apiResponse(new BatchDetailsResource($batch), 'Batch updated successfully', true, 201);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Batch $batch)
    {
        $batch = $this->batchService->delete($batch->id);
        return $this->apiResponse(null, 'Batch deleted successfully', true, 200);
    }


    public function studentList (Request $request) {
        $data = $this->batchService->studentList($request);
        return $this->apiResponse($data, 'Student list of batch fetched successfully', true, 200);
    }
}
