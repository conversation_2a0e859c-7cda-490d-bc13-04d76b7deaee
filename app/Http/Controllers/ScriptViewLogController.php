<?php

namespace App\Http\Controllers;

use App\Models\ScriptViewLog;
use Illuminate\Http\Request;

class ScriptViewLogController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(ScriptViewLog $scriptViewLog)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ScriptViewLog $scriptViewLog)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ScriptViewLog $scriptViewLog)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ScriptViewLog $scriptViewLog)
    {
        //
    }
}
