<?php

namespace App\Http\Controllers;

use App\Http\Requests\StorePromotionalItemRequest;
use App\Http\Requests\UpdatePromotionalItemRequest;
use App\Http\Resources\PromotionalItemCollection;
use App\Http\Resources\PromotionalItemResource;
use App\Models\PromotionalItem;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class PromotionalItemController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $items = PromotionalItem::query()
                ->when($request->has('is_active'), function($query) use ($request) {
                    $query->where('is_active', $request->boolean('is_active'));
                })
                ->when($request->has('type'), function($query) use ($request) {
                    $query->where('type', $request->type);
                })
                ->when($request->has('search'), function($query) use ($request) {
                    $query->where('title', 'LIKE', "%{$request->search}%");
                })
                ->orderByRaw('is_active DESC, created_at DESC')
                ->paginate($request->per_page ?? 15);

            return $this->apiResponse(new PromotionalItemCollection($items), 'Successfully retrieved promotional items', true, 200);

        } catch (\Exception $e) {
            return $this->apiResponse([], 'Failed to retrieve promotional items', false, 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StorePromotionalItemRequest $request): JsonResponse
    {
        try {
            DB::beginTransaction();
            $data = $request->validated();
            // Handle file uploads
            if ($request->hasFile('image')) {
                $data['image'] = $this->imageUpload($request, 'image', 'image');
            }

            $item = PromotionalItem::create($data);

            DB::commit();

            return $this->apiResponse(new PromotionalItemResource($item->loadMissing([
                'organization', 'course', 'chapterVideo',
                'chapterScript', 'chapterQuiz', 'ebook', 'createdBy'
            ])), 'Promotional item created successfully', true, 201);

        } catch (\Exception $e) {
            DB::rollBack();
            return $this->apiResponse([], 'Failed to create promotional item', false, 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(PromotionalItem $promotionalItem): JsonResponse
    {
        try {
            return $this->apiResponse(new PromotionalItemResource(
                $promotionalItem->loadMissing([
                    'course', 'chapterVideo',
                    'chapterScript', 'chapterQuiz', 'ebook', 'createdBy'
                ])
            ), 'Successfully retrieved promotional item', true, 200);

        } catch (\Exception $e) {
            return $this->apiResponse([], 'Failed to retrieve promotional item', false, 500);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdatePromotionalItemRequest $request, PromotionalItem $promotionalItem): JsonResponse
    {
        try {
            DB::beginTransaction();

            $data = $request->validated();
            // Handle file uploads
            if ($request->hasFile('image')) {
                $data['image'] = $this->imageUpload($request, 'image', 'image', $promotionalItem->image);
            }

            $promotionalItem->update($data);

            DB::commit();

            return $this->apiResponse(new PromotionalItemResource(
                $promotionalItem->loadMissing([
                    'organization', 'course', 'chapterVideo',
                    'chapterScript', 'chapterQuiz', 'ebook', 'createdBy'
                ])
            ), 'Promotional item updated successfully', true, 200);

        } catch (\Exception $e) {
            DB::rollBack();
            return $this->apiResponse([], 'Failed to update promotional item', false, 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(PromotionalItem $promotionalItem): JsonResponse
    {
        try {
            DB::beginTransaction();

            $promotionalItem->delete();

            DB::commit();

            return $this->apiResponse([], 'Promotional item deleted successfully', true, 200);

        } catch (\Exception $e) {
            DB::rollBack();
            return $this->apiResponse([], 'Failed to delete promotional item', false, 500);
        }
    }

    /**
     * Toggle active status of promotional item
     */
    public function toggleActive(PromotionalItem $promotionalItem): JsonResponse
    {
        try {
            DB::beginTransaction();

            $promotionalItem->update(['is_active' => !$promotionalItem->is_active]);

            DB::commit();

            return $this->apiResponse(new PromotionalItemResource(
                $promotionalItem->loadMissing([
                    'organization', 'course', 'chapterVideo',
                    'chapterScript', 'chapterQuiz', 'ebook', 'createdBy'
                ])
            ), 'Promotional item status updated successfully', true, 200);

        } catch (\Exception $e) {
            DB::rollBack();
            return $this->apiResponse([], 'Failed to update promotional item status', false, 500);
        }
    }

}

