<?php

namespace App\Http\Controllers;

use App\Models\TemplateSubMenu;
use Illuminate\Http\Request;

class TemplateSubMenuController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(TemplateSubMenu $templateSubMenu)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(TemplateSubMenu $templateSubMenu)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, TemplateSubMenu $templateSubMenu)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(TemplateSubMenu $templateSubMenu)
    {
        //
    }
}
