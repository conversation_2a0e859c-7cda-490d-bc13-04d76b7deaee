<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Imports\CourseMaterialImport;
use App\Imports\CommonExcelImport;
use Validator;
use Maatwebsite\Excel\Facades\Excel;

// Importing models
use App\Models\MentorInformation;
use App\Models\Course;
use App\Models\StudentInformation;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Auth;
class AdminCourseController extends Controller
{
    public function importCourseMaterial(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'file' => 'required|mimes:xlsx,xls,csv',
        ]);

        if ($validator->fails()) {
            return $this->apiResponse([], $validator->errors(), false, 422);
        }

        // Load the CSV file and get its data
        $import = new CourseMaterialImport();
        $data = Excel::toArray($import, $request->file('file'));

        $rows = $data[0]; // Assume the first sheet contains the data
        $nestedData = [];
        $currentSubject = null;

        foreach ($rows as $row) {
            // Check if the row represents a subject
            if (!empty($row['name'])) {
                // Start a new subject
                $currentSubject = [
                    'name' => $row['name'],
                    'name_bn' => $row['name_bn'],
                    'subject_code' => $row['subject_code'],
                    'course_id' => $row['course_id'],
                    'class_level_id' => $row['class_level_id'],
                    'price' => $row['price'],
                    'is_free' => $row['is_free'],
                    'icon' => $row['icon'],
                    'color_code' => $row['color_code'],
                    'outlines' => [],
                ];
                $nestedData[] = &$currentSubject;
            }

            // Check if the row represents a course outline
            if (!empty($row['title']) && $currentSubject) {
                $currentSubject['outlines'][] = [
                    'title' => $row['title'],
                    'title_bn' => $row['title_bn'],
                ];
            }
        }

        // Return the nested data
        return response()->json($nestedData);
    }


    public function importMentor(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'file' => 'required|mimes:xlsx,xls,csv',
        ]);

        if ($validator->fails()) {
            return $this->apiResponse([], $validator->errors(), false, 422);
        }

        try {
            // Lock tables before starting the transaction
            DB::statement('LOCK TABLES users WRITE, mentor_informations WRITE');
            DB::beginTransaction();

            $import = new CommonExcelImport();
            $data = Excel::toArray($import, $request->file('file'));
            $rows = $data[0];

            $userData = [];
            $mentorData = [];
            $lastUserId = User::orderByDesc('id')->value('id');
            $lastMentorId = MentorInformation::orderByDesc('id')->value('id');

            foreach ($rows as $row) {
                $userId = ++$lastUserId;

                if (!is_null($row['contact_no'])) {
                    $contactNo = preg_replace('/[^0-9]/', '', $row['contact_no']);
                    if (preg_match('/^1/', $contactNo)) {
                        $contactNo = '0' . $contactNo;
                    } elseif (preg_match('/^880/', $contactNo)) {
                        $contactNo = substr($contactNo, 3);
                    } else {
                        throw new \Exception('Invalid contact number in CSV: ' . $row['contact_no']);
                    }
                } else {
                    $contactNo = null;
                }
                if (!is_null($row['alternative_contact_no'])) {
                    $altContactNo = preg_replace('/[^0-9]/', '', $row['alternative_contact_no']);
                    if (preg_match('/^1/', $altContactNo)) {
                        $altContactNo = '0' . $altContactNo;
                    } elseif (preg_match('/^880/', $altContactNo)) {
                        $altContactNo = substr($altContactNo, 3);
                    } else {
                        throw new \Exception('Invalid contact number in CSV: ' . $row['alternative_contact_no']);
                    }
                } else {
                    $altContactNo = null;
                }


                $userData[] = [
                    'id' => $userId,
                    "organization_id" => Auth::user()->organization_id,
                    'name' => $row['name'],
                    'email' => $row['email'],
                    'password' => isset($row['password']) ? Hash::make($row['password']) : Hash::make('123456bB'),
                    'contact_no' => $contactNo,
                    'address' => $row['current_address'],
                    'user_type' => 'Mentor',
                    'created_at' => now(),
                    'updated_at' => now(),
                ];

                $mentorCodeNumber = ++$lastMentorId;

                $mentorData[] = [
                    "user_id" => $userId,
                    "organization_id" => Auth::user()->organization_id,
                    "name" => $row['name'],
                    "email" => $row['email'],
                    "contact_no" => $contactNo,
                    "education" => $row['education'],
                    "institute" => $row['institute'],
                    "profession" => $row['profession'],
                    "current_address" => $row['current_address'],
                    "permanent_address" => $row['permanent_address'],
                    "nid_no" => $row['nid_no'],
                    "birth_certificate_no" => $row['birth_certificate_no'],
                    "passport_no" => $row['passport_no'],
                    "alternative_contact_no" => $altContactNo,
                    "gender" => $row['gender'],
                    "blood_group" => $row['blood_group'],
                    "bio" => $row['bio'],
                    "father_name" => $row['father_name'],
                    "mother_name" => $row['mother_name'],
                    "religion" => $row['religion'],
                    "marital_status" => $row['marital_status'],
                    "date_of_birth" => $row['date_of_birth'],
                    'mentor_code' => 'MC-' . str_pad($mentorCodeNumber + 1, 5, 0, STR_PAD_LEFT),
                    'is_active' => 1,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            }

            User::insert($userData);
            MentorInformation::insert($mentorData);

            DB::commit();
            return $this->apiResponse(count($mentorData), 'Mentor imported successfully', true, 200);
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->apiResponse([], $e->getMessage(), false, 500);
        } finally {
            // Unlock tables
            DB::statement('UNLOCK TABLES');
        }
    }


    public function importStudent(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'file' => 'required|mimes:xlsx,xls,csv',
        ]);

        if ($validator->fails()) {
            return $this->apiResponse([], $validator->errors(), false, 422);
        }

        try {
            // Lock tables before starting the transaction
            DB::statement('LOCK TABLES users WRITE, student_informations WRITE');
            DB::beginTransaction();

            $import = new CommonExcelImport();
            $data = Excel::toArray($import, $request->file('file'));
            $rows = $data[0];

            $userData = [];
            $studentData = [];
            $lastUserId = User::orderByDesc('id')->value('id');
            $lastStudentId = StudentInformation::orderByDesc('id')->value('id');

            foreach ($rows as $row) {
                $userId = ++$lastUserId;

                if (!is_null($row['contact_no'])) {
                    $contactNo = preg_replace('/[^0-9]/', '', $row['contact_no']);
                    if (preg_match('/^1/', $contactNo)) {
                        $contactNo = '0' . $contactNo;
                    } elseif (preg_match('/^880/', $contactNo)) {
                        $contactNo = substr($contactNo, 3);
                    } else {
                        throw new \Exception('Invalid contact number in CSV: ' . $row['contact_no']);
                    }
                } else {
                    $contactNo = null;
                }

                if (!is_null($row['alternative_contact_no'])) {
                    $altContactNo = preg_replace('/[^0-9]/', '', $row['alternative_contact_no']);
                    if (preg_match('/^1/', $altContactNo)) {
                        $altContactNo = '0' . $altContactNo;
                    } elseif (preg_match('/^880/', $altContactNo)) {
                        $altContactNo = substr($altContactNo, 3);
                    } else {
                        throw new \Exception('Invalid alternative contact number in CSV: ' . $row['alternative_contact_no']);
                    }
                } else {
                    $altContactNo = null;
                }

                $userData[] = [
                    'id' => $userId,
                    "organization_id" => Auth::user()->organization_id,
                    'name' => $row['name'],
                    'email' => $row['email'],
                    'password' => isset($row['password']) ? Hash::make($row['password']) : Hash::make('123456bB'),
                    'contact_no' => $contactNo,
                    'address' => $row['current_address'],
                    'user_type' => 'Student',
                    'created_at' => now(),
                    'updated_at' => now(),
                ];

                $studentCodeNumber = ++$lastStudentId;

                $studentData[] = [
                    "user_id" => $userId,
                    "organization_id" => Auth::user()->organization_id,
                    'student_code' => 'SC-' . str_pad($studentCodeNumber + 1, 5, 0, STR_PAD_LEFT),
                    "contact_no" => $contactNo,
                    "alternative_contact_no" => $altContactNo,
                    'is_active' => 1,
                    'created_at' => now(),
                    'updated_at' => now(),

                    "name" => $row['name'],
                    "student_id" => $row['student_id'],
                    "email" => $row['email'],
                    "gender" => $row['gender'],
                    "blood_group" => $row['blood_group'],
                    "father_name" => $row['father_name'],
                    "mother_name" => $row['mother_name'],
                    "religion" => $row['religion'],
                    "marital_status" => $row['marital_status'],
                    "date_of_birth" => $row['date_of_birth'],
                    "current_address" => $row['current_address'],
                    "permanent_address" => $row['permanent_address'],
                    "interests" => $row['interests'],
                    "nid_no" => $row['nid_no'],
                    "birth_certificate_no" => $row['birth_certificate_no'],
                    "passport_no" => $row['passport_no'],
                    "education" => $row['education'],
                    "institute" => $row['institute']
                ];
            }

            User::insert($userData);
            StudentInformation::insert($studentData);

            DB::commit();
            return $this->apiResponse(count($studentData), 'Student imported successfully', true, 200);
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->apiResponse([], $e->getMessage(), false, 500);
        } finally {
            // Unlock tables
            DB::statement('UNLOCK TABLES');
        }
    }

}

