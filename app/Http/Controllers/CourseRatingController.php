<?php

namespace App\Http\Controllers;

use App\Models\CourseRating;
use App\Models\Course;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Http\Requests\CourseRatingStoreRequest;

use App\Http\Controllers\Mobile\ContentController;
class CourseRatingController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        try {
            $ratings = CourseRating::with('user', 'course')->paginate(10); // Paginated results
            return $this->apiResponse($ratings, 'Ratings retrieved successfully!', true);
        } catch (\Exception $e) {
            return $this->apiResponse(null, 'Something went wrong!', false, 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(CourseRatingStoreRequest $request)
    {
        // try {
            $validated = $request->validated();

            $contentController = new ContentController($request);
            if (!$contentController->isBought(Auth::user(), $validated['course_id'])) {
                return $this->apiResponse(null, 'You are not able to rate this course!', false, 400);
            }
            $rating = CourseRating::create([
                'organization_id' => Auth::user()->organization_id,
                'course_id' => $validated['course_id'],
                'user_id' => Auth::id(),
                'rating' => $validated['rating'],
                'review' => $validated['review'] ?? null,
                'is_active' => true,
            ]);

            $activeRatings = CourseRating::where('course_id', $validated['course_id'])->where('is_active', true)->get();
            $totalRating = 0;
            foreach ($activeRatings as $activeRating) {
                $totalRating += $activeRating->rating;
            }
            $averageRating = $totalRating / $activeRatings->count();

            Course::where('id', $validated['course_id'])->update([
                'rating' => $averageRating
            ]);

            return $this->apiResponse($rating, 'Rating added successfully!', true, 201);
        // } catch (\Exception $e) {
        //     return $this->apiResponse(null, 'Something went wrong!', false, 500);
        // }
    }

    /**
     * Display the specified resource.
     */
    public function show(CourseRating $courseRating)
    {
        try {
            return $this->apiResponse($courseRating, 'Rating retrieved successfully!', true);
        } catch (\Exception $e) {
            return $this->apiResponse(null, 'Something went wrong!', false, 500);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, CourseRating $courseRating)
    {
        try {
            $validated = $request->validate([
                'rating' => 'required|integer|min:1|max:5',
                'review' => 'nullable|string'
            ]);

            $courseRating->update($validated);

            return $this->apiResponse($courseRating, 'Rating updated successfully!', true);
        } catch (\Exception $e) {
            return $this->apiResponse(null, 'Something went wrong!', false, 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(CourseRating $courseRating)
    {
        try {
            $courseRating->delete();

            return $this->apiResponse(null, 'Rating deleted successfully!', true);
        } catch (\Exception $e) {
            return $this->apiResponse(null, 'Something went wrong!', false, 500);
        }
    }
}

