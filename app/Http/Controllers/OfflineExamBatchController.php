<?php

namespace App\Http\Controllers;

use App\Models\OfflineExamBatch;
use Illuminate\Http\Request;

class OfflineExamBatchController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(OfflineExamBatch $offlineExamBatch)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(OfflineExamBatch $offlineExamBatch)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, OfflineExamBatch $offlineExamBatch)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(OfflineExamBatch $offlineExamBatch)
    {
        //
    }
}
