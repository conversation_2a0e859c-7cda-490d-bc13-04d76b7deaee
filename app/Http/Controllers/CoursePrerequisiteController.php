<?php

namespace App\Http\Controllers;

use App\Models\CoursePrerequisite;
use Illuminate\Http\Request;

class CoursePrerequisiteController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(CoursePrerequisite $coursePrerequisite)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(CoursePrerequisite $coursePrerequisite)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, CoursePrerequisite $coursePrerequisite)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(CoursePrerequisite $coursePrerequisite)
    {
        //
    }
}
