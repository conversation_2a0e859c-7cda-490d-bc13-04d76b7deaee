<?php

namespace App\Http\Controllers;

use App\Models\TemplateMenu;
use Illuminate\Http\Request;

class TemplateMenuController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(TemplateMenu $templateMenu)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(TemplateMenu $templateMenu)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, TemplateMenu $templateMenu)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(TemplateMenu $templateMenu)
    {
        //
    }
}
