<?php

namespace App\Http\Controllers;

use App\Models\CertificateTemplate;
use Illuminate\Http\Request;
use App\Models\Course;
use Auth;
class CertificateTemplateController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'organization_id' => 'required|integer|exists:organizations,id',
            'course_id' => 'required|integer|exists:courses,id',
            'background_image' => 'required|image|mimes:jpeg,png,jpg,gif,svg',
            'logo' => 'required|image|mimes:jpeg,png,jpg,gif,svg',
            'signature' => 'required|image|mimes:png,svg',

        ]);
        $course = Course::find($request->course_id);

        $data = $request->only([
            'organization_id',
            'course_id',
            'background_image',
            'logo',
            'signature',
            'authorize_person',
            'designation',
            'prefix',
            'is_active'
        ]);

        $data['background_image'] = $this->imageUpload($request, 'background_image', 'certificate_bg');
        $data['signature'] = $this->imageUpload($request, 'signature', 'certificate_signature');
        $data['logo'] = $this->imageUpload($request, 'logo', 'certificate_logo');
        $data['organization_id'] = $course->organization_id;

        $certificateTemplate = CertificateTemplate::create($data);

        return $this->apiResponse([], 'Certificate template created successfully', true, 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(CertificateTemplate $certificateTemplate)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(CertificateTemplate $certificateTemplate)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, CertificateTemplate $certificateTemplate)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(CertificateTemplate $certificateTemplate)
    {
        //
    }
}
