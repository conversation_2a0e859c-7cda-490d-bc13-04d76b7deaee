<?php

namespace App\Http\Controllers;

use App\Models\Testimonial;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Http\Requests\TestimonialStoreRequest;
use App\Http\Requests\TestimonialUpdateRequest;

class TestimonialController extends Controller
{

    public function testimonialList (Request $request) {
        $testimonial_list = Testimonial::select('id', 'name', 'designation', 'user_type', 'message', 'image')->where('is_active', true)->get();

        return $this->apiResponse($testimonial_list, 'Testimonials list', true, 200);
    }
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        try {
            $query = Testimonial::query();
            if (Auth::user()->organization_id) {
                $query->where('organization_id', Auth::user()->organization_id);
            }

            if ($request->has('user_type')) {
                $query->where('user_type', $request->user_type);
            }

            $testimonials = $query->get();
            return $this->apiResponse($testimonials, 'Testimonials list', true, 200);
        } catch (\Throwable $th) {
            return $this->apiResponse([], $th->getMessage(), false, 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(TestimonialStoreRequest $request)
    {
        try {
            $data = $request->validatedData();

            if ($request->hasFile('image')) {
                $data['image'] = $this->imageUpload($request, 'image', 'testimonial');
            }

            $testimonial = Testimonial::create($data);

            return $this->apiResponse($testimonial, 'Testimonial Created Successfully', true, 201);
        } catch (\Throwable $th) {
            return $this->apiResponse([], $th->getMessage(), false, 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Testimonial $testimonial)
    {
        try {
            return $this->apiResponse($testimonial, 'Testimonial Details', true, 200);
        } catch (\Throwable $th) {
            return $this->apiResponse([], $th->getMessage(), false, 500);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(TestimonialUpdateRequest $request, Testimonial $testimonial)
    {
        try {
            $data = $request->validatedData();

            if ($request->hasFile('image')) {
                $data['image'] = $this->imageUpload($request, 'image', 'testimonial', $testimonial->image);
            }

            $testimonial->update($data);

            return $this->apiResponse($testimonial, 'Testimonial Updated Successfully', true, 200);
        } catch (\Throwable $th) {
            return $this->apiResponse([], $th->getMessage(), false, 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Testimonial $testimonial)
    {
        try {
            $testimonial->delete();
            return $this->apiResponse([], 'Testimonial Deleted Successfully', true, 200);
        } catch (\Throwable $th) {
            return $this->apiResponse([], $th->getMessage(), false, 500);
        }
    }
}

