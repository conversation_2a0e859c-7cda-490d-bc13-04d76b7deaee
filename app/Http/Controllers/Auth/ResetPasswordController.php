<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Auth\Events\PasswordReset;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Facades\Validator;

class ResetPasswordController extends Controller
{
    /**
     * Show the form to reset the password.
     *
     * @param  string  $token
     * @return \Illuminate\View\View
     */
    public function showResetForm($token)
    {
        return view('reset-password', ['token' => $token]);
    }

    /**
     * Handle a password reset request.
     *
     * @return \Illuminate\Http\Response
     */
    public function reset(Request $request)
    {
        // Validate the input fields

        $validator = Validator::make($request->all(), [
            'token' => 'required',
            'email' => 'required|email',
            'password' => 'required|min:8|confirmed',
        ], [
            'name.required' => 'Please provide your name.',
            'email.required' => 'We need your email address.',
            'password.required' => 'A password is required to proceed.',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        // Attempt to reset the user's password
        $status = Password::reset(
            $request->only('email', 'password', 'password_confirmation', 'token'),
            function ($user, $password) {
                $user->forceFill([
                    'password' => Hash::make($password),
                ])->save();

                event(new PasswordReset($user));
            }
        );

        // Handle the outcome of the password reset
        return $status === Password::PASSWORD_RESET
            ? redirect()->back()->with('status', __($status)) // Redirect to login with a status message
            : back()->withErrors(['email' => [__($status)]]); // Return back with error messages
    }
}
