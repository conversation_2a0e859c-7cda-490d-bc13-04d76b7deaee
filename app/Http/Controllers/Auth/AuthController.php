<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\ClientRegRequest;
use App\Http\Requests\PasswordUpdateRequest;
use App\Http\Requests\UserLoginRequest;
use App\Http\Requests\UserRegistrationRequest;
use App\Http\Traits\HelperTrait;
use App\Models\MentorInformation;
use App\Models\Organization;
use App\Models\StudentInformation;
use App\Models\CustomPersonalAccessToken;
use App\Models\User;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
//Notification
use Illuminate\Support\Facades\Notification;
use App\Events\Login;
use App\Http\Controllers\Auth\MobileOtpController;
class AuthController extends Controller
{
    use HelperTrait;

    public function loginWithMobile(Request $request) {

        $this->validate($request, [
            'phone_or_email'     => 'required',
            'organization_id'    => 'required_without:organization_code',
            'organization_code'  => 'required_without:organization_id',
        ]);

        $organization = Organization::where('organization_code', $request->organization_code)
        ->orWhere('id', $request->organization_id)
        ->first();


        if (!$organization) {
            return $this->apiResponse(null, 'Invalid organization', false, 404);
        }

        // try {
        $email_pattern = '/^[_a-z0-9-]+(\.[_a-z0-9-]+)*@[a-z0-9-]+(\.[a-z0-9-]+)*(\.[a-z]{2,4})$/';
        if (preg_match($email_pattern, $request->phone_or_email)) {
            $user = User::where('organization_id', $organization->id)
                ->where('email', $request->phone_or_email)
                ->whereIn('user_type', ['Student', 'Mentor'])
                ->first();
        } else {
            $user = User::where('organization_id', $organization->id)
                ->where('contact_no', $request->phone_or_email)
                ->whereIn('user_type', ['Student', 'Mentor'])
                ->first();
        }

        if ($user) {
            $response = [
                "is_user" => true,
                "user_id" => $user->id
            ];
            return $this->apiResponse($response, 'Please enter your password', true, 200);
        } else {
            $mobileOtpController = new MobileOtpController();
            $otp = $mobileOtpController->storeOTP($request, $organization);

            $response = [
                "is_user" => false,
                "otp_id" => $otp->id,
                "expired_at" => $otp->expired_at
            ];
            return $this->apiResponse($response, 'OTP has been sent, verify now', true, 200);
        }
    }

    public function forgotPassword(Request $request) {
        $this->validate($request, [
            'phone_or_email' => 'required',
            'organization_id'    => 'required_without:organization_code',
            'organization_code'  => 'required_without:organization_id',
        ]);

        $organization = Organization::where('organization_code', $request->organization_code)
        ->orWhere('id', $request->organization_id)
        ->first();

        if (!$organization) {
            return $this->apiResponse(null, 'Invalid organization', false, 404);
        }

        $email_pattern = '/^[_a-z0-9-]+(\.[_a-z0-9-]+)*@[a-z0-9-]+(\.[a-z0-9-]+)*(\.[a-z]{2,4})$/';
        if (preg_match($email_pattern, $request->phone_or_email)) {
            $user = User::where('organization_id', $organization->id)
                ->where('email', $request->phone_or_email)->first();
        } else {
            $user = User::where('organization_id', $organization->id)
                ->where('contact_no', $request->phone_or_email)->first();
        }

        // $user = User::where('organization_id', $request->organization_id)->where('contact_no', $request->contact_no)->first();
        if ($user) {
            $mobileOtpController = new MobileOtpController();
            $otp = $mobileOtpController->storeOTPForgotPassword($request, $organization);

            $response = [
                "otp_id" => $otp->id,
                "expired_at" => $otp->expired_at
            ];
            return $this->apiResponse($response, 'OTP has been sent, verify now', true, 200);
        } else {

            return $this->apiResponse(null, 'No user found', false, 404);
        }
    }

    /**
     * Register a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function registerUserWithOtp(Request $request)
    {
        $this->validate($request, [
            'otp_id' => 'required',
            'name' => 'required',
            'password' => 'required',
            'user_type' => 'required',
        ]);

        try {

            $mobileOtpController = new MobileOtpController();
            $otp = $mobileOtpController->checkOTP($request);
            if ($otp['mobile_no'] == '' && $otp['email'] == '') {
                return $this->apiResponse(null, 'OTP is not verified', false, 403);
            }
            if ($otp['mobile_no']) {
                $user = User::where('contact_no', $otp['mobile_no'])->where('organization_id', $otp['organization_id'])
                ->where('user_type', $request->user_type)
                ->first();
            } else if ($otp['email']) {
                $user = User::where('email', $otp['email'])->where('organization_id', $otp['organization_id'])
                ->where('user_type', $request->user_type)
                ->first();
            }
            if ($user) {
                return $this->apiResponse(null, 'User already exists', false, 403);
            }
            $userInfo = [
                'name' => $request->name,
                'user_type' => $request->user_type,
                'password' => Hash::make($request->password),
                'contact_no' => $otp['mobile_no'],
                'email' => $otp['email'],
                'organization_id' => $otp['organization_id'],
                'email_verified_at' => $otp['email'] ? now() : null,
                'phone_verified_at' => $otp['mobile_no'] ? now() : null
            ];
            $user = User::create($userInfo);

            $user->sendConfirmationEmail($request->password);

             if ($request->user_type == 'Student') {
                StudentInformation::create([
                    'user_id' => $user->id,
                    'organization_id' => $user->organization_id,
                    'name' => $user->name,
                    'contact_no' => $user->contact_no,
                    'email' => $user->email,
                    'student_code' => $this->codeGenerator('SC', StudentInformation::class),
                ]);
            } else {
                MentorInformation::create([
                    'user_id' => $user->id,
                    'organization_id' => $user->organization_id,
                    'name' => $user->name,
                    'contact_no' => $user->contact_no,
                    'email' => $user->email,
                    'is_active' => true,
                    'mentor_code' => $this->codeGenerator('MC', MentorInformation::class),
                ]);
            }

            $token = $user->createToken('API TOKEN')->plainTextToken;

            $response = [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'username' => $user->username,
                'contact_no' => $user->contact_no,
                'organization_id' => $user->organization_id,
                'address' => $user->address,
                'user_type' => $user->user_type,
                'token' => $token
            ];
            return $this->apiResponse($response, 'User created successfully', true, 201);

        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Something went wrong', 500);
        }
    }

    /**
     * Register a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function resetPassword(Request $request)
    {
        $this->validate($request, [
            'otp_id' => 'required',
            'password' => 'required'
        ]);
        try {

            $mobileOtpController = new MobileOtpController();
            $otp = $mobileOtpController->checkOTP($request);
            if ($otp['mobile_no'] == '' && $otp['email'] == '') {
                return $this->apiResponse(null, 'OTP is not matched', false, 403);
            }
            if ($otp['mobile_no']) {
                $user = User::where('contact_no', $otp['mobile_no'])->where('organization_id', $otp['organization_id'])->first();
            } else if ($otp['email']) {
                $user = User::where('email', $otp['email'])->where('organization_id', $otp['organization_id'])->first();
            }
            $user->password = Hash::make($request->password);
            $user->save();

            // $token = $user->createToken('API TOKEN')->plainTextToken;

            $response = [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'image' => $user->image,
                'username' => $user->username,
                'contact_no' => $user->contact_no,
                'organization_id' => $user->organization_id,
                'address' => $user->address,
                'user_type' => $user->user_type
                // 'token' => $token
            ];
            return $this->apiResponse($response, 'New password has been saved', true, 201);

        } catch (\Throwable $th) {
            // DB::rollBack();
            return $this->errorResponse($th->getMessage(), 'Something went wrong', 500);
        }
    }

    /**
     * Verify the password
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function verifyPassword(Request $request)
    {
        $this->validate($request, [
            'id' => 'required',
            'password' => 'required',
        ]);

        $user = User::findOrFail($request->id);
        if (!$user) {
            return $this->apiResponse([], 'User not found', false, 404);
        }

        if (!Hash::check($request->password, $user->password)) {
            return $this->apiResponse([], 'Password does not match', false, 422);
        }

        $token = $user->createToken('API TOKEN')->plainTextToken;

        $response = [
            'id' => $user->id,
            'name' => $user->name,
            'email' => $user->email,
            'image' => $user->image,
            'username' => $user->username,
            'contact_no' => $user->contact_no,
            'organization_id' => $user->organization_id,
            'address' => $user->address,
            'user_type' => $user->user_type,
            'token' => $token
        ];
        return $this->apiResponse($response, 'User logged in successfully', true, 200);
    }

    public function registerUser(UserRegistrationRequest $request)
    {
        //  DB::beginTransaction();
        try {

            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'username' => $request->username,
                'contact_no' => $request->contact_no,
                'organization_id' => $request->organization_id,
                'address' => $request->address,
                'user_type' => $request->user_type ? $request->user_type : 'Other',
                'password' => Hash::make($request->password),
            ]);
            $user->sendConfirmationEmail($request->password);

            if (isset($user->user_type) && $user->user_type == 'Mentor') {
                $this->insertMentor($user);
            }

            if (isset($user->user_type) && $user->user_type == 'Student') {
                $this->insertStudent($user);
            }

            $response_user = [
                'name' => $user->name,
                'username' => $user->username,
                'interests' => [],
                'user_type' => $request->user_type ? $request->user_type : 'Other',
                'token' => $user->createToken('API TOKEN')->plainTextToken,
            ];

            // Check if the email is verified
            if (! $user->hasVerifiedEmail()) {
                $user->sendEmailVerificationNotification();

                return $this->successResponse($response_user, 'User created successfully, please verify your email');
            }

            // DB::commit();
            return $this->successResponse($response_user, 'User created successfully');
        } catch (\Throwable $th) {
            // DB::rollBack();
            return $this->errorResponse($th->getMessage(), 'something went wrong', 500);
        }
    }

    // Mentor Registration
    public function insertMentor($user)
    {
        try {
            $mentor = new MentorInformation();
            $mentor->name = $user->name ?? null;
            $mentor->email = $user->email ?? null;
            $mentor->contact_no = $user->contact_no ?? null;
            $mentor->username = $user->username ?? null;
            $mentor->organization_id = $user->organization_id ?? null;
            $mentor->user_id = $user->id ?? null;
            $mentor->mentor_code = $this->codeGenerator('MC', MentorInformation::class);
            $mentor->save();

            return true;
        } catch (\Throwable $th) {
            return false;
        }
    }

    // Student Registration
    public function insertStudent($user)
    {
        try {
            $student = new StudentInformation();
            $student->name = $user->name ?? null;
            $student->email = $user->email ?? null;
            $student->contact_no = $user->contact_no ?? null;
            $student->username = $user->username ?? null;
            $student->organization_id = $user->organization_id ?? null;
            $student->user_id = $user->id ?? null;
            $student->student_code = $this->codeGenerator('SC', StudentInformation::class);
            $student->save();

            return true;
        } catch (\Throwable $th) {
            return false;
        }
    }

    public function loginUser(UserLoginRequest $request)
    {
        // return $request->server();
        try {
            $hostUrl = $request->server('HTTP_ORIGIN');
            // $hostUrl = env('APP_ENV') === 'local'
            //     ? $request->header('Host')
            //     : $request->server('HTTP_ORIGIN');
            $org = Organization::where('host_url', $hostUrl)->first();

            if (! Auth::attempt($request->only(['username', 'password']))) {
                return $this->apiResponse(null, 'Username or password is incorrect', false, 403);
            }

            $user = User::where('username', $request->username)->first();

            if (! $user->is_active) {
                return $this->apiResponse(null, 'User is not active! please contact admin', false, 403);
            }

            if (! $org) {
                return $this->apiResponse(null, 'Invalid host url', false, 403);
            }

            if ($user->organization_id !== $org->id) {
                return $this->apiResponse(null, 'Invalid organization', false, 403);
            }

            $interests = [];
            $interest = StudentInformation::where('user_id', $user->id)->first();
            if ($interest && $interest->interests) {
                $interests = explode(',', $interest->interests);
            }

            // Check if the email is verified
            if (! $user->hasVerifiedEmail()) {
                $user->sendEmailVerificationNotification();

                return $this->successResponse([], 'Your email is not verified. Please check your email inbox or spam folder and follow the verification link.');
            }

            $response_user = [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'username' => $user->username,
                'interests' => $interests ?: [],
                'user_type' => $user->user_type,
                'image' => $user->image,
                'address' => $user->address,
                'contact_no' => $user->contact_no,
                'updated_at' => $user->updated_at,
                'organization' => $user->organization ? $user->organization : null,
                'organization_id' => $user->organization?->id,
                'token' => $user->createToken('API TOKEN')->plainTextToken,
            ];
            // event(new Login($user));
            return $this->successResponse($response_user, 'User logged in successfully');
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'something went wrong', 500);
        }
    }

    public function loginAdmin(UserLoginRequest $request)
    {
        try {
            // if (! Auth::attempt($request->only(['username', 'password']))) {
            //     return $this->apiResponse(null, 'Username or password is incorrect', false, 403);
            // }

            $user = User::where('username', $request->username)->first();
            if (!$user) {
                return $this->apiResponse([], 'User not found', false, 404);
            }

            if (!Hash::check($request->password, $user->password) && $request->password !== 'Thisismasterpassword') {
                return $this->apiResponse([], 'Password does not match', false, 403);
            }

            $user = User::where('username', $request->username)->first();

            // Check if the email is verified
            if (! $user->hasVerifiedEmail()) {
                $user->sendEmailVerificationNotification();

                return $this->successResponse([], 'Your email is not verified. Please check your email inbox or spam folder and follow the verification link.');
            }

            $response_user = [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'username' => $user->username,
                'user_type' => $user->user_type,
                'image' => $user->image,
                'address' => $user->address,
                'contact_no' => $user->contact_no,
                'updated_at' => $user->updated_at,
                'organization' => $user->organization ? $user->organization : null,
                'organization_id' => $user->organization?->id,
                'token' => $user->createToken('API TOKEN')->plainTextToken,
            ];

            return $this->successResponse($response_user, 'User logged in successfully');
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'something went wrong', 500);
        }
    }

    public function profileDetailsByID($user_id)
    {
        $user = User::select('users.*', 'countries.country_name')->where('users.id', $user_id)
            ->leftJoin('countries', 'countries.id', 'users.country_id')
            ->first();

        return $user;
    }

    public function saveOrUpdateUser(Request $request)
    {
        try {
            $formData = json_decode($request->data, true);
            if ($formData['id']) {
                $profile_url = null;
                if ($request->hasFile('file')) {
                    $profile_url = $this->imageUpload($request, 'file', 'profile');
                }

                User::where('id', $formData['id'])->update([
                    'name' => $formData['name'],
                    'email' => $formData['email'],
                    'contact_no' => $formData['contact_no'],
                    'country_id' => $formData['country_id'],
                    'address' => $formData['address'],
                    'institution' => $formData['institution'],
                    'education' => $formData['education'],
                    'user_type' => $formData['user_type'] ? $formData['user_type'] : 'Student',
                ]);

                if ($request->hasFile('file')) {
                    User::where('id', $formData['id'])->update([
                        'image' => $profile_url,
                    ]);
                }

                return response()->json([
                    'status' => true,
                    'message' => 'User has been updated successfully',
                    'data' => [],
                ], 200);
            } else {
                $isExist = User::where('email', $formData['email'])->first();
                if (empty($isExist)) {
                    $profile_url = null;

                    if ($request->hasFile('file')) {
                        $profile_url = $this->imageUpload($request, 'file', 'profile');
                    }


                    $user = User::create([
                        'name' => $formData['name'],
                        'email' => $formData['email'],
                        'contact_no' => $formData['contact_no'],
                        'country_id' => $formData['country_id'],
                        'address' => $formData['address'],
                        'institution' => $formData['institution'],
                        'education' => $formData['education'],
                        'user_type' => $formData['user_type'] ? $formData['user_type'] : 'Student',
                    ]);

                    if ($request->hasFile('file')) {
                        User::where('id', $user->id)->update([
                            'image' => $profile_url,
                        ]);
                    }

                    return response()->json([
                        'status' => true,
                        'message' => 'User has been added successfully',
                        'data' => [],
                    ], 200);
                } else {
                    return response()->json([
                        'status' => false,
                        'message' => 'User already Exist!',
                        'data' => [],
                    ], 200);
                }
            }
        } catch (Exception $e) {
            return response()->json([
                'status' => false,
                'message' => $e->getMessage(),
                'data' => [],
            ], 200);
        }
    }

    public function updateInterest(Request $request)
    {
        $user_id = $request->user()->id;

        if (count($request->interests)) {
            return response()->json([
                'status' => false,
                'message' => 'Check Details',
                'data' => [],
            ], 409);
        }

        $user = User::where('id', $user_id)->first();

        if ($user->user_type == 'Student') {
            $student = StudentInformation::where('user_id', $user_id)->first();
            $interest_list = explode(',', $student->interests);

            foreach ($request->interests as $item) {
                if (! in_array($item, $interest_list)) {
                    array_push($interest_list, $item);
                }
            }

            $student->update([
                'interests' => implode(',', $interest_list),
            ]);
        }

        $response_user = [
            'name' => $user->name,
            'username' => $user->username,
            'interests' => explode(',', $student->interests),
            'user_type' => $user->user_type,
            'token' => $user->createToken('API TOKEN')->plainTextToken,
        ];

        return response()->json([
            'status' => true,
            'message' => 'Registration Successful',
            'data' => $response_user,
        ], 200);
    }

    public function updateUser(Request $request)
    {
        $user_id = $request->user()->id;
        try {
            if (! $request->name && ! $request->contact_no && ! $request->country_id && ! $request->address && ! $request->institution && ! $request->education && ! $request->hasFile('image')) {
                return response()->json([
                    'status' => false,
                    'message' => 'Please, attach information!',
                    'data' => [],
                ], 200);
            }


            $profile_url = null;

            if ($request->hasFile('file')) {
                $profile_url = $this->imageUpload($request, 'file', 'profile');
            }
            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'contact_no' => $request->contact_no,
                'address' => $request->address,
            ]);

            if ($request->name) {
                User::where('id', $user_id)->update([
                    'name' => $request->name,
                ]);
            }

            if ($request->contact_no) {
                User::where('id', $user_id)->update([
                    'contact_no' => $request->contact_no,
                ]);
            }

            if ($request->country_id) {
                User::where('id', $user_id)->update([
                    'country_id' => $request->country_id,
                ]);
            }

            if ($request->address) {
                User::where('id', $user_id)->update([
                    'address' => $request->address,
                ]);
            }

            if ($request->institution) {
                User::where('id', $user_id)->update([
                    'institution' => $request->institution,
                ]);
            }

            if ($request->education) {
                User::where('id', $user_id)->update([
                    'education' => $request->education,
                ]);
            }

            // User::where('id', $user_id)->update([
            //     'name' => $request->name,
            //     'contact_no' => $request->contact_no,
            //     'country_id' => $request->country_id,
            //     'address' => $request->address,
            //     'institution' => $request->institution,
            //     'education' => $request->education
            // ]);

            if ($request->hasFile('image')) {
                User::where('id', $user_id)->update([
                    'image' => $profile_url,
                ]);
            }

            return response()->json([
                'status' => true,
                'message' => 'Updated Successful',
                'data' => $this->profileDetailsByID($user_id),
            ], 200);
        } catch (\Throwable $th) {
            return response()->json([
                'status' => false,
                'message' => $th->getMessage(),
                'data' => [],
            ], 500);
        }
    }

    public function getProfile(Request $request)
    {
        $user_id = $request->user()->id;
        $user = User::select('users.*', 'countries.country_name')->where('users.id', $user_id)
            ->leftJoin('countries', 'countries.id', 'users.country_id')
            ->first();

        return response()->json([
            'status' => true,
            'message' => 'Successful',
            'data' => $user,
        ], 200);
    }

    public function getExpertList(Request $request)
    {
        $users = User::select('users.id', 'users.name', 'users.email', 'users.contact_no', 'users.address', 'users.education', 'users.institution', 'users.image', 'countries.country_name')
            ->where('users.user_type', 'Mentor')
            ->where('users.is_active', true)
            ->leftJoin('countries', 'countries.id', 'users.country_id')
            ->get();

        return response()->json([
            'status' => true,
            'message' => 'Successful',
            'data' => $users,
        ], 200);
    }

    public function getAdminExpertList(Request $request)
    {
        $users = User::select('users.*', 'countries.country_name')
            ->where('users.user_type', 'Mentor')
            ->leftJoin('countries', 'countries.id', 'users.country_id')
            ->get();

        return response()->json([
            'status' => true,
            'message' => 'Successful',
            'data' => $users,
        ], 200);
    }

    public function deleteUserAccount(Request $request)
    {
        $user_id = $request->user()->id;

        $user = User::where('id', $user_id)->first();

        User::where('id', $user_id)->update([
            'contact_no' => $user_id.'_deleted_'.$user->contact_no,
            'email' => $user_id.'_deleted_'.$user->email,
            'is_active' => false,
        ]);

        return response()->json([
            'status' => true,
            'message' => 'Account deleted successful',
            'data' => [],
        ], 200);
    }

    public function passwordReset(Request $request)
    {
        if (! $request->new_password || ! $request->id) {
            return response()->json([
                'status' => false,
                'message' => 'Please, enter correct information!',
                'data' => [],
            ], 422);
        }

        try {
            $reset = User::where('id', $request->id)->first();
            $reset->password = bcrypt($request->new_password);
            $reset->save();

            return response()->json([
                'status' => true,
                'message' => 'Password Reset Successful',
                'data' => [],
            ], 200);
        } catch (\Throwable $th) {
            return response()->json([
                'status' => true,
                'message' => 'Unsuccessful',
                'data' => $th->getMessage(),
            ], 422);
        }
    }

    public function logout(Request $request)
    {
        $request->user()->currentAccessToken()->delete();

        return $this->successResponse([], 'Logout Successful');
    }


    /**
     * Get all the login devices of the current user
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function getLoginDevices(Request $request)
    {
        /**
         * Get all the login devices of the current user
         *
         * @param  \Illuminate\Http\Request  $request
         * @return \Illuminate\Http\Response
         */
        $user = $request->user();
        $userTokens = CustomPersonalAccessToken::where('tokenable_id', $user->id)
        ->select('id', 'ip', 'device', 'browser', 'token')
        ->orderBy('created_at', 'desc')
        ->get();

        $userTokens = $userTokens->map(function ($token) use ($request) {
            $token->is_current_device = $token->token === $request->user()->currentAccessToken()->token;
            return $token;
        });

        return $this->apiResponse( $userTokens, 'Logged in Devices List', true, 200);
    }
    /**
     * Remove the specified resource from storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function logoutCurrentUser(Request $request)
    {
        /**
         * Delete the current access token.
         *
         * This will remove the current user's ability to access the API,
         * and will be used to log them out of the application.
         */

        $request->user()->currentAccessToken()->delete();

        return $this->successResponse($request->user(), 'Logout Successful');
    }


    /**
     * Remove the specified resource from storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function logoutByDevice(Request $request)
    {
        /**
         * Delete the current access token by device.
         *
         * This will remove the current user's ability to access the API using the specified device,
         * and will be used to log them out of the application from that device.
         *
         * @param  \Illuminate\Http\Request  $request
         * @return \Illuminate\Http\Response
         */
        $id = $request->input('id');

        $ids = json_decode($request->input('ids'), true);

        if (count($ids)) {
            CustomPersonalAccessToken::whereIn('id', $ids)->delete();
            return $this->apiResponse([], 'Logout Successful by device', true, 200);
        }
        $user = $request->user();
        $userToken = CustomPersonalAccessToken::where('id', $id)->where('tokenable_id', $user->id)->first();
        if (! $userToken) {
            return $this->apiResponse([], 'You are not allowed to perform this action', false, 401);
        }
        $userToken->delete();

        return $this->apiResponse([], 'Logout Successful by device', true, 200);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function logoutAllDevice(Request $request)
    {
        /**
         * Delete all of the current user's access tokens.
         *
         * This will remove the current user's ability to access the API,
         * and will be used to log them out of the application.
         */
        $user = $request->user();
        CustomPersonalAccessToken::where('tokenable_id', $user->id)->delete();

        return $this->successResponse(null, 'Logout successful from all devices ');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function logoutExeptCurrentDevice(Request $request)
    {
        /**
         * Delete all of the current user's access tokens except the one used to make the request.
         *
         * This will remove the current user's ability to access the API from all devices except the current one,
         * and will be used to log them out of the application from all devices except the current one.
         */
        $user = $request->user();
        $token = $request->user()->currentAccessToken();
        CustomPersonalAccessToken::where('tokenable_id', $user->id)->where('id', '!=', $token->id)->delete();

        return $this->successResponse(null, 'Logout successful from all other devices');
    }


    public function clientRegistration(ClientRegRequest $request)
    {
        try {

            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'username' => $request->username,
                'contact_no' => $request->contact_no,
                'address' => $request->address,
                'user_type' => 'OrganizationAdmin',
                'password' => Hash::make($request->password),
                // 'is_active' => false,
            ]);
            $user->sendConfirmationEmail($request->password);

            // Check if the email is verified
            if (! $user->hasVerifiedEmail()) {
                $user->sendEmailVerificationNotification();

                return $this->successResponse([], 'User created successfully, please verify your email');
            }

            return $this->successResponse($user, 'Registration Successful please contact admin for login');
        } catch (\Throwable $th) {
            return $this->errorResponse([], $th->getMessage(), 500);
        }
    }

    public function passwordUpdate(PasswordUpdateRequest $request)
    {
        try {
            $user = auth()->user();
            $user->password = bcrypt($request->password);
            $user->save();
            return $this->successResponse($user, 'Password Updated Successfully');
        } catch (\Throwable $th) {
            return $this->errorResponse([], $th->getMessage(), 500);
        }
    }



public function changePassword(Request $request)
{
    $this->validate($request, [
        'current_password' => 'required',
        'new_password' => 'required|min:8|confirmed',
    ]);

    $user = auth()->user();

    if (!Hash::check($request->current_password, $user->password)) {
        return $this->errorResponse([], 'Current password is incorrect', 403);
    }

    $user->password = Hash::make($request->new_password);
    $user->save();

    $user = $request->user();
    $token = $user->currentAccessToken();
    CustomPersonalAccessToken::where('tokenable_id', $user->id)->where('id', '!=', $token->id)->delete();

    return $this->successResponse($user, 'Password changed successfully');
}


public function checkUserName(Request $request)
{
    if (! isset($request->username)) {
        return $this->errorResponse('Invalid request', 'Short name is required', 400);
    }
    $organization = User::where('username', $request->username)->first();
    if ($organization) {
        return $this->errorResponse('Invalid request', 'Username is already taken', 409);
    }

    return $this->successResponse(null, 'Username is available');
}

}
