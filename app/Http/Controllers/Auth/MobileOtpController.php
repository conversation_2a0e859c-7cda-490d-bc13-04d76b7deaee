<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\MobileOtp;
use App\Models\User;
use App\Http\Traits\HelperTrait;
use Illuminate\Http\Request;
use App\Notifications\SendOTPEmail;

class MobileOtpController extends Controller
{

    use HelperTrait;
    /**
     * Store a newly created resource in storage.
     */
    public function storeOTP(Request $request, $organization)
    {
        $phone = null;
        $email = null;
        $email_pattern = '/^[_a-z0-9-]+(\.[_a-z0-9-]+)*@[a-z0-9-]+(\.[a-z0-9-]+)*(\.[a-z]{2,4})$/';
        if (preg_match($email_pattern, $request->phone_or_email)) {
            $email = $request->phone_or_email;
            $otpModel = MobileOtp::where('email', $request->phone_or_email)
            ->where('organization_id', $organization->id)->where('expired_at', '>', now())->first();

        } else {
            $phone = $request->phone_or_email;
            $otpModel = MobileOtp::where('mobile_no', $request->phone_or_email)
            ->where('organization_id', $organization->id)->where('expired_at', '>', now())->first();
        }

        if ($otpModel) {
            $otpModel->message = "We have already sent you an OTP.";
            return $otpModel;
        }

        $otp = $this->generateOTP();
        $data = [
            "mobile_no" => $phone,
            "email" => $email,
            "organization_id" => $organization->id,
            "otp" => $otp,
            "used_for" => 'register',
            "expired_at" => now()->addSeconds(420)
        ];

        $otpModel = MobileOtp::create($data);

        if ($email) {
            $otpModel->sendOTP($otp, $organization);
        } else {
            $message =   "Your ". $organization->name ." OTP Code is: " . $otp ;
            $this->sendSMS($phone, $message);
        }


        $otpModel->message = 'OTP has been sent, verify now';
        return $otpModel;
    }
    /**
     * Store a newly created resource in storage.
     */
    public function storeOTPForgotPassword(Request $request, $organization)
    {

        $phone = null;
        $email = null;
        $email_pattern = '/^[_a-z0-9-]+(\.[_a-z0-9-]+)*@[a-z0-9-]+(\.[a-z0-9-]+)*(\.[a-z]{2,4})$/';
        if (preg_match($email_pattern, $request->phone_or_email)) {
            $email = $request->phone_or_email;
            $otpModel = MobileOtp::where('email', $request->phone_or_email)
            ->where('organization_id', $organization->id)->where('expired_at', '>', now())->first();

        } else {
            $phone = $request->phone_or_email;
            $otpModel = MobileOtp::where('mobile_no', $request->phone_or_email)
            ->where('organization_id', $organization->id)->where('expired_at', '>', now())->first();
        }


        if ($otpModel) {
            $otpModel->message = "We have already sent you an OTP.";
            return $otpModel;
        }
        $otp = $this->generateOTP();
        $data = [
            "mobile_no" => $phone,
            "email" => $email,
            "organization_id" => $organization->id,
            "otp" => $otp,
            "used_for" => 'forgot_password',
            "expired_at" => now()->addMinutes(2)
        ];

        $otpModel = MobileOtp::create($data);
        if ($email) {
            $otpModel->sendOTP($otp, $organization);
        } else {
            $message =   "Your ". $organization->name ." OTP Code is: " . $otp ;
            $this->sendSMS($phone, $message);
        }


        $otpModel->message = 'OTP has been sent, verify now';
        return $otpModel;
    }

    /**
     * Verify the OTP
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function verifyOTP(Request $request)
    {
        $this->validate($request, [
            'otp_id' => 'required',
            'otp' => 'required',
        ]);

        try {
            $otp = MobileOtp::findOrFail($request->otp_id);
            if ($otp->is_used == 1) {
                return $this->apiResponse([
                    'is_otp_verified' => false
                ], 'OTP already used', false, 422);
            }

            if ($otp->expired_at < now()) {
                return $this->apiResponse([
                    'is_otp_verified' => false
                ], 'OTP expired', false, 422);
            }

            if ($request->otp == $otp->otp) {
                $otp->is_used = 1;
                $otp->save();
                return $this->apiResponse(
                    [
                        'otp_id' => $otp->id,
                        'used_for' => $otp->used_for,
                    ],
                'OTP verified successfully', true, 200);
            } else {
                return $this->apiResponse([
                    'is_otp_verified' => false
                ], 'OTP verification failed', false, 422);
            }
        } catch (ModelNotFoundException $e) {
            return $this->apiResponse([
                'is_otp_verified' => false
            ], 'OTP not found', false, 422);
        }
    }
    /**
     * Verify the OTP
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function verifyOTPForgotPassword(Request $request)
    {
        $this->validate($request, [
            'otp_id' => 'required',
            'otp' => 'required',
        ]);

        try {
            $otp = MobileOtp::findOrFail($request->otp_id);
            if ($otp->is_used == 1) {
                return $this->apiResponse([
                    'is_otp_verified' => false
                ], 'OTP already used', false, 422);
            }
            if ($request->otp == $otp->otp) {
                $otp->is_used = 1;
                $otp->save();
                $user = User::where('contact_no', $otp->mobile_no)->where('organization_id', $otp->organization_id)->first();
                return $this->apiResponse([
                    'otp_id' => $otp->id,
                    'user_id' => $user ? $user->id : ''
                ], 'OTP verified successfully', true, 200);
            } else {
                return $this->apiResponse([
                    'is_otp_verified' => false
                ], 'OTP verification failed', false, 422);
            }
        } catch (ModelNotFoundException $e) {
            return $this->apiResponse([
                'is_otp_verified' => false
            ], 'OTP not found', false, 422);
        }
    }



    /**
     * Verify the OTP
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function checkOTP(Request $request)
    {
        $this->validate($request, [
            'otp_id' => 'required',
        ]);

        // try {
            $otp = MobileOtp::find($request->otp_id);
            if (!$otp) {
                return [
                    'organization_id' => '',
                    'mobile_no' => '',
                    'email' => ''
                ];
            }
            if ($otp->is_used == 1) {
                return [
                    'organization_id' => $otp->organization_id,
                    'mobile_no' => $otp->mobile_no,
                    'email' => $otp->email
                ];
            } else {
                return [
                    'organization_id' => '',
                    'mobile_no' => '',
                    'email' => ''
                ];
            }
        // } catch (ModelNotFoundException $e) {
        //     return [
        //         'organization_id' => '',
        //         'mobile_no' => '',
        //         'email' => ''
        //     ];
        // }
    }



}
