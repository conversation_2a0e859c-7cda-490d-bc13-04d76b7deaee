<?php

namespace App\Http\Controllers\Auth;

use App\Http\Traits\HelperTrait;
use App\Models\User;
use Illuminate\Auth\Events\Verified;
use Illuminate\Routing\Controller;

class VerifyEmailController extends Controller
{
    use HelperTrait;

    public function verify($id, $hash)
    {
        try {
            $user = User::findOrFail($id);

            if (! hash_equals((string) $hash, sha1($user->getEmailForVerification()))) {
                return $this->errorResponse('Invalid signature', 'Invalid signature', 401);
            }

            if (! $user->hasVerifiedEmail()) {
                if ($user->markEmailAsVerified()) {
                    event(new Verified($user));
                }

                return redirect('https://edupackbd.com/login');

                // return redirect()->route('verification.notice');

            } else {
                return redirect()->route('already.verified');
            }
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Invalid signature', 500);
        }
    }

    public function resend()
    {
        if (! request()->user()->hasVerifiedEmail()) {
            request()->user()->sendEmailVerificationNotification();

            return $this->successResponse([], 'Verification email sent.');
        } else {
            return $this->successResponse([], 'Already verified.');
        }
    }
}
