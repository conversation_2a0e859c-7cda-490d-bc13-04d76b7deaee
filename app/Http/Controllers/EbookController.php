<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreEbookRequest;
use App\Http\Requests\UpdateEbookRequest;
use App\Http\Resources\EbookResource;
use App\Models\Ebook;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class EbookController extends Controller
{
    public function index()
    {
        $ebooks = Ebook::query()
            ->when(request('organization_id'), function ($query) {
                $query->where('organization_id', request('organization_id'));
            })
            ->when(request('is_active'), function ($query) {
                $query->where('is_active', request('is_active'));
            })
            ->latest()
            ->paginate();

        return $this->apiResponse(EbookResource::collection($ebooks), 'Dashboard retrieved successfully', true, 200);
    }

    public function store(StoreEbookRequest $request)
    {
        $data = $request->validatedData();

        // Handle file uploads
        if ($request->hasFile('image')) {
            $data['image'] = $this->imageUpload($request, 'image', 'image');
        }

        if ($request->hasFile('pdf')) {
            $data['pdf'] = $this->imageUpload($request, 'pdf', 'pdf');
        }

        $data['created_by'] = auth()->id();

        $ebook = Ebook::create($data);

        return $this->apiResponse(new EbookResource($ebook), 'Ebook created successfully', true, 201);
    }

    public function show(Ebook $ebook)
    {
        return $this->apiResponse(new EbookResource($ebook), 'Ebook retrieved successfully', true, 200);
    }

    public function update(UpdateEbookRequest $request, Ebook $ebook)
    {
        $data = $request->validated();

        // Handle image update
        if ($request->hasFile('image')) {
            $data['image'] = $this->imageUpload($request, 'image', 'image', $ebook->image);
        }

        // Handle pdf update
        if ($request->hasFile('pdf')) {
            $data['pdf'] = $this->imageUpload($request, 'pdf', 'pdf', $ebook->pdf);
        }

        $ebook->update($data);

        return $this->apiResponse(new EbookResource($ebook), 'Ebook updated successfully', true, 200);
    }

    public function destroy(Ebook $ebook)
    {
        // Delete associated files
        if ($ebook->image) {
            Storage::disk('public')->delete($ebook->image);
        }
        if ($ebook->pdf) {
            Storage::disk('public')->delete($ebook->pdf);
        }

        $ebook->delete();

        return $this->apiResponse([], 'Ebook deleted successfully', true, 200);
    }
}




