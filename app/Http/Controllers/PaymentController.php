<?php

namespace App\Http\Controllers;

use App\Models\Course;
use App\Models\StudentInformation;
use App\Models\CourseParticipant;
use App\Models\CourseStudentMapping;
use App\Models\Package;
use App\Models\PackageType;
use App\Models\Payment;
use App\Models\Batch;
use App\Models\Coupon;
use App\Models\PaymentDetail;
use App\Models\TopicConsume;
use App\Models\User;
use App\Models\BatchStudent;
use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Http\Traits\HelperTrait;
use Illuminate\Support\Facades\DB;
use App\Http\Requests\OrganizationPaymentRequest;
use App\Http\Requests\PackageStoreRequest;
use App\Http\Requests\PackageUpdateRequest;
use App\Http\Resources\PendingPaymentListResource;
use Illuminate\Support\Facades\Auth;
use Exception;
use App\Services\PaymentService;

class PaymentController extends Controller
{
    use HelperTrait;
    public function __construct(
      protected PaymentService $paymentService
    ) {
    }
    public function purchaseCourse(Request $request)
    {

        DB::beginTransaction();
        try {
        $user_id = $request->user()->id;
        $student = StudentInformation::where('user_id', $user_id)->first();
        $course_id = $request->course_id ? $request->course_id : 0;

        if (! $course_id) {
            return response()->json([
                'status' => false,
                'message' => 'Please, select a course',
                'data' => [],
            ], 422);
        }

        $course = Course::where('id', $course_id)->orderBy('sequence', 'ASC')->first();


        $payableAmount = $course->regular_price;

        if ($course->sale_price > 0) {
            $payableAmount = $course->sale_price;
            if ($course->discount_percentage > 0) {
                $payableAmount = $payableAmount - ($payableAmount * $course->discount_percentage) / 100;
            }
        }

        $transaction_id = uniqid();
        $payment = Payment::create([
            'user_id' => $user_id,
            'organization_id' => $request->user()->organization_id,
            'item_id' => $course_id,
            'is_promo_applied' => false,
            'item_type' => 'Course',
            'payable_amount' => $course->is_free ? 0.00 : $payableAmount,
            'paid_amount' => $course->is_free ? 0.00 : $payableAmount,
            'discount_amount' => 0.00,
            'currency' => 'BDT',
            'transaction_id' => $transaction_id,
            'payment_type' => 'Web',
            'payment_method' => $request->payment_method ?? 'Cash',
            'status' => 'Completed',
        ]);

        $paymentDetail = PaymentDetail::create([
            'payment_id' => $payment->id,
            'user_id' => $user_id,
            'organization_id' => $course->organization_id,
            'item_id' => $course->id,
            'paid_amount' => $course->minimum_enroll_amount,
            'payable_amount' => $course->minimum_enroll_amount ?? $course->sale_price,
            'payment_method' => $request->payment_method,
            'trx_id' => $request->trx_id,
            'deadline' => Carbon::now(),
            'pay_for' => 'Enrollment',
            'is_approved' => false,
        ]);

        // CourseParticipant::create([
        //     'item_id' => $course->id,
        //     'user_id' => $user_id,
        //     'item_price' => $course->is_free ? 0.00 : $payableAmount,
        //     'paid_amount' => $course->is_free ? 0.00 : $payableAmount,
        //     'payment_id' => $payment->id,
        //     'discount' => 0,
        //     'item_type' => 'Course',
        //     'is_trial_taken' => 0,
        //     'trial_expiry_date' => null,
        // ]);
        $course->number_of_enrolled = $course->number_of_enrolled + 1;
        $course->save();

        DB::commit();
        return $this->apiResponse([], 'Enrollment Successful!', true, 200);

        } catch (\Throwable $th) {
            DB::rollBack();

            return $this->errorResponse($th->getMessage(), 'Something went wrong', 500);
        }
    }


    public function enrollCourse (Request $request) {
        $user = $request->user();
        $course = Course::where('id', $request->course_id)->first();
        $is_enrolled = Payment::where('user_id', $user->id)->where('item_id', $course->id)->first();

        if ($is_enrolled) {
            return $this->apiResponse([], 'You are already enrolled!', false, 200);
        }

        DB::beginTransaction();
        try {
        $student = StudentInformation::where('user_id', $user->id)->first();
        $user_id = $user->id;
        $course_id = $request->course_id ? $request->course_id : 0;

        if (! $course_id) {
            return response()->json([
                'status' => false,
                'message' => 'Please, select a course',
                'data' => [],
            ], 422);
        }

        $course = Course::where('id', $course_id)->orderBy('sequence', 'ASC')->first();

        $specialDiscount = 0;
        $couponCode = $request->coupon ?? null;
        $coupon = null;
        if ($request->coupon ) {
            $coupon = Coupon::where('code', $couponCode)->first();

            if ($coupon) {
                if ($coupon->isExpired()) {
                    return $this->apiResponse([], 'Coupon expired!', false, 422);
                }
                if ($coupon->is_active == 0) {
                    return $this->apiResponse([], 'Coupon is not active!', false, 422);
                }

                if ($coupon->discount_type == 'percentage') {
                    $specialDiscount = ($course->sale_price * $coupon->discount) / 100;
                } else {
                    $specialDiscount = $coupon->discount;
                }
            } else {
                return $this->apiResponse([], 'Invalid coupon code!', false, 422);
            }

        }



        $payableAmount = $course->minimum_enroll_amount ?? $course->sale_price ?? $course->regular_price;

        if ($payableAmount > 0) {
            $payableAmount = $payableAmount - $specialDiscount;

        }


        $payment = Payment::create([
            'user_id' => $user_id,
            'coupon_id' => $coupon?->id,
            'organization_id' => $course->organization_id,
            'item_id' => $course_id,
            'is_promo_applied' => false,
            'item_type' => 'Course',
            'payable_amount' => $course->is_free ? 0.00 : $payableAmount,
            'paid_amount' => $payableAmount,
            'discount_amount' => $request->discount ?? 0,
            'currency' => $course->currency ?? 'BDT',
            'transaction_id' => $request->trx_id,
            'payment_type' => 'Web',
            'payment_method' => $request->payment_method ?? 'Cash',
            'status' => 'Completed',
        ]);

        $paymentDetail = PaymentDetail::create([
            'payment_id' => $payment->id,
            'user_id' => $user_id,
            'organization_id' => $course->organization_id,
            'item_id' => $course->id,
            'paid_amount' => $payableAmount,
            'payable_amount' => $payableAmount,
            'payment_method' => $request->payment_method,
            'trx_id' => $request->trx_id,
            'deadline' => Carbon::now(),
            'pay_for' => 'Enrollment',
            'is_approved' => false,
        ]);

        if ($request->hasFile('image')) {

            $paymentDetail->image = $this->imageUpload($request, 'image', 'payment_screen_shot');

            $paymentDetail->save();

        }


        DB::commit();
        return $this->apiResponse([], 'Successfully Enrolled!', true, 200);

        } catch (\Throwable $th) {
            DB::rollBack();

            return $this->errorResponse($th->getMessage(), 'Something went wrong', 500);
        }
    }

    public function enrollStudentFromAdmin (Request $request)
    {

        DB::beginTransaction();
        try {
        $student = StudentInformation::where('id', $request->student_id)->first();
        $user_id = $student->user_id;
        $course_id = $request->course_id ? $request->course_id : 0;

        if (! $course_id) {
            return response()->json([
                'status' => false,
                'message' => 'Please, select a course',
                'data' => [],
            ], 422);
        }

        $course = Course::where('id', $course_id)->orderBy('sequence', 'ASC')->first();

        $specialDiscount = $request->discount ?? 0;

        $payableAmount = $course->regular_price;


        if ($course->sale_price > 0) {
            $payableAmount = $course->sale_price;
            if ($course->discount_percentage > 0) {
                $payableAmount = $payableAmount - $specialDiscount;
            }
        }

        $transaction_id = uniqid();

        $payment = Payment::create([
            'user_id' => $user_id,
            'organization_id' => $course->organization_id,
            'item_id' => $course_id,
            'is_promo_applied' => false,
            'item_type' => 'Course',
            'payable_amount' => $course->is_free ? 0.00 : $payableAmount,
            'paid_amount' => $request->paid_amount ?? 0.00,
            'discount_amount' => $request->discount ?? 0,
            'currency' => $course->currency,
            'transaction_id' => $transaction_id,
            'payment_type' => 'Web',
            'payment_method' => $request->payment_method ?? 'Cash',
            'status' => 'Completed',
            'is_verified_payment' => 1,
            'is_approved' => 1,
            'approved_by' => Auth::user()->id,
        ]);


        $paymentDetail = PaymentDetail::create([
            'payment_id' => $payment->id,
            'user_id' => $user_id,
            'organization_id' => $course->organization_id,
            'item_id' => $course->id,
            'paid_amount' => $request->paid_amount,
            'payable_amount' => $course->minimum_enroll_amount ?? $course->sale_price,
            'payment_method' => $request->payment_method ?? 'Cash',
            'deadline' => Carbon::now(),
            'pay_for' => 'Enrollment',
            'is_approved' => 1,
            'approved_by' => Auth::user()->id,
        ]);


        CourseParticipant::create([
            'item_id' => $course->id,
            'user_id' => $user_id,
            'item_price' => $course->is_free ? 0.00 : $payableAmount,
            'paid_amount' => $course->is_free ? 0.00 : $payableAmount,
            'payment_id' => $payment->id,
            'discount' => 0,
            'item_type' => 'Course',
            'is_trial_taken' => 0,
            'trial_expiry_date' => null,
        ]);

        if ($request->batch_id) {
            BatchStudent::create([
                'batch_id' => $request->batch_id,
                'student_id' => $student->id,
            ]);
        }

        $course->number_of_enrolled = $course->number_of_enrolled + 1;
        $course->save();


        DB::commit();
        return $this->apiResponse($payment, 'Successfully Enrolled!', true, 200);

        } catch (\Throwable $th) {
            DB::rollBack();

            return $this->errorResponse($th->getMessage(), 'Something went wrong', 500);
        }
    }


    public function checkEnrollmentStudent (Request $request)  {

        $student = StudentInformation::where('id', $request->student_id)->first();
        $user_id = $student->user_id;
        $course_id = $request->course_id ? $request->course_id : 0;

        if (! $course_id) {
            return $this->apiResponse([], 'Please, select a course', false, 422);
        }

        $payment = PaymentDetail::where('user_id', $user_id)->where('item_id', $course_id)->first();

        if ($payment) {
            return $this->apiResponse($payment, 'You are already enrolled!', false, 200);
        } else {
            return $this->apiResponse($payment, 'You are not enrolled!', true, 200);
        }
    }



    public function makePaymentMobile(Request $request)
    {
        $user_id = $request->user()->id;
        $transaction_id = $request->transaction_id;

        //Check TRX ID
        if ($transaction_id) {
            $is_payment_exist = Payment::where('transaction_id', $transaction_id)->where('status', 'Completed')->first();
            if ($is_payment_exist) {

                return $this->apiResponse( [], 'Payment information already exist!', false, 200);

            }
        } else {

            return $this->apiResponse( [], 'Please, enter valid information!', false, 200);

        }

        //Check Items
        if (count($request->items) <= 0) {

            return $this->apiResponse( [], 'Please, add items.', false, 200);

        }

        $user = User::where('id', $user_id)->first();
        $package = Package::where('id', $request->package_id)->first();

        //Check is package exist or not
        if (empty($package)) {
            return response()->json([
                'status' => false,
                'message' => 'Package not found!',
                'data' => [],
            ], 200);
        }

        $expiry_date = Carbon::now()->addDay($package->cycle);

        $payment = Payment::create([
            'user_id' => $user_id,
            'school_id' => $user->school_id,
            'package_id' => $request->package_id,
            'is_promo_applied' => $request->is_promo_applied,
            'promo_id' => $request->promo_id,
            'payable_amount' => $request->payable_amount,
            'paid_amount' => $request->paid_amount,
            'discount_amount' => $request->discount_amount,
            'currency' => $request->currency,
            'transaction_id' => $request->transaction_id,
            'payment_type' => 'Mobile',
            'payment_method' => $request->payment_method,
            'status' => 'Completed',
        ]);

        foreach ($request->items as $item) {

            $package_type = PackageType::where('id', $item['package_type_id'])->first();

            if ($item['quantity']) {
                PaymentDetail::create([
                    'user_id' => $user_id,
                    'school_id' => $user->school_id,
                    'package_id' => $request->package_id,
                    'package_type_id' => $item['package_type_id'],
                    'payment_id' => $payment->id,
                    'unit_price' => $package_type->price,
                    'quantity' => $item['quantity'],
                    'total' => $item['quantity'] * $package_type->price,
                ]);

                TopicConsume::create([
                    'user_id' => $user_id,
                    'school_id' => $user->school_id,
                    'package_id' => $request->package_id,
                    'package_type_id' => $item['package_type_id'],
                    'payment_id' => $payment->id,
                    'balance' => $item['quantity'],
                    'consumme' => 0,
                    'expiry_date' => $expiry_date,
                ]);
            }
        }

        return response()->json([
            'status' => true,
            'message' => 'Payment Successful',
            'data' => [],
        ], 200);
    }

    public function generateTransactionID()
    {
        return 'AEWID#'.date('y').date('d').mt_rand(1000, 9999);
    }

    public function makePaymentWeb(Request $request)
    {
        $user_id = $request->user()->id;
        $transaction_id = uniqid();

        //Check TRX ID
        if ($transaction_id) {
            $is_payment_exist = Payment::where('transaction_id', $transaction_id)->where('status', 'Completed')->first();
            if ($is_payment_exist) {
                return response()->json([
                    'status' => false,
                    'message' => 'Payment information already exist!',
                    'data' => [],
                ], 409);
            }
        } else {
            $transaction_id = uniqid();
        }

        //Check Items
        if (count($request->items) <= 0) {
            return response()->json([
                'status' => false,
                'message' => 'Please, add items.',
                'data' => [],
            ], 200);
        }

        $user = User::where('id', $user_id)->first();
        $package = Package::where('id', $request->package_id)->first();

        //Check is package exist or not
        if (empty($package)) {
            return response()->json([
                'status' => false,
                'message' => 'Package not found!',
                'data' => [],
            ], 200);
        }

        $expiry_date = Carbon::now()->addDay($package->cycle);

        $payment = Payment::create([
            'user_id' => $user_id,
            'school_id' => $user->school_id,
            'package_id' => $request->package_id,
            'is_promo_applied' => $request->is_promo_applied,
            'promo_id' => $request->promo_id,
            'payable_amount' => $request->payable_amount,
            'paid_amount' => $request->paid_amount,
            'discount_amount' => $request->discount_amount,
            'currency' => $request->currency,
            'transaction_id' => $transaction_id,
            'payment_type' => 'Web',
            'payment_method' => $request->payment_method,
            'status' => 'Completed',
        ]);

        foreach ($request->items as $item) {

            $package_type = PackageType::where('id', $item['package_type_id'])->first();

            if ($item['quantity']) {
                PaymentDetail::create([
                    'user_id' => $user_id,
                    'school_id' => $user->school_id,
                    'package_id' => $request->package_id,
                    'package_type_id' => $item['package_type_id'],
                    'payment_id' => $payment->id,
                    'unit_price' => $package_type->price,
                    'quantity' => $item['quantity'],
                    'total' => $item['quantity'] * $package_type->price,
                ]);

                TopicConsume::create([
                    'user_id' => $user_id,
                    'school_id' => $user->school_id,
                    'package_id' => $request->package_id,
                    'package_type_id' => $item['package_type_id'],
                    'payment_id' => $payment->id,
                    'balance' => $item['quantity'],
                    'consumme' => 0,
                    'expiry_date' => $expiry_date,
                ]);
            }
        }

        return response()->json([
            'status' => true,
            'message' => 'Payment has been completed successfully!',
            'data' => [],
        ], 200);
    }

    public function myPaymentList(Request $request)
    {
        $user_id = $request->user()->id;
        $payment = Payment::select(
            'payments.*',
            'packages.title as packages_title',
            'packages.feature_image',
            'packages.description as packages_description',
        )
            ->leftJoin('packages', 'packages.id', 'payments.package_id')
            ->where('user_id', $user_id)
            ->get();

        foreach ($payment as $item) {
            $details = TopicConsume::where('payment_id', $item->id)->first();
            $item->expiry_date = $details['expiry_date'];
        }

        return response()->json([
            'status' => true,
            'message' => 'Payment list successful',
            'data' => $payment,
        ], 200);
    }

    public function packageDetailsByPaymentID(Request $request)
    {
        $user_id = $request->user()->id;
        $payment_id = $request->payment_id ? $request->payment_id : 0;

        $details = Payment::select(
            'payments.id as payment_id',
            'payments.package_id',
            'packages.title as packages_title',
            'packages.feature_image',
            'packages.description as packages_description',
            'payments.created_at as purchased_date',
        )
            ->where('payments.id', $payment_id)
            ->leftJoin('packages', 'packages.id', 'payments.package_id')
            ->first();

        $list = TopicConsume::select('topic_consumes.balance', 'topic_consumes.consumme', 'topic_consumes.expiry_date', 'package_types.name as syllabus', 'topic_consumes.package_type_id')
            ->leftJoin('package_types', 'package_types.id', 'topic_consumes.package_type_id')
            ->where('topic_consumes.user_id', $user_id)
            ->where('topic_consumes.payment_id', $payment_id)
            ->orderBy('package_types.name', 'ASC')
            ->get();

        $details->balance = $list->sum('balance');
        $details->consumme = $list->sum('consumme');
        $details->expiry_date = $list->pluck('expiry_date')->first();
        $details->details = $list->map->only(['balance', 'consumme', 'syllabus', 'package_type_id']);

        return response()->json([
            'status' => true,
            'message' => 'Details Successful',
            'data' => $details,
        ], 200);
    }

    public function adminPaymentList(Request $request)
    {
        $payment = Payment::select(
            'payments.*',
            'users.name',
            'users.email',
            'packages.title as packages_title',
            'packages.feature_image',
            'packages.description as packages_description',
        )
            ->leftJoin('users', 'users.id', 'payments.user_id')
            ->leftJoin('packages', 'packages.id', 'payments.package_id')
            ->where('payments.status', 'Completed')
            ->orderBy('payments.id', 'DESC')
            ->get();

        return response()->json([
            'status' => true,
            'message' => 'Payment list successful',
            'data' => $payment,
        ], 200);
    }


    public function enrollomentListAdmin(Request $request)
    {
        try {
            $courseId = $request->course_id;

            $itemsPerPage = $request->input('itemsPerPage', 10);
            $user = $request->user();

            $query = Payment::query()->select(
                'payments.*',
                'courses.title as course_title',
                'courses.thumbnail',
                'users.name',
                'users.image',
            )
                ->leftJoin('courses', 'courses.id', 'payments.item_id')
                ->leftJoin('users', 'users.id', 'payments.user_id')
                ->where('payments.organization_id', $user->organization_id)
                ->where('payments.status', 'Completed')
                ->where('payments.item_type', 'Course')
                ->when($request->course_id, function ($query) use ($request) {
                    return $query->where('payments.item_id', $request->course_id);
                })
                ->when($request->search, function ($query) use ($request) {
                    return $query->where('users.name', 'LIKE', '%' . $request->search . '%');
                });
                $query->orderBy('payments.id', 'DESC');
            $enrollmentList = $request->pagination != "false" ? $query->paginate($itemsPerPage) : $query->get();

            return $this->successResponse($enrollmentList, 'Enrollment list successful!');

        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Something went wrong!', 500);
        }
    }

    public function getPendingPayments(Request $request) {

        $organization_id = $request->user()->organization_id ?? $request->organization_id;

        $pendings = PaymentDetail::where('is_approved', 0)
            ->when($organization_id, function ($query) use ($organization_id) {
                return $query->where('organization_id', $organization_id);
            })
            ->when($request->has('start_date'), function ($query) use ($request) {
                return $query->whereDate('created_at', '>=', $request->start_date);
            })
            ->when($request->has('end_date'), function ($query) use ($request) {
                return $query->whereDate('created_at', '<=', $request->end_date);
            })
            ->get();

        return $this->apiResponse(PendingPaymentListResource::collection($pendings), 'Pending payments list successful', true, 200);
    }


    public function approvePendingEnrollment(Request $request) {
        try {
            $ids = $request->ids;
            $approved_by = $request->user()->id;

            foreach ($ids as $id) {
                $paymentDetail = PaymentDetail::where('id', $id)->first();
                $paymentDetail->is_approved = 1;
                $paymentDetail->approved_by = $approved_by;
                $paymentDetail->save();

                $payment = Payment::where('id', $paymentDetail->payment_id)->first();
                if ($payment->is_approved == 1) {
                    $payment->paid_amount = $payment->paid_amount + $paymentDetail->paid_amount;
                } else {
                    $payment->is_approved = 1;
                    $payment->approved_by = $approved_by;
                }
                $payment->is_verified_payment = 1;
                $payment->save();

                CourseParticipant::create([
                    'item_id' => $payment->item_id,
                    'user_id' => $payment->user_id,
                    'item_price' => $payment->payable_amount,
                    'paid_amount' => $payment->paid_amount,
                    'payment_id' => $payment->id,
                    'discount' => $payment->discount_amount,
                    'item_type' => 'Course',
                    'is_trial_taken' => 0,
                    'trial_expiry_date' => null,
                ]);

                $course = Course::where('id', $paymentDetail->item_id)->first();

                $course->number_of_enrolled = $course->number_of_enrolled + 1;
                $course->save();

            }

            return $this->apiResponse([], count($ids).' Pending payments approved successfully', true, 200);

        } catch (Exception $e) {
            return $this->apiResponse(null, $e->getMessage(), false, 500);

        }
    }


    public function startNewMonth (Request $request) {
        try {
            $user = $request->user();
            $organization_id = $user->organization_id ?? $request->organization_id;

            $courses = Course::where('organization_id', $organization_id)->where('is_active', 1)->where('installment_type', 'Monthly')->get();
            // $batches = Batch::where('organization_id', $organization_id)->where('is_active', 1)->get();

            foreach ($courses as $course) {
                $batches = $course->batches()->where('is_active', 1)->get();
                foreach ($batches as $batch) {
                    $students = $batch->studentUsers;
                    foreach ($students as $student) {
                        $payment = Payment::where('user_id', $student->user_id)->where('item_id', $course->id)->where('is_approved', 1)->first();
                        if ($payment) {
                            $payment->payable_amount = $payment->payable_amount + $course->monthly_amount;
                            $payment->save();
                            $dueAmount =  $course->monthly_amount;

                            $lastPaymentDetail = PaymentDetail::where('payment_id', $payment->id)->where('due_amount', '>', 0)->orderBy('created_at', 'DESC')->first();
                            if ($lastPaymentDetail) {
                                $dueAmount = $course->monthly_amount + $lastPaymentDetail->due_amount;
                            }
                            $paymentDetail = PaymentDetail::create([
                                'payment_id' => $payment->id,
                                'user_id' => $student->user_id,
                                'organization_id' => $organization_id,
                                'item_id' => $course->id,
                                'deadline' => Carbon::now()->endOfMonth(),
                                'pay_for' => 'Monthly',
                                'paid_amount' => 0,
                                'payable_amount' => $course->monthly_amount,
                                'due_amount' => $dueAmount,
                                'created_by' => $user->id,
                            ]);

                        }
                    }
                }
            }

            return $this->apiResponse(null, 'New Month Started successful', true, 200);
        } catch (Exception $e) {
            return $this->apiResponse(null, $e->getMessage(), 500);
        }
    }


    public function collectPayment (Request $request) {
        $user = $request->user();
        $payment = Payment::where('id', $request->payment_id)->first();
        $payment->paid_amount = $payment->paid_amount + $request->amount;
        $payment->save();

        $paymentDetail = PaymentDetail::where('payment_id', $request->payment_id)->orderBy('created_at', 'DESC')->first();

        $paymentDetail->paid_amount = $paymentDetail->paid_amount + $request->amount;
        $paymentDetail->due_amount = $paymentDetail->due_amount - $request->amount;
        $paymentDetail->save();

        return $this->apiResponse(null, 'Payment collected successfully', true, 200);

    }

    public function studentPayments (Request $request) {
        try {
            $student = StudentInformation::where('id', $request->id)->with('payments')->first();
            return $this->apiResponse($student, 'Student Payment History', true, 200);
        }
        catch (Exception $e) {
            return $this->errorResponse($e->getMessage(), 'Something went wrong', 500);
        }
    }
    public function studentMyPayments (Request $request) {
        $user = $request->user();
        try {
            $student = StudentInformation::where('user_id', $user->id)->with(['payments', 'payments.course'])->first();
            return $this->apiResponse($student, 'Student Payment History', true, 200);
        }
        catch (Exception $e) {
            return $this->apiResponse(null, $e->getMessage(), false, 500);
        }
    }

    public function payDueByStudent (Request $request) {
        try {
            $payment = Payment::where('id', $request->payment_id)->first();
            $payment->is_verified_payment = 0;
            // $payment->paid_amount = $payment->paid_amount + $request->amount;

            if ($request->amount != ($payment->payable_amount - $payment->paid_amount)) {
                return $this->apiResponse(null, 'Please, pay the exact amount', false, 422);
            }

            $paymentDetail = new PaymentDetail();
            if ($request->hasFile('image')) {
                $paymentDetail->image = $this->imageUpload($request, 'image', 'payment_screen_shot');
            }


            $paymentDetail->payment_id = $payment->id;
            $paymentDetail->user_id = Auth::user()->id;
            $paymentDetail->trx_id = $request->trx_id;
            $paymentDetail->item_id = $payment->item_id;
            $paymentDetail->payment_method = $request->payment_method;
            $paymentDetail->paid_amount = $request->amount;
            $paymentDetail->due_amount = ($payment->payable_amount - $payment->paid_amount) - $request->amount;
            $paymentDetail->is_approved = 0;
            $paymentDetail->save();

            return $this->apiResponse(null, 'Payment submission successfully', true, 200);

        } catch (Exception $e) {
            return $this->apiResponse(null, $e->getMessage(), false, 500);
        }
    }

    // Repository Pattern Start

    public function createPackage (PackageStoreRequest $request) {
        try {
            $data = $this->paymentService->create($request->validatedData());
            return $this->apiResponse($data, 'Package list successful', true, 201);
        } catch (Exception $e) {
            return $this->errorResponse($e->getMessage(), 'Something went wrong', 500);
        }
    }
    public function updatePackage (PackageUpdateRequest $request, $id) {

        try {
            $data = $this->paymentService->update($request->validatedData(), $id);
            return $this->apiResponse($data, 'Update successful', true, 201);
        } catch (Exception $e) {
            return $this->errorResponse($e->getMessage(), 'Something went wrong', 500);
        }
    }

    public function deletePackage (Request $request, $id) {
        try {
            $data = $this->paymentService->delete( $id);
            return $this->apiResponse($data, 'Delete successful', true, 201);
        } catch (Exception $e) {
            return $this->errorResponse($e->getMessage(), 'Something went wrong', 500);
        }
    }
    public function packageDetails (Request $request, $id) {
        try {
            $data = $this->paymentService->find($id);
            return $this->apiResponse($data, 'Package Details successful', true, 200);
        } catch (Exception $e) {
            return $this->errorResponse($e->getMessage(), 'Something went wrong', 500);
        }
    }
    public function packageList (Request $request) {
        try {
            $data = $this->paymentService->all($request);
            return $this->apiResponse($data, 'Package list successful', true, 200);
        } catch (Exception $e) {
            return $this->errorResponse($e->getMessage(), 'Something went wrong', 500);
        }
    }

    public function makePaymentOrganization (OrganizationPaymentRequest $request) {
        try {
            $data = $this->paymentService->makePaymentOrganization($request->validated());
            return $this->apiResponse($data, 'Payment successful', true, 201);
        } catch (Exception $e) {
            return $this->errorResponse($e->getMessage(), 'Something went wrong', 500);
        }
    }





    /**
     * Get all payments with filtering options
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAllPayments(Request $request)
    {
        try {
            $user = $request->user();
            $organization_id = $user->organization_id;

            $query = Payment::with(['student', 'course', 'details'])
                ->where('organization_id', $organization_id);

            // Filter by date range
            if ($request->has('start_date') && $request->start_date) {
                $query->whereDate('created_at', '>=', $request->start_date);
            }

            if ($request->has('end_date') && $request->end_date) {
                $query->whereDate('created_at', '<=', $request->end_date);
            }

            // Filter by student ID
            if ($request->has('student_id') && $request->student_id) {
                $query->where('user_id', $request->student_id);
            }

            // Filter by student name or email
            if ($request->has('search') && $request->search) {
                $search = $request->search;
                $query->whereHas('user', function($q) use ($search) {
                    $q->where('name', 'LIKE', "%{$search}%")
                      ->orWhere('email', 'LIKE', "%{$search}%");
                });
            }

            // Order by latest
            $query->orderBy('created_at', 'desc');

            // Paginate results
            $itemsPerPage = $request->input('itemsPerPage', 10);
            $payments = $request->pagination != "false" ? $query->paginate($itemsPerPage) : $query->get();

            return $this->apiResponse($payments, 'Payment list retrieved successfully', true, 200);
        } catch (Exception $e) {
            return $this->errorResponse($e->getMessage(), 'Something went wrong', 500);
        }
    }

    /**
     * Get detailed information for a specific payment
     *
     * @param Request $request
     * @param int $payment_id
     * @return \Illuminate\Http\JsonResponse
     */
    public function paymentDetails(Request $request, $payment_id)
    {
        try {
            $user = $request->user();
            $organization_id = $user->organization_id;

            $payment = Payment::with([
                'user',
                'student:user_id,current_address,permanent_address',
                'course',
                'details',
                'details.course',
                'createdBy'
            ])
            ->where('organization_id', $organization_id)
            ->where('id', $payment_id)
            ->orWhere('transaction_id', $payment_id)
            ->first();

            if (!$payment) {
                return $this->apiResponse(null, 'Payment not found', false, 404);
            }

            return $this->apiResponse($payment, 'Payment details retrieved successfully', true, 200);
        } catch (Exception $e) {
            return $this->errorResponse($e->getMessage(), 'Something went wrong', 500);
        }
    }
}
