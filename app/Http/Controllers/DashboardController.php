<?php

namespace App\Http\Controllers;

use App\Http\Traits\HelperTrait;
use App\Models\Organization;
use App\Models\TempVideo;
use App\Models\PaymentDetail;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use App\Http\Resources\PendingPaymentListResource;
use App\Models\StudentInformation;
use App\Models\MentorInformation;
use App\Models\User;
use App\Models\Course;

class DashboardController extends Controller
{
    use HelperTrait;

    public function superAdminDashboard (Request $request) {
        $organizations = Organization::withCount('students', 'mentors', 'courses', 'scripts', 'videos', 'quizzes')->limit(5)->get();
        $data = [
            'organizations_count' => Organization::count(),
            'students_count' => StudentInformation::count(),
            'mentors_count' => MentorInformation::count(),
            'users_count' => User::count(),
            'courses_count' => Course::count(),
            'organizations' => $organizations
        ];
        return $this->apiResponse($data, 'Dashboard retrieved successfully', true, 200);
    }

    public function dashboard(Request $request)
    {
        try {
            $orgId = $request->id ?? Auth::user()->organization_id;
            $organization = Organization::withCount('students', 'mentors', 'courses', 'scripts', 'videos', 'quizzes')->where('id', $orgId)->first();


            $organization->active_course_count = $organization->courses()->where('is_active', 1)->count();

            $organization->total_collection = $organization->payments()->sum('paid_amount');

            $last_year_per_months = array_fill(1, 12, 0);

            $collection = $organization->payments()->whereBetween('created_at', [now()->startOfYear(), now()->endOfYear()])
                ->selectRaw('sum(paid_amount) as total, month(created_at) as month')
                ->groupByRaw('month(created_at)')
                ->get()
                ->pluck('total', 'month')
                ->toArray();

            foreach ($collection as $month => $total) {
                $last_year_per_months[$month] = $total;
            }

            $organization->last_year_per_months = $last_year_per_months;


            $pendings = PaymentDetail::where('is_approved', 0)->where('organization_id', $orgId)
                ->when($request->has('start_date'), function ($query) use ($request) {
                    return $query->whereDate('created_at', '>=', $request->start_date);
                })
                ->when($request->has('end_date'), function ($query) use ($request) {
                    return $query->whereDate('created_at', '<=', $request->end_date);
                })
                ->orderBy('id', 'desc')
                ->limit(5)
                ->get();

            $organization->pending_payments = PendingPaymentListResource::collection($pendings);


            return $this->successResponse($organization, 'Dashboard retrieved successfully');

        } catch (Exception $e) {
            return $this->errorResponse([], $e->getMessage(), 500);
        }
    }


    public function uploadVideo(Request $request)
    {
        try {
            $request->validate([
                'video' => 'required|mimes:mp4,mov,avi,wmv|max:1048576', // Max size: 1GB
            ]);

            $videoFile = $request->file('video');
            $videoName = $videoFile->getClientOriginalName();
            $videoSize = $videoFile->getSize();
            $videoHash = hash_file('md5', $videoFile->getPathname()); // Generate a hash of the video file

            // Check if the video already exists in the database
            $existingVideo = TempVideo::where('name', $videoName)
                ->where('size', number_format($videoSize / 1048576, 2))
                ->orWhere('hash', $videoHash) // Ensure to store a `hash` column in the database
                ->first();

            if ($existingVideo) {
                return $this->apiResponse($existingVideo, 'Video already exists', true, 200);
            }

            // If video doesn't exist, proceed with upload
            $data = [
                'name' => $videoName,
                'ip' => $request->ip(),
                'device' => $request->header('User-Agent'),
                'browser' => $request->header('Sec-Ch-Ua'),
                'size' => number_format($videoSize / 1048576, 2),
                'hash' => $videoHash, // Store the hash in the database
            ];

            $videoPath = $this->imageUpload($request, 'video', 'video');
            $baseUrl = $request->getSchemeAndHttpHost();
            $data['raw_url'] = $baseUrl . '/uploads/' . $videoPath;

            $tempVideo = TempVideo::create($data);

            return $this->apiResponse($tempVideo, 'Video uploaded successfully', true, 200);
        } catch (\Throwable $th) {
            return $this->errorResponse([], 'Video upload failed', 500);
        }
    }

    public function deleteTempVideo (Request $request, $id) {
        try {
            $tempVideo = TempVideo::find($id);
            if (!$tempVideo) {
                return $this->errorResponse([], 'Video not found', 404);
            }

            $baseUrl = $request->getSchemeAndHttpHost();

            unlink(public_path(str_replace($baseUrl, '', $tempVideo->raw_url)));


            $tempVideo->delete();
            return $this->apiResponse($tempVideo, 'Video deleted successfully', true, 200);
        } catch (\Throwable $th) {
            return $this->errorResponse([], 'Video delete failed', 500);
        }
    }

}



