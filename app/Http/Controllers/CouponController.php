<?php

namespace App\Http\Controllers;

use App\Models\Coupon;
use App\Http\Requests\CouponRequest;
use App\Http\Requests\CouponUpdateRequest;
use App\Http\Requests\CouponCheckRequest;
use Illuminate\Http\Request;
use App\Services\CouponService;
use Exception;

class CouponController extends Controller
{
    private $couponService;

    public function __construct(CouponService $couponService)
    {
        $this->couponService = $couponService;
    }

    public function index()
    {
        try {
            $result = $this->couponService->getAllCoupons();
            return $this->apiResponse($result, 'Coupons fetched successfully', true, 200);
        } catch (Exception $e) {
            return $this->apiResponse(null, $e->getMessage(), false, 500);
        }
    }

    public function store(CouponRequest $request)
    {
        try {
            $result = $this->couponService->createCoupon($request->validated());
            return $this->apiResponse($result, 'Coupon created successfully', true, 201);
        } catch (Exception $e) {
            return $this->apiResponse(null, $e->getMessage(), false, 500);
        }
    }

    public function show(Coupon $coupon)
    {
        try {
            $result = $this->couponService->getCoupon($coupon);

            return $this->apiResponse($result, 'Coupon details fetched successfully', true, 200);
        } catch (Exception $e) {
            return $this->apiResponse(null, $e->getMessage(), false, 500);
        }
    }

    public function update(CouponUpdateRequest $request, Coupon $coupon)
    {
        try {
            $result = $this->couponService->updateCoupon($coupon, $request->validated());
            return $this->apiResponse($result, 'Coupon updated successfully', true, 200);
        } catch (Exception $e) {
            return $this->apiResponse(null, $e->getMessage(), false, 500);
        }
    }

    public function destroy(Coupon $coupon)
    {
        try {
            $this->couponService->deleteCoupon($coupon);
            return $this->apiResponse(null, 'Coupon deleted successfully', true, 200);
        } catch (Exception $e) {
            return $this->apiResponse(null, $e->getMessage(), false, 500);
        }
    }

    public function couponCheck (CouponCheckRequest $request) {

        try {
            $result = $this->couponService->couponCheck($request->validated());

            if ($result === null) {

                return $this->apiResponse((object)[], 'Coupon not available', false, 200);
            }

            return $this->apiResponse($result, 'Coupon check success', true, 200);
        } catch (Exception $e) {
            return $this->apiResponse(null, $e->getMessage(), false, 500);
        }
    }
}

