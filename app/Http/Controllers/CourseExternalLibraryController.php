<?php

namespace App\Http\Controllers;

use App\Models\CourseExternalLibrary;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class CourseExternalLibraryController extends Controller
{
    // List all entries

public function getList(Request $request)
{
    try {
        $query = CourseExternalLibrary::query();

        // Filter by query parameters
        if ($request->has('course_id')) {
            $query->where('course_id', $request->input('course_id'));
        }
        if ($request->has('title')) {
            $query->where('title', 'like', '%' . $request->input('title') . '%');
        }
        if ($request->has('url')) {
            $query->where('url', 'like', '%' . $request->input('url') . '%');
        }
        if ($request->has('description')) {
            $query->where('description', 'like', '%' . $request->input('description') . '%');
        }

        $libraries = $query->latest()->get();
        return $this->apiResponse($libraries, 'Filtered resources retrieved successfully', true, 200);
    } catch (\Throwable $th) {
        return $this->apiResponse(null, $th->getMessage(), false, 500);
    }
}

    public function index()
    {
        try {
            $libraries = CourseExternalLibrary::latest()->get();
            return $this->apiResponse($libraries, 'Resources retrieved successfully', true, 200);
        } catch (\Throwable $th) {
            return $this->apiResponse(null, $th->getMessage(), false, 500);
        }
    }

    // Show a specific entry
    public function show($id)
    {
        try {
            $library = CourseExternalLibrary::find($id);

            if (!$library) {
                return $this->apiResponse(null, 'Resource not found.', false, 404);
            }

            return $this->apiResponse($library, 'Resource retrieved successfully', true, 200);
        } catch (\Throwable $th) {
            return $this->apiResponse(null, $th->getMessage(), false, 500);
        }
    }

    // Create a new entry
    public function store(Request $request)
    {

        try {
            $validator = Validator::make($request->all(), [
                'course_id'       => 'required|integer',
                'title'           => 'required|string|max:255',
                'url'             => 'required|url|max:500',
                'description'     => 'nullable|string',
            ]);

            if ($validator->fails()) {
                return $this->apiResponse(null, 'Validation errors!', false, 422);
            }
            $data = $validator->validated();
            $data['organization_id'] = auth()->user()->organization_id;
            $data['user_id'] = auth()->id();
            $data['is_approved'] = auth()->user()->user_type == 'Student' ? false : true;

            // return $data;
            $library = CourseExternalLibrary::create($data);

            return $this->apiResponse($library, 'Created successfully.', true, 201);
        } catch (\Throwable $th) {
            return $this->apiResponse(null, $th->getMessage(), false, 500);
        }
    }

    // Update an existing entry
    public function update(Request $request, $id)
    {
        try {
            $library = CourseExternalLibrary::find($id);

            if (!$library) {
                return $this->apiResponse(null, 'Resource not found.', false, 404);
            }

            $validator = Validator::make($request->all(), [
                'title'           => 'sometimes|required|string|max:255',
                'url'             => 'sometimes|required|url|max:500',
                'description'     => 'nullable|string',
                'is_approved'     => 'boolean',
            ]);

            if ($validator->fails()) {
                return $this->apiResponse(null, 'Validation errors!', false, 422);
            }

            $library->update($validator->validated());

            return $this->apiResponse($library, 'Updated successfully.', true, 201);
        } catch (\Throwable $th) {
            return $this->apiResponse(null, $th->getMessage(), false, 500);
        }
    }

    // Soft delete an entry
    public function destroy($id)
    {
        try {
            $library = CourseExternalLibrary::find($id);

            if (!$library) {
                return $this->apiResponse(null, 'Resource not found.', false, 404);
            }

            $library->delete();

            return $this->apiResponse(null, 'Deleted successfully.', true, 200);
        } catch (\Throwable $th) {
            return $this->apiResponse(null, $th->getMessage(), false, 500);
        }
    }

    // Optional: Restore a soft-deleted entry
    public function restore($id)
    {
        try {
            $library = CourseExternalLibrary::onlyTrashed()->find($id);

            if (!$library) {
                return $this->apiResponse(null, 'Resource not found or not deleted.', false, 404);
            }

            $library->restore();

            return $this->apiResponse($library, 'Restored successfully.', true, 200);
        } catch (\Throwable $th) {
            return $this->apiResponse(null, $th->getMessage(), false, 500);
        }
    }

}

