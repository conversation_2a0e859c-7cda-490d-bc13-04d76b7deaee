<?php

namespace App\Http\Controllers;

use App\Http\Requests\ChapterQuizRequest;
use App\Http\Requests\ChapterRequest;
use App\Http\Requests\ChapterVideoRequest;
use App\Http\Requests\ClassLevelRequest;
use App\Http\Requests\FillInBlankQuestionRequest;
use App\Http\Requests\MatchingQuestionRequest;
use App\Http\Requests\QuizQuestionRequest;
use App\Http\Requests\QuizSubjectRequest;
use App\Http\Requests\ScriptRequest;
use App\Http\Requests\SubjectRequest;
use App\Http\Requests\TrueFalseQuestionRequest;
use App\Http\Traits\HelperTrait;
use App\Imports\QuizImport;
use App\Models\Category;
use App\Models\Chapter;
use App\Models\ChapterQuiz;
use App\Models\BlankAnswer;
use App\Models\ChapterQuizItem;
use App\Models\ChapterQuizQuestion;
use App\Models\ChapterQuizSubject;
use App\Models\ChapterQuizWrittenQuestion;
use App\Models\ChapterScript;
use App\Models\ChapterTrueFalseQuestion;
use App\Models\ChapterVideo;
use App\Models\ClassLevel;
use App\Models\Content;
use App\Models\ContentOutline;
use App\Models\CourseCategory;
use App\Models\ContentSubject;
use App\Models\CourseParticipant;
use App\Models\FillInTheBlankQuestion;
use App\Models\MatchingAnswer;
use App\Models\MatchingQuestion;
use App\Models\QuizCoreSubjects;
use App\Models\QuizQuestionSet;
use App\Http\Resources\ChapterQuizResource;
use App\Models\QuizType;
use App\Models\TempVideo;
use App\Models\Subject;
use Illuminate\Http\Request;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;

class ContentController extends Controller
{
    use HelperTrait;

    public function contentsList()
    {
        $contentList = Content::get();

        return $this->apiResponse($contentList, 'Content List Successful', true, 200);
    }

    public function questionSetList()
    {
        $setList = QuizQuestionSet::select('id', 'name')->get();

        return $this->apiResponse($setList, 'Set List Successful', true, 200);
    }

    public function subjectListByClassID(Request $request)
    {
        $class_id = $request->class_id;
        $subjectList = Subject::select('id', 'name', 'name_bn', 'class_level_id')->where('class_level_id', $class_id)->get();

        return $this->apiResponse($subjectList, 'Subject List Successful', true, 200);
    }

    public function chapterListBySubjectID(Request $request)
    {
        $subject_id = $request->subject_id;
        $subjectList = Chapter::select('id', 'name', 'name_bn', 'subject_id')->where('subject_id', $subject_id)->get();

        return $this->apiResponse($subjectList, 'Chapter List Successful', true, 200);
    }

    public function scriptListByChapterID(Request $request)
    {
        $chapter_id = $request->chapter_id;
        $scriptList = ChapterScript::select('id', 'title', 'title_bn', 'chapter_id')->where('chapter_id', $chapter_id)->get();

        return $this->apiResponse($scriptList, 'Script List Successful', true, 200);
    }

    public function scriptDetails(Request $request, $id)
    {
        $script = ChapterScript::where('id', $id)->first();

        return $this->apiResponse($script, 'Script Details Successful', true, 200);
    }

    public function videoListByChapterID(Request $request)
    {
        $chapter_id = $request->chapter_id;
        $videoList = ChapterVideo::select('id', 'title', 'title_bn', 'chapter_id')->where('chapter_id', $chapter_id)->get();

        return $this->apiResponse($videoList, 'Video List Successful', true, 200);
    }

    public function videoDetails(Request $request, $id)
    {
        $video = ChapterVideo::where('id', $id)->first();

        return $this->apiResponse($video, 'Video Details Successful', true, 200);
    }

    public function quizListByChapterID(Request $request)
    {
        $chapter_id = $request->chapter_id;
        $quizList = ChapterQuiz::select('id', 'title', 'title_bn', 'chapter_id')->where('chapter_id', $chapter_id)->get();

        return $this->apiResponse($quizList, 'Quiz List Successful', true, 200);
    }

    public function resourceListByChapterID(Request $request)
    {
        $chapter_id = $request->chapter_id ? $request->chapter_id : 0;

        if (! $chapter_id) {
            return response()->json([
                'status' => false,
                'message' => 'Please, attach ID',
                'data' => [],
            ], 422);
        }
        $scriptList = ChapterScript::select('id', 'title', 'title_bn', 'chapter_id')->where('chapter_id', $chapter_id)->get();
        $videoList = ChapterVideo::select('id', 'title', 'title_bn', 'chapter_id')->where('chapter_id', $chapter_id)->get();
        $quizList = ChapterQuiz::select('id', 'title', 'title_bn', 'chapter_id')->where('chapter_id', $chapter_id)->get();

        $response = [
            'script_list' => $scriptList,
            'video_list' => $videoList,
            'quiz_list' => $quizList,
        ];

        return $this->apiResponse($response, 'Resource List Successful', true, 200);
    }

    public function classList(Request $request)
    {
        $query = ClassLevel::query();
        $query->select(['id', 'name', 'name_bn', 'class_code', 'price', 'is_free', 'icon', 'color_code', 'sequence', 'is_active']);
        $this->applySorting($query, $request);
        $searchKeys = ['name'];
        $this->applySearch($query, $request->input('search'), $searchKeys);
        $pagination = $request->boolean('pagination', true);
        if ($pagination) {
            $itemsPerPage = $request->input('itemsPerPage', 10);
            $currentPage = Paginator::resolveCurrentPage('page');
            $results = $query->paginate($itemsPerPage, ['*'], 'page', $currentPage);

            return $this->apiResponse($results, 'List Successful', true, 200);
        }
        $results = $query->get();

        return $this->apiResponse($results, 'Class List Successful', true, 200);
    }

    public function saveOrUpdateClass(ClassLevelRequest $request)
    {
        try {
            if (empty($request->id)) {

                $classList = new ClassLevel();
                $classList->fill($request->only($classList->getFillable()));
                $classList->class_code = $this->codeGenerator('CC', ClassLevel::class);
                $classList->icon = $this->imageUpload($request, 'icon', 'icon');
                $classList->save();

                return $this->apiResponse([], 'Class Created Successfully', true, 201);
            } else {

                $class = ClassLevel::where('id', $request->id)->first();
                $class->fill($request->only($class->getFillable()));

                if ($request->hasFile('icon')) {
                    $class->icon = $this->imageUpload($request, 'icon', 'icon');
                }

                $class->save();

                return $this->apiResponse([], 'Class Updated Successfully', true, 200);
            }
        } catch (\Throwable $th) {
            return response()->json([
                'status' => false,
                'message' => $th->getMessage(),
                'data' => [],
            ], 200);
        }
    }

    public function subjectList(Request $request)
    {
        $query = Subject::ForOrganization('subjects')
            ->leftJoin('class_levels', 'class_levels.id', '=', 'subjects.class_level_id')
            ->select(
                'subjects.id',
                'subjects.name',
                'subjects.name_bn',
                'subjects.class_level_id',
                'subjects.subject_code',
                'subjects.price',
                'subjects.is_free',
                'subjects.icon',
                'subjects.color_code',
                'subjects.sequence',
                'subjects.is_active',
                'class_levels.name as class_name',
                'class_levels.name_bn as class_name_bn'
            );

        $this->applySorting($query, $request);

        $searchKeys = ['subjects.name', 'subjects.name_bn', 'class_levels.name', 'class_levels.name_bn'];
        $this->applySearch($query, $request->input('search'), $searchKeys);

        $pagination = $request->boolean('pagination', true);

        if ($pagination) {
            $itemsPerPage = $request->input('itemsPerPage', 10);
            $currentPage = Paginator::resolveCurrentPage('page');
            $results = $query->paginate($itemsPerPage, ['*'], 'page', $currentPage);

            return $this->apiResponse($results, 'Subject List Successful', true, 200);
        }

        $results = $query->get();

        return $this->apiResponse($results, 'Subject List Successful', true, 200);
    }

    public function saveOrUpdateSubject(SubjectRequest $request)
    {
        try {
            if (empty($request->id)) {
                $subjectList = new Subject();
                $subjects = $request->only($subjectList->getFillable());
                $subjects['subject_code'] = $this->codeGenerator('SC', Subject::class);
                $subjects['icon'] = $this->imageUpload($request, 'icon', 'icon');
                $subjectList->fill($subjects);
                $subjectList->save();

                return $this->apiResponse([], 'Subject Created Successfully', true, 201);
            } else {
                $subject = Subject::where('id', $request->id)->first();
                $subjects = $request->only($subject->getFillable());
                if ($request->hasFile('icon')) {
                    $subjects['icon'] = $this->imageUpload($request, 'icon', 'icon', $subject->icon);
                }
                $subject->fill($subjects);
                $subject->save();

                return $this->apiResponse([], 'Subject Updated Successfully', true, 200);
            }
        } catch (\Throwable $th) {
            return $this->apiResponse([], $th->getMessage(), false, 500);
        }
    }


    public function deleteSubject (Request $request) {
        try {
            $subject = Subject::where('id', $request->id)->first();

            CourseCategory::where('subject_id', $request->id)->update(['subject_id' => null]);

            $subject->delete();

            return $this->apiResponse([], 'Subject Deleted Successfully', true, 200);
        } catch (\Throwable $th) {
            return $this->apiResponse([], $th->getMessage(), false, 500);
        }
    }

    public function chapterList(Request $request)
    {
        $query = Chapter::ForOrganization('chapters')->leftJoin('subjects', 'subjects.id', '=', 'chapters.subject_id')
            ->leftJoin('class_levels', 'class_levels.id', '=', 'chapters.class_level_id')
            ->select(
                'chapters.id',
                'chapters.name',
                'chapters.name_bn',
                'chapters.subject_id',
                'chapters.class_level_id',
                'chapters.chapter_code',
                'chapters.price',
                'chapters.is_free',
                'chapters.icon',
                'chapters.color_code',
                'chapters.sequence',
                'chapters.is_active',
                'subjects.name as subject_name',
                'subjects.name_bn as subject_name_bn',
                'class_levels.name as class_name',
                'class_levels.name_bn as class_name_bn'
            );
        $this->applySorting($query, $request);

        $searchKeys = ['chapters.name', 'chapters.name_bn', 'subjects.name', 'subjects.name_bn', 'class_levels.name', 'class_levels.name_bn'];
        $this->applySearch($query, $request->input('search'), $searchKeys);

        $pagination = $request->boolean('pagination', true);

        if ($pagination) {
            $itemsPerPage = $request->input('itemsPerPage', 10);
            $currentPage = Paginator::resolveCurrentPage('page');
            $results = $query->paginate($itemsPerPage, ['*'], 'page', $currentPage);

            return $this->apiResponse($results, 'Chapter List Successful', true, 200);
        }

        $results = $query->get();

        return $this->apiResponse($results, 'Chapter List Successful', true, 200);
    }

    public function saveOrUpdateChapter(ChapterRequest $request)
    {
        try {

            if (empty($request->id)) {
                $chapter = new Chapter();
                $chapter->chapter_code = $this->codeGenerator('CHC', Chapter::class);
                $message = 'Chapter Created Successfully';
            } else {
                $chapter = Chapter::where('id', $request->id)->first();
                $message = 'Chapter Updated Successfully';
            }

            $chapter->fill($request->only($chapter->getFillable()));
            if ($request->hasFile('icon')) {
                $chapter->icon = $this->imageUpload($request, 'icon', 'icon', $chapter->icon);
            }

            $chapter->save();

            return $this->apiResponse([], $message, true, 200);
        } catch (\Throwable $th) {

            return $this->apiResponse([], $th->getMessage(), false, 500);
        }
    }

    public function videoChapterList(Request $request)
    {
        $query = ChapterVideo::ForOrganization('chapter_videos')->select('*');
        $this->applySorting($query, $request);
        $searchKeys = ['chapter_videos.title', 'chapter_videos.title_bn'];
        $this->applySearch($query, $request->input('search'), $searchKeys);

        $pagination = $request->boolean('pagination', true);
        if ($pagination) {
            $itemsPerPage = $request->input('itemsPerPage', 10);
            $currentPage = Paginator::resolveCurrentPage('page');
            $results = $query->paginate($itemsPerPage, ['*'], 'page', $currentPage);

            return $this->apiResponse($results, 'Video Chapter List Successful', true, 200);
        }

        $results = $query->get();

        return $this->apiResponse($results, 'Video Chapter List Successful', true, 200);
    }

    public function saveOrUpdateChapterVideo(ChapterVideoRequest $request)
    {
        try {
            if (empty($request->id)) {
                $chapterVideo = new ChapterVideo();
                $chapterVideo->video_code = $this->codeGenerator('CHV', ChapterVideo::class);
                $message = 'Chapter Video Created Successfully';
            } else {
                $chapterVideo = ChapterVideo::where('id', $request->id)->first();
                $message = 'Chapter Video Updated Successfully';
            }

            $chapterVideo->fill($request->only($chapterVideo->getFillable()));
            if ($request->hasFile('thumbnail')) {
                $chapterVideo->thumbnail = $this->imageUpload($request, 'thumbnail', 'thumbnail', $chapterVideo->thumbnail);
            }

            $chapterVideo->save();
            if ($request->temp_video_id) {
                TempVideo::where('id', $request->temp_video_id)->update(['is_used' => 1]);
            }

            return $this->apiResponse($chapterVideo, $message, true, 200);
        } catch (\Throwable $th) {

            return $this->apiResponse([], $th->getMessage(), false, 500);
        }
    }

    public function deleteChapterVideo (Request $request) {
        try {
            $chapterVideo = ChapterVideo::where('id', $request->id)->first();
            $chapterVideo->delete();
            return $this->apiResponse([], 'Chapter Video Deleted Successfully', true, 200);
        } catch (\Throwable $th) {
            return $this->apiResponse([], $th->getMessage(), false, 500);
        }
    }

    public function saveOrUpdateScript(ScriptRequest $request)
    {

        try {
            if (empty($request->id)) {
                $script = new ChapterScript();
                $script->script_code = $this->codeGenerator('SCR', ChapterScript::class);
                $message = 'Script Created Successfully';
            } else {
                $script = ChapterScript::where('id', $request->id)->first();
                $message = 'Script Updated Successfully';
            }

            $script->fill($request->only($script->getFillable()));

            if ($request->hasFile('thumbnail')) {
                $script->thumbnail = $this->imageUpload($request, 'thumbnail', 'thumbnail', $script->thumbnail);
            }

            if ($request->hasFile('file')) {

                $baseUrl = env('APP_URL');
                $existingFileUrl = $script->raw_url ? explode('/uploads/', $script->raw_url)[1] : null;

                $rawUrl = $this->imageUpload($request, 'file', 'file', $existingFileUrl);

                $script->raw_url = $baseUrl . '/uploads/' . $rawUrl;

            }

            $script->save();

            return $this->apiResponse($script, $message, true, 200);
        } catch (\Throwable $th) {
            return $this->apiResponse([], $th->getMessage(), false, 500);
        }
    }

    public function scriptChapterList(Request $request)
    {
        $query = ChapterScript::ForOrganization('chapter_scripts')->select('*');
        $this->applySorting($query, $request);

        $searchKeys = [
            'chapter_scripts.title',
            'chapter_scripts.title_bn',
        ];
        $this->applySearch($query, $request->input('search'), $searchKeys);

        $pagination = $request->boolean('pagination', true);
        if ($pagination) {
            $itemsPerPage = $request->input('itemsPerPage', 10);
            $currentPage = Paginator::resolveCurrentPage('page');
            $results = $query->paginate($itemsPerPage, ['*'], 'page', $currentPage);

            return $this->apiResponse($results, 'Script Chapter List Successful', true, 200);
        }

        $results = $query->get();

        return $this->apiResponse($results, 'Script Chapter List Successful', true, 200);
    }


    public function deleteChapterScript (Request $request) {
        try {
            $chapterScript = ChapterScript::where('id', $request->id)->first();
            $chapterScript->delete();
            return $this->apiResponse([], 'Chapter Script Deleted Successfully', true, 200);
        } catch (\Throwable $th) {
            return $this->apiResponse([], $th->getMessage(), false, 500);
        }
    }

    public function quizDetailsById(Request $request)
    {
        $quiz = ChapterQuiz::where('id', $request->id)->with([
            'quizItems.chapterQuizQuestion',
            'quizItems.trueFalse',
            'quizItems.matching.matchingAnswers',
            'quizItems.fillInTheBlank.blankAnswers',
            'writtenQuestions'
        ])->first();

        return $this->apiResponse(new ChapterQuizResource($quiz), 'Quiz By Id Successful', true, 200);
    }

    public function quizTypeList()
    {
        $quizTypeList = QuizType::select('id', 'name', 'name_bn', 'participation_limit', 'in_course', 'is_active')->get();

        return $this->apiResponse($quizTypeList, 'Quiz Type List Successful', true, 200);
    }

    public function saveOrUpdateQuiz(ChapterQuizRequest $request)
    {
        try {
            if (empty($request->id)) {
                $chapterQuiz = new ChapterQuiz();
                $chapterQuiz->quiz_code = $this->codeGenerator('CHQ', ChapterQuiz::class);
                $message = 'Chapter Quiz Created Successfully';
            } else {
                $chapterQuiz = ChapterQuiz::where('id', $request->id)->first();
                $message = 'Chapter Quiz Updated Successfully';
            }
            $chapterQuiz->fill($request->only($chapterQuiz->getFillable()));
            $chapterQuiz->save();

            return $this->apiResponse($chapterQuiz, $message, true, 200);
        } catch (\Throwable $th) {
            return $this->apiResponse([], $th->getMessage(), false, 500);
        }
    }

    public function chapterQuizList(Request $request)
    {
        $query = ChapterQuiz::ForOrganization('chapter_quizzes')->select('*');
        $this->applySorting($query, $request);

        $searchKeys = [
            'chapter_quizzes.title',
            'chapter_quizzes.title_bn',
        ];
        $this->applySearch($query, $request->input('search'), $searchKeys);

        $pagination = $request->boolean('pagination', true);
        if ($pagination) {
            $itemsPerPage = $request->input('itemsPerPage', 10);
            $currentPage = Paginator::resolveCurrentPage('page');
            $results = $query->paginate($itemsPerPage, ['*'], 'page', $currentPage);

            return $this->apiResponse($results, 'Chapter Quiz List Successful', true, 200);
        }

        $results = $query->get();

        return $this->apiResponse($results, 'Chapter Quiz List Successful', true, 200);
    }

    public function saveOrUpdateQuizQuestion(QuizQuestionRequest $request)
    {
        try {
            $isNew = empty($request->id);

            // Prepare data for quiz question
            $quizQuestions = [
                'chapter_quiz_id' => $request->chapter_quiz_id,
                'class_level_id' => $request->class_level_id,
                'subject_id' => $request->subject_id,
                'chapter_id' => $request->chapter_id,
                'question_text' => $request->question_text,
                'question_text_bn' => $request->question_text_bn,
                'question_set_id' => $request->question_set_id,
                'chapter_quiz_subject_id' => $request->chapter_quiz_subject_id,
                'option1' => $request->option1,
                'option2' => $request->option2,
                'option3' => $request->option3,
                'option4' => $request->option4,
                'answer1' => $request->answer1,
                'answer2' => $request->answer2,
                'answer3' => $request->answer3,
                'answer4' => $request->answer4,
                'explanation_text' => $request->explanation_text,
                'is_active' => $request->is_active,
            ];

            // Determine if it's a new quiz question or update an existing one
            if ($isNew) {
                $quizQuestion = ChapterQuizQuestion::create($quizQuestions);
                $message = 'Chapter Quiz Question Created Successfully';
            } else {
                $quizQuestion = ChapterQuizQuestion::findOrFail($request->id);
                $quizQuestion->update($quizQuestions);
                $message = 'Chapter Quiz Question Updated Successfully';
            }

            // Handle image uploads if provided
            if (
                $request->hasFile('question_image') ||
                $request->hasFile('option1_image') ||
                $request->hasFile('option2_image') ||
                $request->hasFile('option3_image') ||
                $request->hasFile('option4_image') ||
                $request->hasFile('explanation_image')
            ) {
                $imageData = [
                    'question_image' => ['question_image', 'quiz', 'question_image'],
                    'option1_image' => ['option1_image', 'quiz', 'option1_image'],
                    'option2_image' => ['option2_image', 'quiz', 'option2_image'],
                    'option3_image' => ['option3_image', 'quiz', 'option3_image'],
                    'option4_image' => ['option4_image', 'quiz', 'option4_image'],
                    'explanation_image' => ['explanation_image', 'quiz', 'explanation_image'],
                ];

                foreach ($imageData as $key => $value) {
                    if ($request->hasFile($value[0])) {
                        $quizQuestion->update([
                            $key => $this->imageUploadWithPrefix($request, $value[0], $value[1], $value[2], $quizQuestion->$key),
                        ]);
                    }
                }
            }

            // Update chapter quiz if sufficient questions are added
            if ($isNew) {
                $this->updateChapterQuizIfSufficientQuestions($request->chapter_quiz_id);
            }

            return $this->apiResponse([], $message, true, $isNew ? 201 : 200);
        } catch (\Throwable $th) {
            return $this->apiResponse([], $th->getMessage(), false, 500);
        }
    }

    /**
     * Helper function to update ChapterQuiz if sufficient questions are added.
     */
    private function updateChapterQuizIfSufficientQuestions($chapterQuizId)
    {
        $chapterQuizUpdate = ChapterQuiz::where('id', $chapterQuizId)->first();
        $sets = QuizQuestionSet::get();
        $sufficientQuestion = false;

        foreach ($sets as $set) {
            $setCount = ChapterQuizQuestion::where('chapter_quiz_id', $chapterQuizId)
                ->where('class_level_id', $chapterQuizUpdate->class_level_id)
                ->where('subject_id', $chapterQuizUpdate->subject_id)
                ->where('chapter_id', $chapterQuizUpdate->chapter_id)
                ->where('question_set_id', $set->id)
                ->count();

            if ($chapterQuizUpdate->number_of_question <= $setCount) {
                $sufficientQuestion = true;
            } else {
                $sufficientQuestion = false;
                break;
            }
        }

        if ($sufficientQuestion) {
            ChapterQuiz::where('id', $chapterQuizId)->update([
                'sufficient_question' => true,
            ]);
        }
    }

    public function importQuestion(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'file' => 'required|mimes:xlsx,xls,csv',
        ]);

        if ($validator->fails()) {
            return $this->apiResponse([], $validator->errors(), false, 422);
        }

        try {


            Excel::import(new QuizImport($request->chapter_quiz_id), $request->file('file'));

            return $this->apiResponse(null, 'Question Imported Successfully', true, 200);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Something went wrong', 500);
        }
    }

    public function quizQuestionList(Request $request)
    {
        $quiz_id = $request->id;
        $quizQuestions =
            ChapterQuizQuestion::ForOrganization('chapter_quiz_questions')
                ->where('chapter_quiz_id', $quiz_id)
                ->leftJoin('class_levels', 'class_levels.id', '=', 'chapter_quiz_questions.class_level_id')
                ->leftJoin('subjects', 'subjects.id', '=', 'chapter_quiz_questions.subject_id')
                ->leftJoin('chapters', 'chapters.id', '=', 'chapter_quiz_questions.chapter_id')
                ->leftJoin('chapter_quizzes', 'chapter_quizzes.id', '=', 'chapter_quiz_questions.chapter_quiz_id')
                ->leftJoin('quiz_question_sets', 'quiz_question_sets.id', '=', 'chapter_quiz_questions.question_set_id')
                ->leftJoin('quiz_core_subjects', 'quiz_core_subjects.id', '=', 'chapter_quiz_questions.chapter_quiz_subject_id')
                ->select(
                    'chapter_quiz_questions.id',
                    'chapter_quiz_questions.chapter_quiz_id',
                    'chapter_quiz_questions.class_level_id',
                    'chapter_quiz_questions.subject_id',
                    'chapter_quiz_questions.question_set_id',
                    'chapter_quiz_questions.chapter_quiz_subject_id',
                    'chapter_quiz_questions.chapter_id',
                    'chapter_quiz_questions.question_text',
                    'chapter_quiz_questions.question_text_bn',
                    'chapter_quiz_questions.option1',
                    'chapter_quiz_questions.option2',
                    'chapter_quiz_questions.option3',
                    'chapter_quiz_questions.option4',
                    'chapter_quiz_questions.answer1',
                    'chapter_quiz_questions.answer2',
                    'chapter_quiz_questions.answer3',
                    'chapter_quiz_questions.answer4',
                    'chapter_quiz_questions.explanation_text',
                    'chapter_quiz_questions.is_active',
                    'chapter_quiz_questions.question_image',
                    'chapter_quiz_questions.option1_image',
                    'chapter_quiz_questions.option2_image',
                    'chapter_quiz_questions.option3_image',
                    'chapter_quiz_questions.option4_image',
                    'chapter_quiz_questions.explanation_image',
                    'class_levels.name as class_name',
                    'class_levels.name_bn as class_name_bn',
                    'subjects.name as subject_name',
                    'subjects.name_bn as subject_name_bn',
                    'chapters.name as chapter_name',
                    'chapters.name_bn as chapter_name_bn',
                    'chapter_quizzes.title as quiz_title',
                    'chapter_quizzes.title_bn as quiz_title_bn',
                    'quiz_question_sets.name as question_set_name',
                    'quiz_core_subjects.name as core_subject_name'
                )
                ->get();

        return $this->apiResponse($quizQuestions, 'Chapter Quiz Question List Successful', true, 200);
    }

    public function saveOrUpdateFillInBlankQuestion(FillInBlankQuestionRequest $request)
    {
        try {
            $isNew = empty($request->id);

            // Prepare data for fill in the blank question
            $fillInBlankData = [
                'chapter_quiz_id' => $request->chapter_quiz_id,
                'question_text' => $request->question_text,
                'explanation_text' => $request->explanation_text,
                'is_active' => $request->is_active ?? true,
            ];

            // Determine if it's a new question or update an existing one
            if ($isNew) {
                $fillInBlankQuestion = FillInTheBlankQuestion::create($fillInBlankData);
                $message = 'Fill in the Blank Question Created Successfully';
            } else {
                $fillInBlankQuestion = FillInTheBlankQuestion::findOrFail($request->id);
                $fillInBlankQuestion->update($fillInBlankData);
                $message = 'Fill in the Blank Question Updated Successfully';

                // Delete existing blank answers
                BlankAnswer::where('fill_in_the_blank_question_id', $fillInBlankQuestion->id)->delete();
            }

            // Handle image uploads if provided
            if ($request->hasFile('question_image') || $request->hasFile('explanation_image')) {
                $imageData = [
                    'question_image' => ['question_image', 'quiz', 'question_image'],
                    'explanation_image' => ['explanation_image', 'quiz', 'explanation_image'],
                ];

                foreach ($imageData as $key => $value) {
                    if ($request->hasFile($value[0])) {
                        $fillInBlankQuestion->update([
                            $key => $this->imageUploadWithPrefix($request, $value[0], $value[1], $value[2], $fillInBlankQuestion->$key),
                        ]);
                    }
                }
            }

            // Create blank answers
            foreach ($request->blank_answers as $blankAnswer) {
                $blankAnswerModel = new BlankAnswer();
                $blankAnswerModel->fill_in_the_blank_question_id = $fillInBlankQuestion->id;
                $blankAnswerModel->blank_key = $blankAnswer['blank_key'];
                $blankAnswerModel->blank_answer = $blankAnswer['blank_answer'];
                // $blankAnswerModel->save();
                $blankAnswerModel->saveQuietly();
                // BlankAnswer::create([
                //     'fill_in_the_blank_question_id' => $fillInBlankQuestion->id,
                //     'blank_key' => $blankAnswer['blank_key'],
                //     'blank_answer' => $blankAnswer['blank_answer'],
                // ]);
            }

            // Update chapter quiz if sufficient questions are added
            if ($isNew) {
                $this->updateChapterQuizIfSufficientQuestions($request->chapter_quiz_id);
            }

            return $this->apiResponse([], $message, true, $isNew ? 201 : 200);
        } catch (\Throwable $th) {
            return $this->apiResponse([], $th->getMessage(), false, 500);
        }
    }

    public function saveOrUpdateTrueFalseQuestion(TrueFalseQuestionRequest $request)
    {
        try {
            $isNew = empty($request->id);

            // Prepare data for true/false question
            $trueFalseData = [
                'chapter_quiz_id' => $request->chapter_quiz_id,
                'question_text' => $request->question_text,
                'answer' => $request->answer,
                'explanation_text' => $request->explanation_text,
            ];

            // Determine if it's a new question or update an existing one
            if ($isNew) {
                $trueFalseQuestion = ChapterTrueFalseQuestion::create($trueFalseData);
                $message = 'True/False Question Created Successfully';
            } else {
                $trueFalseQuestion = ChapterTrueFalseQuestion::findOrFail($request->id);
                $trueFalseQuestion->update($trueFalseData);
                $message = 'True/False Question Updated Successfully';
            }

            // Handle image uploads if provided
            if ($request->hasFile('question_image') || $request->hasFile('explanation_image')) {
                $imageData = [
                    'question_image' => ['question_image', 'quiz', 'question_image'],
                    'explanation_image' => ['explanation_image', 'quiz', 'explanation_image'],
                ];

                foreach ($imageData as $key => $value) {
                    if ($request->hasFile($value[0])) {
                        $trueFalseQuestion->update([
                            $key => $this->imageUploadWithPrefix($request, $value[0], $value[1], $value[2], $trueFalseQuestion->$key),
                        ]);
                    }
                }
            }

            // Update chapter quiz if sufficient questions are added
            if ($isNew) {
                $this->updateChapterQuizIfSufficientQuestions($request->chapter_quiz_id);
            }

            return $this->apiResponse([], $message, true, $isNew ? 201 : 200);
        } catch (\Throwable $th) {
            return $this->apiResponse([], $th->getMessage(), false, 500);
        }
    }

    public function saveOrUpdateMatchingQuestion(MatchingQuestionRequest $request)
    {
        try {
            $isNew = empty($request->id);

            // Prepare data for matching question
            $matchingData = [
                'chapter_quiz_id' => $request->chapter_quiz_id,
                'question_text' => $request->question_text,
                'explanation_text' => $request->explanation_text,
                'is_active' => $request->is_active ?? true,
            ];

            // Determine if it's a new question or update an existing one
            if ($isNew) {
                $matchingQuestion = MatchingQuestion::create($matchingData);
                $message = 'Matching Question Created Successfully';
            } else {
                $matchingQuestion = MatchingQuestion::findOrFail($request->id);
                $matchingQuestion->update($matchingData);
                $message = 'Matching Question Updated Successfully';

                // Delete existing matching answers
                MatchingAnswer::where('matching_question_id', $matchingQuestion->id)->delete();
            }

            // Handle image uploads if provided
            if ($request->hasFile('explanation_image')) {
                $matchingQuestion->update([
                    'explanation_image' => $this->imageUpload($request, 'explanation_image', 'quiz', $matchingQuestion->explanation_image),
                ]);
            }

            // Create matching answers
            foreach ($request->matching_answers as $matchingAnswer) {
                $matchingAnswerModel = new MatchingAnswer();
                $matchingAnswerModel->matching_question_id = $matchingQuestion->id;
                $matchingAnswerModel->left_item = $matchingAnswer['left_item'];
                $matchingAnswerModel->right_item = $matchingAnswer['right_item'];
                $matchingAnswerModel->saveQuietly();

            }

            // Update chapter quiz if sufficient questions are added
            if ($isNew) {
                $this->updateChapterQuizIfSufficientQuestions($request->chapter_quiz_id);
            }

            return $this->apiResponse([], $message, true, $isNew ? 201 : 200);
        } catch (\Throwable $th) {
            return $this->apiResponse([], $th->getMessage(), false, 500);
        }
    }

    public function deleteQuestion(Request $request)
    {
        try {
            $question = ChapterQuizQuestion::where('id', $request->id)->first();
            if ($question->question_image != null) {
                $this->deleteImage($question->question_image);
            }
            if ($question->option1_image != null) {
                $this->deleteImage($question->option1_image);
            }
            if ($question->option2_image != null) {
                $this->deleteImage($question->option2_image);
            }
            if ($question->option3_image != null) {
                $this->deleteImage($question->option3_image);
            }
            if ($question->option4_image != null) {
                $this->deleteImage($question->option4_image);
            }
            if ($question->explanation_image != null) {
                $this->deleteImage($question->explanation_image);
            }

            // Delete the associated ChapterQuizItem
            ChapterQuizItem::where('chapter_quiz_question_id', $question->id)->delete();

            $question->delete();

            return $this->apiResponse([], 'Question Deleted Successfully', true, 200);
        } catch (\Throwable $th) {
            return $this->apiResponse([], $th->getMessage(), false, 500);
        }
    }

    /**
     * Delete a fill in the blank question
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteFillInBlankQuestion(Request $request)
    {
        try {
            $question = FillInTheBlankQuestion::where('id', $request->id)->first();
            if (!$question) {
                return $this->apiResponse([], 'Question not found', false, 404);
            }

            // Delete images if they exist
            if ($question->question_image != null) {
                $this->deleteImage($question->question_image);
            }
            if ($question->explanation_image != null) {
                $this->deleteImage($question->explanation_image);
            }

            // Delete associated blank answers
            BlankAnswer::where('fill_in_the_blank_question_id', $question->id)->delete();

            // Delete the associated ChapterQuizItem
            ChapterQuizItem::where('chapter_quiz_fill_in_blank_id', $question->id)->delete();

            $question->delete();

            return $this->apiResponse([], 'Fill in the Blank Question Deleted Successfully', true, 200);
        } catch (\Throwable $th) {
            return $this->apiResponse([], $th->getMessage(), false, 500);
        }
    }

    /**
     * Delete a true/false question
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteTrueFalseQuestion(Request $request)
    {
        try {
            $question = ChapterTrueFalseQuestion::where('id', $request->id)->first();
            if (!$question) {
                return $this->apiResponse([], 'Question not found', false, 404);
            }

            // Delete images if they exist
            if ($question->question_image != null) {
                $this->deleteImage($question->question_image);
            }
            if ($question->explanation_image != null) {
                $this->deleteImage($question->explanation_image);
            }

            // Delete the associated ChapterQuizItem
            ChapterQuizItem::where('chapter_quiz_true_false_id', $question->id)->delete();

            $question->delete();

            return $this->apiResponse([], 'True/False Question Deleted Successfully', true, 200);
        } catch (\Throwable $th) {
            return $this->apiResponse([], $th->getMessage(), false, 500);
        }
    }

    /**
     * Delete a matching question
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteMatchingQuestion(Request $request)
    {
        try {
            $question = MatchingQuestion::where('id', $request->id)->first();
            if (!$question) {
                return $this->apiResponse([], 'Question not found', false, 404);
            }

            // Delete images if they exist
            if ($question->explanation_image != null) {
                $this->deleteImage($question->explanation_image);
            }

            // Delete associated matching answers
            MatchingAnswer::where('matching_question_id', $question->id)->delete();

            // Delete the associated ChapterQuizItem
            ChapterQuizItem::where('chapter_quiz_matching_id', $question->id)->delete();

            $question->delete();

            return $this->apiResponse([], 'Matching Question Deleted Successfully', true, 200);
        } catch (\Throwable $th) {
            return $this->apiResponse([], $th->getMessage(), false, 500);
        }
    }
    public function allContentList(Request $request)
    {
        $menus = Category::where('is_content', true)->get();
        foreach ($menus as $item) {
            if ($item->is_content) {
                $content_list = Content::where('category_id', $item->id)->get();
                $item->contents = $content_list;

                foreach ($content_list as $content) {
                    $content->content_outline = ContentOutline::select(
                        'content_outlines.*',
                        'class_levels.name as class_name',
                        'subjects.name as subject_name',
                        'chapters.name as chapter_name'
                    )
                        ->where('content_outlines.content_id', $content->id)
                        ->leftJoin('class_levels', 'class_levels.id', 'content_outlines.class_level_id')
                        ->leftJoin('subjects', 'subjects.id', 'content_outlines.subject_id')
                        ->leftJoin('chapters', 'chapters.id', 'content_outlines.chapter_id')
                        ->get();
                }
            }
        }

        return response()->json([
            'status' => true,
            'message' => 'List Successful',
            'data' => $menus,
        ], 200);
    }

    public function saveOrUpdateContent(Request $request)
    {
        try {
            $content = new Content();

            $data = $request->only($content->getFillable());

            if (empty($request->id)) {
                $content = Content::create($data);
            } else {
                $content = Content::findOrFail($request->id);
                $content->update($data);
            }

            if ($request->hasFile('icon')) {
                $content->update([
                    'icon' => $this->imageUpload($request, 'icon', 'icon', $content->icon ?? null),
                ]);
            }

            if ($request->hasFile('thumbnail')) {
                $content->update([
                    'thumbnail' => $this->imageUpload($request, 'thumbnail', 'thumbnail', $content->thumbnail ?? null),
                ]);
            }

            $message = empty($request->id) ? 'Content Created Successfully' : 'Content Updated Successfully';

            return $this->apiResponse([], $message, true, empty($request->id) ? 201 : 200);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Something went wrong', 500);
        }
    }

    public function contentList(Request $request)
    {
        $query = Content::ForOrganization('contents')
            ->leftJoin('categories', 'categories.id', '=', 'contents.category_id')
            ->select('contents.*', 'categories.name as category_name');
        $this->applySorting($query, $request);

        $searchKeys = ['contents.title', 'categories.name']; // Adjust the fields you want to search by
        $this->applySearch($query, $request->input('search'), $searchKeys);

        $pagination = $request->boolean('pagination', true);
        if ($pagination) {
            $itemsPerPage = $request->input('itemsPerPage', 10);
            $currentPage = Paginator::resolveCurrentPage('page');
            $results = $query->paginate($itemsPerPage, ['*'], 'page', $currentPage);

            return $this->apiResponse($results, 'Content List', true, 200);
        }

        $results = $query->get();

        return $this->apiResponse($results, 'Content List', true, 200);
    }

    public function saveOrUpdateContentOutline(Request $request)
    {
        try {
            $Content = [
                'title' => $request->title,
                'title_bn' => $request->title_bn,
                'content_id' => $request->content_id,
                'class_level_id' => $request->class_level_id,
                'subject_id' => $request->subject_id,
                'chapter_id' => $request->chapter_id,
                'content_subject_id' => $request->content_subject_id,
                'chapter_script_id' => $request->chapter_script_id,
                'chapter_video_id' => $request->chapter_video_id,
                'chapter_quiz_id' => $request->chapter_quiz_id,
                'is_free' => $request->is_free,
                'color_code' => $request->color_code,
                'sequence' => $request->sequence,
                'is_active ' => $request->is_active,
            ];

            if (empty($request->id)) {
                $contentList = ContentOutline::create($Content);
                if ($request->hasFile('icon')) {
                    $contentList->update([
                        'icon' => $this->imageUpload($request, 'icon', 'icon'),
                    ]);
                }

                return $this->apiResponse([], 'Content Outline Created Successfully', true, 201);
            } else {
                $content = ContentOutline::where('id', $request->id)->first();
                $content->update($Content);
                if ($request->hasFile('icon')) {
                    $content->update([
                        'icon' => $this->imageUpload($request, 'icon', 'icon', $content->icon),
                    ]);
                }

                return $this->apiResponse([], 'Content Outline Updated Successfully', true, 200);
            }
        } catch (\Throwable $th) {
            return response()->json([
                'status' => false,
                'message' => $th->getMessage(),
                'data' => [],
            ], 500);
        }
    }

    public function contentOutlineList(Request $request)
    {
        $id = $request->id ? $request->id : 0;
        $contentOutlineList = ContentOutline::forOrganization('content_outlines')
            ->where('content_subject_id', $id)
            ->leftJoin('contents', 'contents.id', 'content_outlines.content_id')
            ->leftJoin('class_levels', 'class_levels.id', 'content_outlines.class_level_id')
            ->leftJoin('subjects', 'subjects.id', 'content_outlines.subject_id')
            ->leftJoin('chapters', 'chapters.id', 'content_outlines.chapter_id')
            ->select(
                'content_outlines.*',
                'contents.title as content_name',
                'class_levels.name as class_name',
                'subjects.name as subject_name',
                'chapters.name as chapter_name',
            )
            // ->when($id, function ($query, $id) {
            //     return $query->where('content_outlines.content_id', $id);
            // })
            ->get();

        return $this->apiResponse($contentOutlineList, 'Content Outline List', true, 200);
    }

    public function contentOutlineDelete(Request $request)
    {
        try {
            contentOutline::where('id', $request->id)->delete();

            return $this->apiResponse([], 'Content Outline Deleted Successfully', true, 200);
        } catch (\Throwable $th) {
            return response()->json([
                'status' => false,
                'message' => $th->getMessage(),
                'data' => [],
            ], 500);
        }
    }

    public function quizSubjectSaveOrUpdate(QuizSubjectRequest $request)
    {
        try {
            $quizSubject = [
                'chapter_quiz_id' => $request->chapter_quiz_id,
                'quiz_core_subject_id' => $request->quiz_core_subject_id,
                'no_of_question' => $request->no_of_question,
                'is_active' => $request->is_active,
            ];

            if (empty($request->id)) {
                $Quiz = ChapterQuiz::where('id', $request->chapter_quiz_id)->first();
                $numberOfQuiz = $Quiz->number_of_question;
                $numberOfChapterQuizSubject = ChapterQuizSubject::where('chapter_quiz_id', $request->chapter_quiz_id)->get();
                if (in_array($request->quiz_core_subject_id, $numberOfChapterQuizSubject->pluck('quiz_core_subject_id')->toArray())) {
                    return $this->apiResponse([], 'Subject Already Added', false, 500);
                } else {
                    $alreadyNumberOfQuiz = $numberOfChapterQuizSubject->sum('no_of_question');
                    $sumOfQuiz = $alreadyNumberOfQuiz + $request->no_of_question;
                    if ($numberOfQuiz > $alreadyNumberOfQuiz && $numberOfQuiz >= $sumOfQuiz) {
                        $quizSubject = ChapterQuizSubject::create($quizSubject);

                        return $this->apiResponse([], 'Quiz Subject Created Successfully', true, 201);
                    } else {
                        return $this->apiResponse([], 'Number of Question Exceed', false, 500);
                    }
                }
            } else {
                $quizSubject = ChapterQuizSubject::where('id', $request->id)->first();
                $Quiz = ChapterQuiz::where('id', $request->chapter_quiz_id)->first();
                $numberOfQuiz = $Quiz->number_of_question;
                $numberOfChapterQuizSubject = ChapterQuizSubject::where('chapter_quiz_id', $request->chapter_quiz_id)
                    ->where('id', '!=', $request->id)
                    ->get();
                $alreadyNumberOfQuiz = $numberOfChapterQuizSubject->sum('no_of_question');
                $sumOfQuiz = $alreadyNumberOfQuiz + $request->no_of_question;
                if ($numberOfQuiz >= $alreadyNumberOfQuiz && $numberOfQuiz >= $sumOfQuiz) {
                    $quizSubject->update(
                        [
                            'no_of_question' => $request->no_of_question,
                            'is_active' => $request->is_active,
                        ]
                    );

                    return $this->apiResponse([], 'Quiz Subject Updated Successfully', true, 200);
                } else {
                    return $this->apiResponse([], 'Number of Question Exceed', false, 500);
                }
            }
        } catch (\Throwable $th) {
            return $this->apiResponse([], $th->getMessage(), false, 500);
        }
    }

    public function chapterQuizSubjectList(Request $request)
    {

        $chapter_quiz_id = $request->id;
        $chapterQuizSubjectList = ChapterQuizSubject::ForOrganization('chapter_quiz_subjects')
            ->where('chapter_quiz_id', $chapter_quiz_id)
            ->leftJoin('chapter_quizzes', 'chapter_quizzes.id', 'chapter_quiz_subjects.chapter_quiz_id')
            ->leftJoin('quiz_core_subjects', 'quiz_core_subjects.id', 'chapter_quiz_subjects.quiz_core_subject_id')
            ->select(
                'chapter_quiz_subjects.*',
                'chapter_quizzes.title as quiz_title',
                'quiz_core_subjects.name as subject_name',
            )
            ->get();

        return $this->apiResponse($chapterQuizSubjectList, 'Chapter Quiz Subject List', true, 200);
    }

    public function coreSubjectList(Request $request)
    {
        $subject = QuizCoreSubjects::get();

        return $this->apiResponse($subject, 'Subject List', true, 200);
    }

    public function writtenQuestionList(Request $request, $id)
    {
        $writtenQuestion = ChapterQuizWrittenQuestion::ForOrganization('chapter_quiz_written_questions')
            ->where('chapter_quiz_id', $id)
            ->leftJoin('chapter_quizzes', 'chapter_quizzes.id', 'chapter_quiz_written_questions.chapter_quiz_id')
            ->select(
                'chapter_quiz_written_questions.*',
                'chapter_quizzes.title as quiz_title',
            )
            ->get();

        return $this->apiResponse($writtenQuestion, 'Written Question List', true, 200);
    }

    public function saveOrUpdateWrittenQuestion(Request $request)
    {

        try {
            $question = [
                'chapter_quiz_id' => $request->chapter_quiz_id,
                'marks' => $request->marks,
                'description' => $request->description,
                'instruction' => $request->instruction,
                'duration' => $request->duration,
                'no_of_question' => $request->no_of_question,
                'is_active' => $request->is_active,
            ];

            if (empty($request->id)) {

                $alreadyUploadedQuestion = ChapterQuizWrittenQuestion::where('chapter_quiz_id', $request->chapter_quiz_id)->get();
                if (count($alreadyUploadedQuestion) > 0) {
                    return $this->apiResponse([], 'Already Uploaded', false, 500);
                }
                if ($request->hasFile('question_attachment')) {
                    $question['question_attachment'] = $this->imageUpload($request, 'question_attachment', 'attachment');
                }

                $writtenQuestion = ChapterQuizWrittenQuestion::create($question);


                return $this->apiResponse([], 'Written Question Created Successfully', true, 201);
            } else {
                $writtenQuestion = ChapterQuizWrittenQuestion::where('id', $request->id)->first();

                if ($request->hasFile('question_attachment')) {
                    $question['question_attachment'] = $this->imageUpload($request, 'question_attachment', 'attachment', $writtenQuestion->question_attachment);
                }

                $writtenQuestion->update($question);

                return $this->apiResponse([], 'Written Question Updated Successfully', true, 200);
            }
        } catch (\Throwable $th) {
            //throw $th;
            return $this->apiResponse([], $th->getMessage(), false, 500);
        }
    }

    public function contentSubjectAssignSaveOrUpdate(Request $request)
    {
        try {
            if (empty($request->id)) {
                foreach ($request->subjectArr as $value) {
                    ContentSubject::create([
                        'content_id' => $request->content_id,
                        'class_level_id' => $value['class_level_id'],
                        'subject_id' => $value['subject_id'],
                        'is_active' => $value['is_active'],
                    ]);
                }

                return $this->apiResponse([], 'Content Subjects Created Successfully', true, 201);
            } else {
                $contentSubject = ContentSubject::find($request->id);

                if (! $contentSubject) {
                    return $this->apiResponse([], 'Content Subject Not Found', false, 404);
                }

                $contentSubject->update($request->only(['content_id', 'class_level_id', 'subject_id', 'is_active']));

                return $this->apiResponse([], 'Content Subject Updated Successfully', true, 200);
            }
        } catch (\Throwable $th) {
            return $this->apiResponse([], 'An error occurred: '.$th->getMessage(), false, 500);
        }
    }

    public function contentSubjectList(Request $request, $id)
    {
        $contentSubjectList = ContentSubject::ForOrganization('content_subjects')
            ->where('content_id', $id)
            ->leftJoin('contents', 'contents.id', 'content_subjects.content_id')
            ->leftJoin('class_levels', 'class_levels.id', 'content_subjects.class_level_id')
            ->leftJoin('subjects', 'subjects.id', 'content_subjects.subject_id')
            ->select(
                'content_subjects.*',
                'contents.title as content_name',
                'class_levels.name as class_name',
                'subjects.name as subject_name',
            )
            ->get();

        return $this->apiResponse($contentSubjectList, 'Content Subject List', true, 200);
    }

    public function contentDetailsByID(Request $request)
    {
        try {
            $contentId = $request->content_id ? $request->content_id : 0;
            $content = Content::select(
                'contents.*',
                'categories.name as category_name',
            )
                ->leftJoin('categories', 'categories.id', 'contents.category_id')
                ->where('contents.id', $contentId)
                ->first();

            $content->subjects = ContentSubject::select(
                'content_subjects.*',
                'contents.title as content_name',
                'class_levels.name as class_name',
                'subjects.name as subject_name',
            )
                ->leftJoin('contents', 'contents.id', 'content_subjects.content_id')
                ->leftJoin('class_levels', 'class_levels.id', 'content_subjects.class_level_id')
                ->leftJoin('subjects', 'subjects.id', 'content_subjects.subject_id')
                ->where('content_subjects.content_id', $contentId)
                ->orderBy('subjects.name', 'ASC')
                ->get();

            return $this->apiResponse($content, 'Content Subject List', true, 200);
        } catch (\Throwable $th) {
            return $this->apiResponse([], $th->getMessage(), false, 500);
        }
    }

    public function ContentOutlineDetailsByID(Request $request)
    {
        $contentSubjectId = $request->content_subject_id ?: 0;
        $authId = $request->user->id ?? null;
        try {

            $contentSubject = ContentSubject::select(
                'content_subjects.*',
                'contents.title as content_name',
                'class_levels.name as class_name',
                'subjects.name as subject_name'
            )
                ->leftJoin('contents', 'contents.id', 'content_subjects.content_id')
                ->leftJoin('class_levels', 'class_levels.id', 'content_subjects.class_level_id')
                ->leftJoin('subjects', 'subjects.id', 'content_subjects.subject_id')
                ->where('content_subjects.id', $contentSubjectId)
                ->orderBy('subjects.name', 'ASC')
                ->first();

            $isExist = CourseParticipant::where('item_id', $contentSubject->content_id)
                ->where('user_id', $authId)
                ->where('item_type', 'Content')
                ->exists();

            $contentSubject->is_purchased = $isExist;

            if (! $contentSubject) {
                return $this->apiResponse(null, 'Content Subject not found', false, 404);
            }

            $mainSubjectId = $contentSubject->subject_id ?? 0;

            $lectures = Chapter::with(['scripts:id,class_level_id,subject_id,chapter_id,title,description,is_free,sequence', 'videos:id,class_level_id,subject_id,chapter_id,title,description,duration,is_free,sequence', 'quiz:id,class_level_id,subject_id,chapter_id,title,description,is_free,sequence'])
                ->where('subject_id', $mainSubjectId)
                ->get();

            $contentSubject->content_outlines = $lectures;

            return $this->apiResponse($contentSubject, 'Content Details Successful!', true, 200);
        } catch (\Exception $e) {
            return $this->apiResponse(null, $e->getMessage(), false, 500);
        }
    }
}
