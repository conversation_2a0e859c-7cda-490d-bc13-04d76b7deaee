<?php

namespace App\Http\Controllers;

use App\Models\PaymentType;
use App\Models\PaymentTypeItem;
use Illuminate\Http\Request;
use App\Http\Requests\PaymentTypeCreateRequest;
use App\Http\Requests\PaymentTypeUpdateRequest;

class PaymentTypeController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $paymentTypes = PaymentType::with('items')->get();
        return $this->apiResponse($paymentTypes, 'Payment type list fetched successfully', true, 200);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(PaymentTypeCreateRequest $request)
    {
        $validated = $request->validated();

        $paymentIcon = $this->imageUpload($request, 'icon', 'payment_icon');
        $validated['icon'] = $paymentIcon;

        $paymentType = PaymentType::create($validated);

        // Create associated PaymentTypeItems
        if (isset($validated['items'])) {
            foreach ($validated['items'] as $item) {
                $paymentType->items()->create($item);
            }
        }

        return $this->apiResponse($paymentType->load('items'), 'Payment type created successfully', true, 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(PaymentType $paymentType)
    {
        $paymentType->load('items');
        return $this->apiResponse($paymentType, 'Payment type details fetched successfully', true, 200);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(PaymentTypeUpdateRequest $request, PaymentType $paymentType)
    {
        $validated = $request->validated();

        $paymentIcon = $this->imageUpload($request, 'icon', 'payment_icon', $paymentType->icon);
        $validated['icon'] = $paymentIcon;

        $paymentType->update($validated);

        // Update or create associated PaymentTypeItems
        if (isset($validated['items'])) {
            foreach ($validated['items'] as $item) {
                if (isset($item['id'])) {
                    // Update existing PaymentTypeItem
                    PaymentTypeItem::where('id', $item['id'])
                        ->where('payment_type_id', $paymentType->id)
                        ->update(['field_name' => $item['field_name']]);
                } else {
                    // Create new PaymentTypeItem
                    $paymentType->items()->create($item);
                }
            }
        }

        return $this->apiResponse($paymentType->load('items'), 'Payment type updated successfully', true, 200);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(PaymentType $paymentType)
    {
        // Delete associated PaymentTypeItems
        $paymentType->items()->delete();
        $paymentType->delete();

        return $this->apiResponse(null, 'Payment type and associated items deleted successfully', true, 200);
    }

}
