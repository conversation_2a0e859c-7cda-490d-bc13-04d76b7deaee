<?php

namespace App\Http\Controllers;

use App\Models\SchoolInformation;
use App\Models\User;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class SchoolController extends Controller
{
    public function schoolList(Request $request)
    {
        $school_list = SchoolInformation::select('id', 'title', 'details', 'address', 'email', 'phone_no', 'logo', 'contact_person', 'is_active')->where('is_active', true)->get();

        return response()->json([
            'status' => true,
            'message' => 'List Successful',
            'data' => $school_list,
        ], 200);
    }

    public function adminSchoolList(Request $request)
    {
        $school_list = SchoolInformation::all();

        return response()->json([
            'status' => true,
            'message' => 'List Successful',
            'data' => $school_list,
        ], 200);
    }

    public function saveOrUpdateSchool(Request $request)
    {
        try {
            DB::beginTransaction();

            $formData = json_decode($request->data, true);

            if (! filter_var($formData['email'], FILTER_VALIDATE_EMAIL)) {
                return $this->apiResponse([], 'Please, enter valid email address!', false, 200);
            }

            $logo_url = null;
            if ($request->hasFile('file')) {
                $logo_url = $this->uploadImage($request->file('file'), 'uploads/school', 'logo_');
            }

            if (! empty($formData['id'])) {
                $school = SchoolInformation::findOrFail($formData['id']);
                $school->update(array_merge($formData, $logo_url ? ['logo' => $logo_url] : []));

                DB::commit();

                return $this->apiResponse([], 'School has been updated successfully', true, 200);
            } else {
                if (SchoolInformation::where('title', $formData['title'])->exists() ||
                    SchoolInformation::where('email', $formData['email'])->exists()) {
                    return $this->apiResponse([], 'School already exists!', false, 200);
                }

                $school = SchoolInformation::create(array_merge($formData, $logo_url ? ['logo' => $logo_url] : []));

                $user = User::create([
                    'name' => $formData['title'],
                    'email' => $formData['email'],
                    'contact_no' => $formData['phone_no'],
                    'school_id' => $school->id,
                    'address' => $formData['address'],
                    'institution' => $formData['title'],
                    'education' => null,
                    'user_type' => 'SchoolAdmin',
                    'password' => Hash::make($formData['password'] ?? '123456'),
                ]);

                $school->update(['user_id' => $user->id]);

                if ($logo_url) {
                    $user->update(['image' => $logo_url]);
                }

                DB::commit();

                return $this->apiResponse([], 'School has been created successfully', true, 200);
            }
        } catch (Exception $e) {
            DB::rollBack();

            return $this->apiResponse($e->getMessage(), 'Something went wrong', false, 500);
        }
    }
}
