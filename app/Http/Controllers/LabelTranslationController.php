<?php

namespace App\Http\Controllers;

use App\Models\LabelTranslation;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use App\Http\Requests\LabelTranslationRequest;
use App\Services\LabelTranslationService;

class LabelTranslationController extends Controller
{

    protected $labelTranslationService;

    public function __construct(LabelTranslationService $labelTranslationService)
    {
        $this->labelTranslationService = $labelTranslationService;
    }

    public function index(Request $request)
    {
        try {
            $perPage = $request->get('per_page', 15);
            $translations = $this->labelTranslationService->getPaginated($perPage);
            return $this->apiResponse($translations, 'Label translations retrieved successfully', true, 200);
        } catch (\Exception $e) {
            return $this->apiResponse(null, $e->getMessage(), false, 500);
        }
    }
    public function getTranslations()
    {
        try {
            $translations = $this->labelTranslationService->getTranslations();
            return $this->apiResponse($translations, 'Label translations retrieved successfully', true, 200);
        } catch (\Exception $e) {
            return $this->apiResponse(null, $e->getMessage(), false, 500);
        }
    }

    public function store(LabelTranslationRequest $request)
    {
        try {
            $data = $request->validated();
            $data['organization_id'] = auth()->user()->organization_id;
            $data['label_key'] = strtolower($data['label_key']);
            $translation = $this->labelTranslationService->create($data);
            return $this->apiResponse($translation, 'Label translation created successfully', true, 201);
        } catch (\Exception $e) {
            return $this->apiResponse(null, $e->getMessage(), false, 500);
        }
    }

    public function show(int $id)
    {
        try {
            $translation = $this->labelTranslationService->getById($id);
            return $this->apiResponse($translation, 'Label translation retrieved successfully', true, 200);
        } catch (\Exception $e) {
            return $this->apiResponse(null, $e->getMessage(), false, 404);
        }
    }

    public function update(LabelTranslationRequest $request, LabelTranslation $labelTranslation)
    {
        try {
            $translation = $this->labelTranslationService->update($labelTranslation, $request->validated());
            return $this->apiResponse($translation, 'Label translation updated successfully', true, 200);
        } catch (\Exception $e) {
            return $this->apiResponse(null, $e->getMessage(), false, 500);
        }
    }

    public function destroy(LabelTranslation $labelTranslation)
    {
        try {
            $this->labelTranslationService->delete($labelTranslation);
            return $this->apiResponse(null, 'Label translation deleted successfully', true, 200);
        } catch (\Exception $e) {
            return $this->apiResponse(null, $e->getMessage(), false, 500);
        }
    }
}

