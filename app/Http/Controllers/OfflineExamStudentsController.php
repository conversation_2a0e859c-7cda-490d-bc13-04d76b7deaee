<?php

namespace App\Http\Controllers;

use App\Models\OfflineExamStudents;
use Illuminate\Http\Request;

class OfflineExamStudentsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(OfflineExamStudents $offlineExamStudents)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(OfflineExamStudents $offlineExamStudents)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, OfflineExamStudents $offlineExamStudents)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(OfflineExamStudents $offlineExamStudents)
    {
        //
    }
}
