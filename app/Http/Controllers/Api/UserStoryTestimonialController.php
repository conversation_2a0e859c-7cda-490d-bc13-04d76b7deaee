<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\UserStoryTestimonial;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class UserStoryTestimonialController extends Controller
{
    /**
     * Display a listing of active testimonials.
     */
    public function index(Request $request): JsonResponse
    {
        $query = UserStoryTestimonial::with('userStory')
            ->active()
            ->ordered();

        // Filter by user story if provided
        if ($request->has('user_story_id')) {
            $query->where('user_story_id', $request->user_story_id);
        }

        // Optional filtering
        if ($request->has('limit')) {
            $limit = min($request->integer('limit', 10), 50); // Max 50 items
            $testimonials = $query->limit($limit)->get();
        } else {
            $testimonials = $query->paginate($request->integer('per_page', 10));
        }

        // Transform data for frontend consumption
        $transformedData = $testimonials instanceof \Illuminate\Pagination\LengthAwarePaginator
            ? $testimonials->getCollection()->map(function ($testimonial) {
                return $this->transformTestimonial($testimonial);
            })
            : $testimonials->map(function ($testimonial) {
                return $this->transformTestimonial($testimonial);
            });

        if ($testimonials instanceof \Illuminate\Pagination\LengthAwarePaginator) {
            $testimonials->setCollection($transformedData);
            $responseData = $testimonials;
        } else {
            $responseData = $transformedData;
        }

        return $this->apiResponse($responseData, 'Testimonials retrieved successfully', true, 200);
    }

    /**
     * Display the specified testimonial.
     */
    public function show(UserStoryTestimonial $testimonial): JsonResponse
    {
        if (!$testimonial->is_active) {
            return $this->apiResponse(null, 'Testimonial not found', false, 404);
        }

        $testimonial->load('userStory');

        return $this->apiResponse($this->transformTestimonial($testimonial), 'Testimonial retrieved successfully', true, 200);
    }

    /**
     * Get testimonials by user story.
     */
    public function byUserStory(Request $request, $userStoryId): JsonResponse
    {
        $testimonials = UserStoryTestimonial::where('user_story_id', $userStoryId)
            ->active()
            ->ordered()
            ->get()
            ->map(function ($testimonial) {
                return $this->transformTestimonial($testimonial);
            });

        return $this->apiResponse($testimonials, 'Testimonials retrieved successfully', true, 200);
    }

    /**
     * Get featured testimonials (first 6 active testimonials).
     */
    public function featured(): JsonResponse
    {
        $testimonials = UserStoryTestimonial::with('userStory')
            ->active()
            ->ordered()
            ->limit(6)
            ->get()
            ->map(function ($testimonial) {
                return $this->transformTestimonial($testimonial);
            });

        return $this->apiResponse($testimonials, 'Featured testimonials retrieved successfully', true, 200);
    }

    /**
     * Transform testimonial data for frontend consumption.
     */
    private function transformTestimonial($testimonial): array
    {
        return [
            'id' => $testimonial->id,
            'name' => $testimonial->name,
            'designation' => $testimonial->designation,
            'speech' => $testimonial->speech,
            'image' => $testimonial->image_url,
            'datetime' => $testimonial->datetime?->format('Y-m-d H:i:s'),
            'sort_order' => $testimonial->sort_order,
            'user_story' => $testimonial->userStory ? [
                'id' => $testimonial->userStory->id,
                'title' => $testimonial->userStory->title,
                'color' => $testimonial->userStory->color,
                'background' => $testimonial->userStory->background
            ] : null
        ];
    }
}
