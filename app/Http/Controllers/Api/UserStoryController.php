<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\UserStory;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class UserStoryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        // Get only the first user story with its testimonials
        $userStory = UserStory::with(['activeTestimonials' => function($query) {
            $query->orderBy('sort_order');
        }])->active()->ordered()->first();

        if (!$userStory) {
            return $this->apiResponse([], 'No user stories found', false, 404);
        }

        return $this->apiResponse($userStory, 'User story retrieved successfully', true, 200);

    }

    /**
     * Display the specified resource.
     */
    public function show(UserStory $userStory): JsonResponse
    {
        if (!$userStory->is_active) {
            return response()->json([
                'status' => false,
                'message' => 'User story not found'
            ], 404);
        }

        $userStory->load(['activeTestimonials' => function($query) {
            $query->orderBy('sort_order');
        }]);

        return response()->json([
            'status' => true,
            'message' => 'User story retrieved successfully',
            'data' => $userStory
        ]);
    }

    /**
     * Get testimonials for a specific user story.
     */
    public function testimonials(UserStory $userStory): JsonResponse
    {
        if (!$userStory->is_active) {
            return response()->json([
                'status' => false,
                'message' => 'User story not found'
            ], 404);
        }

        $testimonials = $userStory->activeTestimonials()
            ->orderBy('sort_order')
            ->get()
            ->map(function ($testimonial) {
                return [
                    'id' => $testimonial->id,
                    'name' => $testimonial->name,
                    'designation' => $testimonial->designation,
                    'speech' => $testimonial->speech,
                    'image' => $testimonial->image,
                    'datetime' => $testimonial->datetime?->format('Y-m-d H:i:s'),
                    'sort_order' => $testimonial->sort_order
                ];
            });

        return response()->json([
            'status' => true,
            'message' => 'Testimonials retrieved successfully',
            'data' => $testimonials
        ]);
    }

    /**
     * Get all user stories with testimonials for landing page.
     */
    public function landing(): JsonResponse
    {
        $userStories = UserStory::with(['activeTestimonials' => function($query) {
            $query->orderBy('sort_order');
        }])
        ->active()
        ->first();


        return response()->json([
            'status' => true,
            'message' => 'Landing page user stories retrieved successfully',
            'data' => $userStories
        ]);
    }
}
