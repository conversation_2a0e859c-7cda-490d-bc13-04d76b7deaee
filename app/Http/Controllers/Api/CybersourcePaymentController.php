<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\CyberSourceClient;
use Illuminate\Http\Request;

class CybersourcePaymentController extends Controller
{
    public function session(Request $r)
    {
        $cs = new CyberSourceClient($r->user()->organization_id);
        return response()->json($cs->createSession());
    }

    public function pay(Request $r)
    {
        $cs = new CyberSourceClient($r->user()->organization_id);
        $payload = [
            'clientReferenceInformation' => ['code' => uniqid('order_')],
            'processingInformation' => ['commerceIndicator' => 'internet'],
            'paymentInformation' => ['tokenizedCard' => [
                'transientToken' => $r->paymentToken
            ]],
            'orderInformation' => ['amountDetails' => [
                'totalAmount' => number_format($r->amount, 2, '.', ''),
                'currency' => $r->currency,
            ]],
        ];

        return response()->json($cs->createPayment($payload));
    }
}
