<?php

namespace App\Http\Controllers;

use App\Models\ClassScheduleStudents;
use Illuminate\Http\Request;

class ClassScheduleStudentsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(ClassScheduleStudents $classScheduleStudents)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ClassScheduleStudents $classScheduleStudents)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ClassScheduleStudents $classScheduleStudents)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ClassScheduleStudents $classScheduleStudents)
    {
        //
    }
}
