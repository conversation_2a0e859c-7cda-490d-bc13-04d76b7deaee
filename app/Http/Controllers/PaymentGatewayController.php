<?php

namespace App\Http\Controllers;

use App\Models\PaymentGateway;
use Illuminate\Http\Request;

class PaymentGatewayController extends Controller
{
    public function index()
    {
        $gateways = PaymentGateway::all();
        return $this->apiResponse($gateways, 'Payment gateways retrieved successfully', true, 200);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'type' => 'nullable|string|max:255',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg,webp|max:2048',
            'short_description' => 'nullable|string',
            'creadential_format' => 'nullable',
            'is_active' => 'boolean',
        ]);

        if ($request->hasFile('logo')) {
            $validated['logo'] = $this->imageUpload($request, 'logo', 'logo');
        }

        $gateway = PaymentGateway::create($validated);
        return $this->apiResponse($gateway, 'Payment gateway created successfully', true, 201);
    }

    public function show($id)
    {
        $gateway = PaymentGateway::findOrFail($id);
        return $this->apiResponse($gateway, 'Payment gateway details retrieved successfully', true, 200);
    }

    public function update(Request $request, $id)
    {
        $gateway = PaymentGateway::findOrFail($id);
        $validated = $request->validate([
            'name' => 'sometimes|required|string|max:255',
            'type' => 'nullable|string|max:255',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg,webp|max:2048',
            'short_description' => 'nullable|string',
            'creadential_format' => 'nullable',
            'is_active' => 'boolean',
        ]);

        if ($request->hasFile('logo')) {
            $validated['logo'] = $this->imageUpload($request, 'logo', 'logo', $gateway->logo);
        }

        $gateway->update($validated);
        return $this->apiResponse($gateway, 'Payment gateway updated successfully', true, 200);
    }

    public function destroy($id)
    {
        $gateway = PaymentGateway::findOrFail($id);
        $gateway->delete();
        return $this->apiResponse(null, 'Payment gateway deleted successfully', true, 200);
    }
}
