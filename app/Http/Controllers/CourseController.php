<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Http;
use App\Http\Requests\CourseRequest;
use App\Http\Traits\HelperTrait;
use App\Models\BlankAnswer;
use App\Models\Category;
use App\Models\ChapterQuiz;
use App\Models\ChapterQuizQuestion;
use App\Models\ChapterQuizResult;
use App\Models\ChapterQuizResultAnswer;
use App\Models\ChapterQuizResultFillInTheBlank;
use App\Models\ChapterQuizResultItem;
use App\Models\ChapterQuizResultMatching;
use App\Models\ChapterQuizResultTrueFalse;
use App\Models\ChapterQuizSubject;
use App\Models\ChapterQuizItem;
use App\Models\ChapterQuizSubjectWiseResult;
use App\Models\ChapterQuizWrittenAttachment;
use App\Models\ChapterQuizWrittenMark;
use App\Models\ChapterQuizWrittenQuestion;
use App\Models\ChapterTrueFalseQuestion;
use App\Models\ClassSchedule;
use App\Models\Content;
use App\Models\Subject;
use App\Models\ContentOutline;
use App\Models\ChapterVideo;
use App\Models\ChapterScript;
use App\Models\Course;
use App\Models\FillInTheBlankQuestion;
use App\Models\MatchingAnswer;
use App\Models\MatchingQuestion;
use App\Models\CourseClassRoutine;
use App\Models\CourseFaq;
use App\Models\CourseFeature;
use App\Models\CourseLearningItem;
use App\Models\CourseMentor;
use App\Models\CourseCategory;
use App\Models\CourseOutline;
use App\Models\CourseParticipant;
use App\Models\CourseStudentMapping;
use App\Models\CourseType;
use App\Models\MentorInformation;
use App\Models\MentorZoomLink;
use App\Models\Payment;
use App\Models\PaymentDetail;
use App\Models\QuizQuestionSet;
use App\Models\StudentInformation;
use App\Models\StudentJoinHistory;
use App\Http\Resources\Mobile\MentorClassDetailsResource;
use Illuminate\Http\Request;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use App\UrlCheckTrait;

class CourseController extends Controller
{
    use HelperTrait, UrlCheckTrait;

    public function courseListByCategory(Request $request)
    {
        $courseList = Course::where('category_id', $request->category_id)
            ->select('id', 'name')->get();

        return response()->json([
            'status' => true,
            'message' => 'List Successful',
            'data' => $courseList,
        ], 200);
    }

    public function coursesList(Request $request)
    {
        $query = Course::query();
        $query->where('is_active', true);
        $query->select([
            'id',
            'organization_id',
            'title',
            'title_bn',
            'category_id',
            'sub_category_id',
            'description',
            'thumbnail',
            'icon',
            'number_of_enrolled',
            'regular_price',
            'sale_price',
            'discount_percentage',
            'rating',
            'has_life_coach',
            'is_free',
            'sequence',
            'is_active',
            'is_featured',
            'created_by',
            'created_at',
            'updated_at'
        ]);
        $filters = ['category_id' => '=', 'sub_category_id' => '='];
        $searchKeys = ['title', 'description'];

        $this->applyFilters($query, $request, $filters);
        $this->applySorting($query, $request);
        $this->applySearch($query, $request->input('search'), $searchKeys);

        $pagination = $request->boolean('pagination', true);
        if ($pagination) {
            $itemsPerPage = $request->input('itemsPerPage', 8);
            $currentPage = Paginator::resolveCurrentPage('page');
            $results = $query->paginate($itemsPerPage, ['*'], 'page', $currentPage);

            return $this->apiResponse($results, 'Course List', true, 200);
        }

        $results = $query->get();

        return $this->apiResponse($results, 'Course List', true, 200);
    }

    public function courseListForFilter()
    {

        $courseList = Course::select('id', 'title')->get();

        return response()->json([
            'status' => true,
            'message' => 'List Successful',
            'data' => $courseList,
        ], 200);
    }

    public function courseListForFilterMentor()
    {
        $user = Auth::user();
        $mentor = MentorInformation::where('user_id', $user->id)->first();
        $courseIdList = CourseMentor::where('mentor_id', $mentor->id)->pluck('course_id')->toArray();
        $courseList = Course::select('id', 'title')->whereIn('id', $courseIdList)->get();

        return response()->json([
            'status' => true,
            'message' => 'List Successful',
            'data' => $courseList,
        ], 200);
    }

    public function courseDetailsByID(Request $request)
    {
        $course_id = $request->course_id ? $request->course_id : 0;

        if (! $course_id) {
            return response()->json([
                'status' => false,
                'message' => 'Please, attach menu ID',
                'data' => [],
            ], 422);
        }

        $course = Course::where('id', $course_id)->orderBy('sequence', 'ASC')->first();

        $course->course_outline = CourseOutline::select(
            'course_outlines.*',
            'class_levels.name as class_name',
            'subjects.name as subject_name',
            'chapters.name as chapter_name',
            'chapter_videos.title as video_title',
            'chapter_videos.title_bn as video_title_bn',
            'chapter_videos.author_name as video_author_name',
            'chapter_videos.author_details as video_author_details',
            'chapter_videos.raw_url as video_raw_url',
            'chapter_videos.s3_url as video_s3_url',
            'chapter_videos.youtube_url as video_youtube_url',
            'chapter_videos.download_url as video_download_url',
            'chapter_videos.duration as video_duration',
            'chapter_videos.thumbnail as video_thumbnail',
            'chapter_videos.is_free as video_is_free',
            'chapter_scripts.title as script_title',
            'chapter_scripts.title_bn as script_title_bn',
            'chapter_scripts.raw_url as script_raw_url',
            'chapter_scripts.is_free as script_is_free',
            'chapter_quizzes.title as quiz_title',
            'chapter_quizzes.title_bn as quiz_title_bn',
            'chapter_quizzes.duration as quiz_duration',
            'chapter_quizzes.is_free as quiz_is_free',
        )
            ->where('course_outlines.course_id', $course_id)
            ->leftJoin('class_levels', 'class_levels.id', 'course_outlines.class_level_id')
            ->leftJoin('subjects', 'subjects.id', 'course_outlines.subject_id')
            ->leftJoin('chapters', 'chapters.id', 'course_outlines.chapter_id')
            ->leftJoin('chapter_videos', 'chapter_videos.id', 'course_outlines.chapter_video_id')
            ->leftJoin('chapter_scripts', 'chapter_scripts.id', 'course_outlines.chapter_script_id')
            ->leftJoin('chapter_quizzes', 'chapter_quizzes.id', 'course_outlines.chapter_quiz_id')
            ->get();

        $course->course_routine = CourseClassRoutine::where('course_id', $course_id)->get();
        $course->course_feature = CourseFeature::where('course_id', $course_id)->get();
        $course->course_mentor = CourseMentor::select('course_mentors.*', 'mentor_informations.name', 'mentor_informations.education', 'mentor_informations.institute')
            ->where('course_mentors.course_id', $course_id)
            ->leftJoin('mentor_informations', 'mentor_informations.id', 'course_mentors.mentor_id')
            ->get();

        $course->course_faq = CourseFaq::where('course_id', $course_id)->get();

        return response()->json([
            'status' => true,
            'message' => 'List Successful',
            'data' => $course,
        ], 200);
    }

    public function courseListByID(Request $request)
    {
        $menu_id = $request->menu_id ? $request->menu_id : 0;

        if (! $menu_id) {
            return response()->json([
                'status' => false,
                'message' => 'Please, attach menu ID',
                'data' => [],
            ], 422);
        }

        $menu = Category::where('id', $menu_id)->first();

        if (empty($menu)) {
            return response()->json([
                'status' => false,
                'message' => 'Menu not found!',
                'data' => [],
            ], 404);
        }

        if ($menu->is_course) {
            $courses = Course::where('category_id', $menu->id)->orderBy('sequence', 'ASC')->get();
            $menu->courses = $courses;

            foreach ($courses as $course) {
                $course->course_outline = CourseOutline::select(
                    'course_outlines.*',
                    'class_levels.name as class_name',
                    'subjects.name as subject_name',
                    'chapters.name as chapter_name',
                    'chapter_videos.title as video_title',
                    'chapter_videos.title_bn as video_title_bn',
                    'chapter_videos.author_name as video_author_name',
                    'chapter_videos.author_details as video_author_details',
                    'chapter_videos.raw_url as video_raw_url',
                    'chapter_videos.s3_url as video_s3_url',
                    'chapter_videos.youtube_url as video_youtube_url',
                    'chapter_videos.download_url as video_download_url',
                    'chapter_videos.duration as video_duration',
                    'chapter_videos.thumbnail as video_thumbnail',
                    'chapter_videos.is_free as video_is_free',
                    'chapter_scripts.title as script_title',
                    'chapter_scripts.title_bn as script_title_bn',
                    'chapter_scripts.raw_url as script_raw_url',
                    'chapter_scripts.is_free as script_is_free',
                    'chapter_quizzes.title as quiz_title',
                    'chapter_quizzes.title_bn as quiz_title_bn',
                    'chapter_quizzes.duration as quiz_duration',
                    'chapter_quizzes.is_free as quiz_is_free',
                )
                    ->where('course_outlines.course_id', $course->id)
                    ->leftJoin('class_levels', 'class_levels.id', 'course_outlines.class_level_id')
                    ->leftJoin('subjects', 'subjects.id', 'course_outlines.subject_id')
                    ->leftJoin('chapters', 'chapters.id', 'course_outlines.chapter_id')
                    ->leftJoin('chapter_videos', 'chapter_videos.id', 'course_outlines.chapter_video_id')
                    ->leftJoin('chapter_scripts', 'chapter_scripts.id', 'course_outlines.chapter_script_id')
                    ->leftJoin('chapter_quizzes', 'chapter_quizzes.id', 'course_outlines.chapter_quiz_id')
                    ->get();

                $course->course_routine = CourseClassRoutine::where('course_id', $course->id)->get();
                $course->course_feature = CourseFeature::where('course_id', $course->id)->get();
                $course->course_mentor = CourseMentor::select('course_mentors.*', 'mentor_informations.name', 'mentor_informations.education', 'mentor_informations.institute')
                    ->where('course_mentors.course_id', $course->id)
                    ->leftJoin('mentor_informations', 'mentor_informations.id', 'course_mentors.mentor_id')
                    ->get();
                $course->course_faq = CourseFaq::where('course_id', $course->id)->get();
            }
        }

        if ($menu->is_content) {
            $content_list = Content::where('category_id', $menu->id)->get();
            $menu->contents = $content_list;

            foreach ($content_list as $content) {
                $content->content_outline = ContentOutline::select(
                    'content_outlines.*',
                    'class_levels.name as class_name',
                    'subjects.name as subject_name',
                    'chapters.name as chapter_name'
                )
                    ->where('content_outlines.content_id', $content->id)
                    ->leftJoin('class_levels', 'class_levels.id', 'content_outlines.class_level_id')
                    ->leftJoin('subjects', 'subjects.id', 'content_outlines.subject_id')
                    ->leftJoin('chapters', 'chapters.id', 'content_outlines.chapter_id')
                    ->get();
            }
        }

        return response()->json([
            'status' => true,
            'message' => 'Successful',
            'data' => $menu,
        ], 200);
    }

    public function courseDetailsByUserID(Request $request)
    {
        try {
            $authId = $request->user->id ?? null;
            $courseId = $request->course_id ?: 0;

            // Check if the user is a participant in the course
            $isExist = CourseParticipant::where('item_id', $courseId)
                ->where('user_id', $authId)
                ->where('item_type', 'Course')
                ->exists();

            // Fetch the course details along with related data
            $courses = Course::where('id', $courseId)
                ->with([
                    'courseCategory' => function ($query) {
                        $query->where('is_active', true) // Filter active categories
                            ->orderBy('sequence', 'asc');
                    },
                    'courseCategory.courseOutlines' => function ($query) {
                        $query->where('is_active', true); // Filter active outlines
                    },
                    'courseRoutine' => function ($query) {
                        $query->where('is_active', true); // Filter active routines
                    },
                    'courseFeature' => function ($query) {
                        $query->where('is_active', true); // Filter active features
                    },
                    'courseMentor' => function ($query) {
                        $query->where('is_active', true) // Filter active mentors
                            ->with(['mentor:id,name,education,institute,image']);
                    },
                    'courseFaq',
                ])
                ->withCount('courseCategory as module_count')
                ->first();

            // Check if the course was found
            if (! $courses) {
                return $this->apiResponse([], 'Course not found', false, 404);
            }

            // Set the purchased status for the course
            $courses->is_purchased = $isExist;

            return $this->apiResponse($courses, 'Course Details', true, 200);
        } catch (\Throwable $th) {
            return $this->apiResponse([], $th->getMessage(), false, 500);
        }
    }



    public function quizStartDetails(Request $request, $id)
    {
        try {
            $quiz = ChapterQuiz::where('id', $id)->first();

            return $this->apiResponse($quiz, 'Quiz Details', true, 200);
        } catch (\Throwable $th) {
            return $this->apiResponse($th->getMessage(), 'Something went wrong', false, 400);
        }
    }

    public function chapterQuizDetails(Request $request, $quiz_id = null)
    {
        try {
            // Check if quiz_id is passed as a route parameter or as a query parameter
            $chapter_quiz_id = $quiz_id ?? $request->quiz_id ?? 0;

            if (! $chapter_quiz_id) {
                return $this->apiResponse(null, 'Please, attach Quiz ID', false, 422);
            }

            if ($chapter_quiz_id) {
                $outline = CourseOutline::where('chapter_quiz_id', $chapter_quiz_id)->first();

                if (!$outline) {
                    return $this->apiResponse(null, 'Quiz outline not found', false, 404);
                }

                $quiz_details = ChapterQuiz::where('chapter_quizzes.id', $chapter_quiz_id)
                    ->select('chapter_quizzes.*')
                    ->with('writtenQuestions')
                    ->first();

                if (!$quiz_details) {
                    return $this->apiResponse(null, 'Quiz not found', false, 404);
                }

                // $quiz_details->written_question = null;
                $user = auth('sanctum')->user();

                if ($outline->is_free == false && !$user) {
                    return $this->apiResponse(null, 'Please login first', false, 401);
                }
                $items = ChapterQuizItem::where('chapter_quiz_id', $chapter_quiz_id)
                ->with([
                    'chapterQuizQuestion',
                    'trueFalse',
                    'matching',
                    'matching.matchingAnswers',
                    'fillInTheBlank',
                    'fillInTheBlank.blankAnswers:id,fill_in_the_blank_question_id,blank_key'
                ])
                ->get();

                // Process matching items to add left and right items
                foreach ($items as $item) {
                    if ($item->type === 'matching' && $item->matching) {
                        // Create a collection of matching answers without right_item
                        $matchingAnswersWithoutRightItem = $item->matching->matchingAnswers->map(function ($answer) {
                            $answerArray = $answer->toArray();
                            unset($answerArray['right_item']);
                            return $answerArray;
                        });

                        // Store the random right items separately
                        $item->matching->random_right_items = $item->matching->matchingAnswers->pluck('right_item')->shuffle();

                        // Replace the original matchingAnswers with the version without right_item
                        $item->matching->setRelation('matchingAnswers', collect($matchingAnswersWithoutRightItem));
                    }
                }
                // $questions = ChapterQuizQuestion::inRandomOrder()
                // ->where('chapter_quiz_id', $chapter_quiz_id)

                // ->limit($quiz_details->number_of_question)
                // ->get();


                $quiz_details->items = $items;
            }

            return $this->apiResponse($quiz_details, 'Quiz Details', true, 200);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Something went wrong', 500);
        }
    }

    public function startQuiz(Request $request)
    {
        $user_id = Auth::id();
        $course_id = $request->course_id ? $request->course_id : 0;
        $chapter_quiz_id = $request->chapter_quiz_id ? $request->chapter_quiz_id : 0;



        if (! $course_id || ! $chapter_quiz_id) {
            return response()->json([
                'status' => false,
                'message' => 'Please, attach ID',
                'data' => [],
            ], 422);
        }

        $quiz = ChapterQuiz::where('id', $chapter_quiz_id)->first();
        $quizAttempts = ChapterQuizResult::where('user_id', $user_id)->where('chapter_quiz_id', $chapter_quiz_id)->count();

        if ($quiz->quiz_attempts <= $quizAttempts) {
            return response()->json([
                'status' => false,
                'message' => 'You have already exceeded the quiz attempts limit!',
                'data' => [],
            ], 422);
        }



        $result = ChapterQuizResult::create([
            'user_id' => $user_id,
            'chapter_quiz_id' => $chapter_quiz_id,
            'course_id' => $course_id,
            'mark' => 0,
        ]);

        return response()->json([
            'status' => true,
            'message' => 'Result Details',
            'data' => $result,
        ], 200);
    }

    public function submitQuizAnswer(Request $request)
    {
        try {
            $user_id = Auth::id();
            $result_id = $request->result_id ? $request->result_id : 0;
            $chapter_quiz_id = $request->chapter_quiz_id ? $request->chapter_quiz_id : 0;
            $answers = $request->answers ? $request->answers : [];

            $result = ChapterQuizResult::where('id', $result_id)->first();
            if ($result->submission_status == 'Submitted') {
                return response()->json([
                    'status' => false,
                    'message' => 'You have already submitted the quiz!',
                    'data' => [],
                ], 422);
            }

            if (empty($answers)) {
                return response()->json([
                    'status' => false,
                    'message' => 'Please, attach Answer!',
                    'data' => [],
                ], 422);
            }

            if (! $result_id) {
                return response()->json([
                    'status' => false,
                    'message' => 'Please, Start Exam properly!',
                    'data' => [],
                ], 422);
            }

            $positiveCount = 0;
            $negativeCount = 0;
            $totalMarks = 0;
            $obtainedMarks = 0;

            $quiz_details = ChapterQuiz::where('id', $chapter_quiz_id)->first();

            if (empty($quiz_details)) {
                return response()->json([
                    'status' => false,
                    'message' => 'Quiz Not found!',
                    'data' => [],
                ], 422);
            }

            $subject_list = ChapterQuizSubject::where('chapter_quiz_id', $chapter_quiz_id)->get();

            foreach ($subject_list as $subject) {
                $subject->positive_count = 0;
                $subject->negative_count = 0;
            }

            foreach ($answers as $ans) {
                // Check the type of question
                $questionType = $ans['type'] ?? 'mcq';
                $is_correct = false;
                $mark_obtained = 0;

                switch ($questionType) {
                    case 'mcq':
                        // Process MCQ type questions
                        $question = ChapterQuizQuestion::where('id', $ans['question_id'])->select(
                            'id',
                            'chapter_quiz_id',
                            'question_set_id',
                            'chapter_quiz_subject_id',
                            'answer1',
                            'answer2',
                            'answer3',
                            'answer4',
                        )->first();

                        if ($question && ($ans['answer1'] == true || $ans['answer2'] == true || $ans['answer3'] == true || $ans['answer4'] == true)) {
                            $given_answer1 = $ans['answer1'] ? $ans['answer1'] : false;
                            $given_answer2 = $ans['answer2'] ? $ans['answer2'] : false;
                            $given_answer3 = $ans['answer3'] ? $ans['answer3'] : false;
                            $given_answer4 = $ans['answer4'] ? $ans['answer4'] : false;

                            if (
                                $given_answer1 == $question->answer1
                                && $given_answer2 == $question->answer2
                                && $given_answer3 == $question->answer3
                                && $given_answer4 == $question->answer4
                            ) {
                                $positiveCount++;
                                $is_correct = true;
                                $mark_obtained = $quiz_details->positive_mark;
                            } else {
                                $negativeCount++;
                                $mark_obtained = -$quiz_details->negative_mark;
                            }

                            // Create ChapterQuizResultAnswer for MCQ
                            ChapterQuizResultAnswer::insert([
                                'chapter_quiz_result_id' => $result_id,
                                'question_id' => $ans['question_id'],
                                'answer1' => $ans['answer1'] ? $ans['answer1'] : 0,
                                'answer2' => $ans['answer2'] ? $ans['answer2'] : 0,
                                'answer3' => $ans['answer3'] ? $ans['answer3'] : 0,
                                'answer4' => $ans['answer4'] ? $ans['answer4'] : 0,
                                'is_correct' => $is_correct,
                            ]);

                            // Find the ChapterQuizItem for this question
                            $quizItem = ChapterQuizItem::where('chapter_quiz_question_id', $question->id)
                                ->where('type', 'mcq')
                                ->first();

                            if ($quizItem) {
                                // Create ChapterQuizResultItem
                                $resultItem = ChapterQuizResultItem::create([
                                    'chapter_quiz_result_id' => $result_id,
                                    'chapter_quiz_item_id' => $quizItem->id,
                                    'chapter_quiz_question_id' => $question->id,
                                    'type' => 'mcq',
                                    'is_correct' => $is_correct,
                                    'mark_obtained' => $mark_obtained
                                ]);
                            }
                        }
                        break;

                    case 'true_false':
                        // Process True/False type questions
                        $trueFalseId = $ans['true_false_id'] ?? null;
                        if ($trueFalseId) {
                            $trueFalseQuestion = ChapterTrueFalseQuestion::find($trueFalseId);

                            if ($trueFalseQuestion) {
                                $userAnswer = $ans['answer'] ?? false;
                                $is_correct = ($userAnswer == $trueFalseQuestion->answer);

                                if ($is_correct) {
                                    $positiveCount++;
                                    $mark_obtained = $quiz_details->positive_mark;
                                } else {
                                    $negativeCount++;
                                    $mark_obtained = -$quiz_details->negative_mark;
                                }

                                // Find the ChapterQuizItem for this question
                                $quizItem = ChapterQuizItem::where('chapter_quiz_true_false_id', $trueFalseId)
                                    ->where('type', 'true_false')
                                    ->first();

                                if ($quizItem) {
                                    // Create ChapterQuizResultItem
                                    $resultItem = ChapterQuizResultItem::create([
                                        'chapter_quiz_result_id' => $result_id,
                                        'chapter_quiz_item_id' => $quizItem->id,
                                        'chapter_quiz_true_false_id' => $trueFalseId,
                                        'type' => 'true_false',
                                        'answer' => $userAnswer ? 'true' : 'false',
                                        'is_correct' => $is_correct,
                                        'mark_obtained' => $mark_obtained
                                    ]);

                                    // Create ChapterQuizResultTrueFalse
                                    ChapterQuizResultTrueFalse::create([
                                        'chapter_quiz_result_item_id' => $resultItem->id,
                                        'chapter_quiz_true_false_id' => $trueFalseId,
                                        'answer' => $userAnswer,
                                        'is_correct' => $is_correct
                                    ]);
                                }
                            }
                        }
                        break;

                    case 'matching':
                        // Process Matching type questions
                        $matchingId = $ans['matching_id'] ?? null;
                        $matches = $ans['matches'] ?? [];

                        if ($matchingId && !empty($matches)) {
                            // Find the ChapterQuizItem for this question
                            $quizItem = ChapterQuizItem::where('chapter_quiz_matching_id', $matchingId)
                                ->where('type', 'matching')
                                ->first();

                            if ($quizItem) {
                                $totalMatches = count($matches);
                                $correctMatches = 0;

                                // Create a single ChapterQuizResultItem for the entire matching question first
                                $resultItem = ChapterQuizResultItem::create([
                                    'chapter_quiz_result_id' => $result_id,
                                    'chapter_quiz_item_id' => $quizItem->id,
                                    'chapter_quiz_matching_id' => $matchingId,
                                    'type' => 'matching',
                                    'answer' => json_encode($matches), // Store all matches as JSON
                                    'is_correct' => false, // Will update after processing all matches
                                    'mark_obtained' => 0 // Will update after processing all matches
                                ]);

                                // Process each match in the matches array
                                foreach ($matches as $match) {
                                    $matchingAnswerId = $match['matching_answer_id'] ?? null;
                                    $selectedRightItem = $match['selected_right_item'] ?? '';

                                    if ($matchingAnswerId) {
                                        $matchingAnswer = MatchingAnswer::find($matchingAnswerId);

                                        if ($matchingAnswer) {
                                            $is_correct = ($selectedRightItem == $matchingAnswer->right_item);

                                            if ($is_correct) {
                                                $correctMatches++;
                                            }

                                            // Create ChapterQuizResultMatching for each match
                                            ChapterQuizResultMatching::create([
                                                'chapter_quiz_result_item_id' => $resultItem->id,
                                                'chapter_quiz_matching_id' => $matchingId,
                                                'matching_answer_id' => $matchingAnswerId,
                                                'selected_right_item' => $selectedRightItem,
                                                'is_correct' => $is_correct
                                            ]);
                                        }
                                    }
                                }

                                // Calculate overall correctness for the matching question
                                $overallIsCorrect = ($correctMatches == $totalMatches);

                                // Calculate mark obtained
                                if ($overallIsCorrect) {
                                    $positiveCount++;
                                    $mark_obtained = $quiz_details->positive_mark;
                                } else {
                                    $negativeCount++;
                                    $mark_obtained = -$quiz_details->negative_mark;
                                }

                                // Update the result item with the final correctness and mark
                                $resultItem->update([
                                    'is_correct' => $overallIsCorrect,
                                    'mark_obtained' => $mark_obtained
                                ]);
                            }
                        }
                        break;

                    case 'fill_in_blank':
                        // Process Fill in the Blank type questions
                        $fillInBlankId = $ans['fill_in_blank_id'] ?? null;
                        $blankAnswers = $ans['blank_answers'] ?? [];

                        if ($fillInBlankId && !empty($blankAnswers)) {
                            // Find the ChapterQuizItem for this question
                            $quizItem = ChapterQuizItem::where('chapter_quiz_fill_in_blank_id', $fillInBlankId)
                                ->where('type', 'fill_in_blank')
                                ->first();

                            if ($quizItem) {
                                $totalBlanks = count($blankAnswers);
                                $correctBlanks = 0;

                                // Create ChapterQuizResultItem first
                                $resultItem = ChapterQuizResultItem::create([
                                    'chapter_quiz_result_id' => $result_id,
                                    'chapter_quiz_item_id' => $quizItem->id,
                                    'chapter_quiz_fill_in_blank_id' => $fillInBlankId,
                                    'type' => 'fill_in_blank',
                                    'answer' => json_encode($blankAnswers), // Store all blank answers as JSON
                                    'is_correct' => false, // Will update after processing all blanks
                                    'mark_obtained' => 0 // Will update after processing all blanks
                                ]);

                                // Process each blank answer
                                foreach ($blankAnswers as $blankAnswer) {
                                    $blankAnswerId = $blankAnswer['blank_answer_id'] ?? null;
                                    $userAnswer = $blankAnswer['user_answer'] ?? '';

                                    if ($blankAnswerId) {
                                        $dbBlankAnswer = BlankAnswer::find($blankAnswerId);

                                        if ($dbBlankAnswer) {
                                            $is_correct = (strtolower(trim($userAnswer)) == strtolower(trim($dbBlankAnswer->blank_answer)));

                                            if ($is_correct) {
                                                $correctBlanks++;
                                            }

                                            // Create ChapterQuizResultFillInTheBlank for each blank
                                            ChapterQuizResultFillInTheBlank::create([
                                                'chapter_quiz_result_item_id' => $resultItem->id,
                                                'chapter_quiz_fill_in_blank_id' => $fillInBlankId,
                                                'blank_answer_id' => $blankAnswerId,
                                                'user_answer' => $userAnswer,
                                                'is_correct' => $is_correct
                                            ]);
                                        }
                                    }
                                }

                                // Calculate overall correctness for the fill in blank question
                                $overallIsCorrect = ($correctBlanks == $totalBlanks);

                                // Calculate mark obtained
                                if ($overallIsCorrect) {
                                    $positiveCount++;
                                    $mark_obtained = $quiz_details->positive_mark;
                                } else {
                                    $negativeCount++;
                                    $mark_obtained = -$quiz_details->negative_mark;
                                }

                                // Update the result item with the final correctness and mark
                                $resultItem->update([
                                    'is_correct' => $overallIsCorrect,
                                    'mark_obtained' => $mark_obtained
                                ]);
                            }
                        }
                        break;
                }
            }

            $mark = $positiveCount * $quiz_details->positive_mark - $negativeCount * $quiz_details->negative_mark;

            ChapterQuizResult::where('id', $result_id)->update([
                'mark' => $mark,
                'positive_count' => $positiveCount,
                'negative_count' => $negativeCount,
                'submission_status' => 'Submitted',
                'submitted_at' => now()
            ]);

            // Update CourseStudent quiz participation count
            $this->updateCourseStudentQuizCount($user_id, $quiz_details->course_id);

            return response()->json([
                'status' => true,
                'message' => 'Quiz Submitted Successful!',
                'data' => [],
            ], 200);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'something went wrong', 500);
        }
    }
    
    public function submitWrittenAnswer(Request $request)
    {
        try {
            $user_id = Auth::id();
            $formData = json_decode($request->data, true);
            $result_id = $request->result_id ?: 0;
            $chapter_quiz_id = $request->quiz_id ?: 0;

            // Validation
            if (!$request->hasFile('attachment_files')) {
                return $this->errorResponse([], 'Please, attach Answer!', 422);
            }

            if (!$result_id) {
                return $this->errorResponse([], 'Please, Start Exam properly!', 422);
            }

            // Upload files using trait method
            $uploadedFiles = $this->uploadMultipleFiles(
                $request->file('attachment_files'),
                'written_answer',
                true, // Use S3 if configured
                'written_answer_' . $chapter_quiz_id
            );

            // Save attachments
            foreach ($uploadedFiles as $filePath) {
                ChapterQuizWrittenAttachment::create([
                    'chapter_quiz_result_id' => $result_id,
                    'chapter_quiz_id' => $chapter_quiz_id,
                    'user_id' => $user_id,
                    'attachment_url' => $filePath,
                ]);
            }

            // Create marks for each question
            $written = ChapterQuizWrittenQuestion::where('chapter_quiz_id', $chapter_quiz_id)->first();
            if ($written) {
                for ($i = 1; $i <= $written->no_of_question; $i++) {
                    ChapterQuizWrittenMark::create([
                        'chapter_quiz_result_id' => $result_id,
                        'chapter_quiz_id' => $chapter_quiz_id,
                        'user_id' => $user_id,
                        'question_no' => $i,
                        'mark' => 0.00,
                        'marks_givenby_id' => 0,
                    ]);
                }
            }

            return $this->successResponse([], 'Exam Submitted Successfully!');
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Something went wrong!', 500);
        }
    }

    public function submitWrittenAnswerMobile(Request $request)
    {
        try {
            $user_id = Auth::id();
            $formData = json_decode($request->data, true);
            $result_id = $formData['result_id'] ?: 0;
            $chapter_quiz_id = $formData['chapter_quiz_id'] ?: 0;
            $attach_count = $formData['attach_count'] ?: 0;

            // Validation
            if (!$attach_count) {
                return $this->errorResponse([], 'Please, attach Answer!', 422);
            }

            if (!$result_id) {
                return $this->errorResponse([], 'Please, Start Exam properly!', 422);
            }

            // Upload files using trait method
            $uploadedFiles = $this->uploadMultipleFiles(
                $request->file('files'),
                'written_answer',
                true, // Use S3 if configured
                'wa_' . $chapter_quiz_id
            );

            // Save attachments
            foreach ($uploadedFiles as $filePath) {
                ChapterQuizWrittenAttachment::create([
                    'chapter_quiz_result_id' => $result_id,
                    'chapter_quiz_id' => $chapter_quiz_id,
                    'user_id' => $user_id,
                    'attachment_url' => $filePath,
                ]);
            }

            // Create marks for each question
            $written = ChapterQuizWrittenQuestion::where('chapter_quiz_id', $chapter_quiz_id)->first();
            if ($written) {
                for ($i = 1; $i <= $written->no_of_question; $i++) {
                    ChapterQuizWrittenMark::create([
                        'chapter_quiz_result_id' => $result_id,
                        'chapter_quiz_id' => $chapter_quiz_id,
                        'user_id' => $user_id,
                        'question_no' => $i,
                        'mark' => 0.00,
                        'marks_givenby_id' => 0,
                    ]);
                }
            }

            return $this->successResponse([], 'Quiz Submitted Successfully!');
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Something went wrong!', 500);
        }
    }

    public function quizAnswerList(Request $request)
    {
        $user_id = Auth::id();

        $answer_list = ChapterQuizResult::forOrganization('chapter_quiz_results')
            ->select(
                'chapter_quiz_results.*',
                'chapter_quizzes.title',
                'chapter_quizzes.title_bn',
                'chapter_quizzes.duration',
                'chapter_quizzes.positive_mark',
                'chapter_quizzes.negative_mark',
                'chapter_quizzes.total_mark as exam_mark',
                'chapter_quizzes.number_of_question',
                'courses.title as course_title',
                'chapters.name as chapter_name',
            )
            ->leftJoin('chapter_quizzes', 'chapter_quizzes.id', 'chapter_quiz_results.chapter_quiz_id')
            ->leftJoin('class_levels', 'class_levels.id', 'chapter_quizzes.class_level_id')
            ->leftJoin('courses', 'courses.id', 'chapter_quizzes.course_id')
            ->leftJoin('chapters', 'chapters.id', 'chapter_quizzes.chapter_id')
            ->where('chapter_quiz_results.user_id', $user_id)
            ->orderBy('chapter_quiz_results.id', 'DESC')
            ->get();

        return response()->json([
            'status' => true,
            'message' => 'Quiz List Successful!',
            'data' => $answer_list,
        ], 200);
    }

    public function quizAnswerDetails(Request $request, $result_id = null)
    {
        $user_id = Auth::id();
        $result_id = $result_id ?? $request->result_id ?? 0;

        $answer = ChapterQuizResult::select(
            'chapter_quiz_results.*',
            'chapter_quizzes.title',
            'chapter_quizzes.title_bn',
            'chapter_quizzes.duration',
            'chapter_quizzes.positive_mark',
            'chapter_quizzes.negative_mark',
            'chapter_quizzes.total_mark as exam_mark',
            'chapter_quizzes.number_of_question',
            'class_levels.name as class_name',
            'subjects.name as subject_name',
            // 'chapters.name as chapter_name',
            'student_informations.name as student_name',
            'student_informations.image as student_image',
            'courses.title as course_title'
        )
            ->leftJoin('student_informations', 'student_informations.user_id', 'chapter_quiz_results.user_id')
            ->leftJoin('chapter_quizzes', 'chapter_quizzes.id', 'chapter_quiz_results.chapter_quiz_id')
            ->leftJoin('class_levels', 'class_levels.id', 'chapter_quizzes.class_level_id')
            ->leftJoin('subjects', 'subjects.id', 'chapter_quizzes.subject_id')
            ->leftJoin('courses', 'courses.id', 'chapter_quizzes.course_id')
            ->where('chapter_quiz_results.id', $result_id)
            ->orderBy('chapter_quiz_results.id', 'DESC')
            ->first();

        if (!$answer) {
            return $this->apiResponse(null, 'Quiz result not found', false, 404);
        }

        // Get MCQ questions and answers
        $answer->mcq_questions = ChapterQuizResultAnswer::select(
            'chapter_quiz_result_answers.*',
            'chapter_quiz_questions.question_text',
            'chapter_quiz_questions.question_image',
            'chapter_quiz_questions.question_text_bn',
            'chapter_quiz_questions.option1',
            'chapter_quiz_questions.option2',
            'chapter_quiz_questions.option3',
            'chapter_quiz_questions.option4',
            'chapter_quiz_questions.option1_image',
            'chapter_quiz_questions.option2_image',
            'chapter_quiz_questions.option3_image',
            'chapter_quiz_questions.option4_image',
            'chapter_quiz_questions.answer1 as correct_answer1',
            'chapter_quiz_questions.answer2 as correct_answer2',
            'chapter_quiz_questions.answer3 as correct_answer3',
            'chapter_quiz_questions.answer4 as correct_answer4',
            'chapter_quiz_questions.explanation_text',
            'chapter_quiz_questions.explanation_image',
        )
            ->leftJoin('chapter_quiz_questions', 'chapter_quiz_questions.id', 'chapter_quiz_result_answers.question_id')
            ->where('chapter_quiz_result_answers.chapter_quiz_result_id', $result_id)
            ->get();

        // Get all result items
        $resultItems = ChapterQuizResultItem::where('chapter_quiz_result_id', $result_id)->get();

        // Get True/False questions and answers
        $trueFalseItems = $resultItems->where('type', 'true_false');
        $trueFalseQuestions = [];

        foreach ($trueFalseItems as $item) {
            $trueFalseResult = ChapterQuizResultTrueFalse::where('chapter_quiz_result_item_id', $item->id)->first();

            if ($trueFalseResult) {
                $trueFalseQuestion = ChapterTrueFalseQuestion::find($trueFalseResult->chapter_quiz_true_false_id);

                if ($trueFalseQuestion) {
                    $trueFalseQuestions[] = [
                        'id' => $trueFalseQuestion->id,
                        'question_text' => $trueFalseQuestion->question_text,
                        'question_image' => $trueFalseQuestion->question_image,
                        'correct_answer' => $trueFalseQuestion->answer,
                        'user_answer' => $trueFalseResult->answer,
                        'is_correct' => $trueFalseResult->is_correct,
                        'explanation_text' => $trueFalseQuestion->explanation_text,
                        'explanation_image' => $trueFalseQuestion->explanation_image,
                    ];
                }
            }
        }

        // Get Fill in the Blank questions and answers
        $fillInBlankItems = $resultItems->where('type', 'fill_in_blank');
        $fillInBlankQuestions = [];

        foreach ($fillInBlankItems as $item) {
            $fillInBlankResult = ChapterQuizResultFillInTheBlank::where('chapter_quiz_result_item_id', $item->id)->first();

            if ($fillInBlankResult) {
                $fillInBlankQuestion = FillInTheBlankQuestion::with('blankAnswers')
                    ->find($fillInBlankResult->chapter_quiz_fill_in_blank_id);

                if ($fillInBlankQuestion) {
                    // Get all blank answers for this question
                    $blankAnswers = BlankAnswer::where('fill_in_the_blank_question_id', $fillInBlankQuestion->id)->get();

                    // Get all user answers for this question
                    $userAnswers = ChapterQuizResultFillInTheBlank::where('chapter_quiz_fill_in_blank_id', $fillInBlankQuestion->id)
                        ->where('chapter_quiz_result_item_id', $item->id)
                        ->get();

                    // Create an array of answers
                    $answers = [];
                    foreach ($blankAnswers as $blankAnswer) {
                        // Find the user's answer for this blank
                        $userAnswer = $userAnswers->where('blank_answer_id', $blankAnswer->id)->first();

                        $answers[] = [
                            'blank_id' => $blankAnswer->id,
                            'blank_key' => $blankAnswer->blank_key,
                            'correct_answer' => $blankAnswer->blank_answer,
                            'user_answer' => $userAnswer ? $userAnswer->user_answer : '',
                            'is_correct' => $userAnswer ? $userAnswer->is_correct : false,
                        ];
                    }

                    $fillInBlankQuestions[] = [
                        'id' => $fillInBlankQuestion->id,
                        'question_text' => $fillInBlankQuestion->question_text,
                        'question_image' => $fillInBlankQuestion->question_image,
                        'is_correct' => $item->is_correct,
                        'answers' => $answers,
                        'explanation_text' => $fillInBlankQuestion->explanation_text,
                        'explanation_image' => $fillInBlankQuestion->explanation_image,
                    ];
                }
            }
        }

        // Get Matching questions and answers
        $matchingItems = $resultItems->where('type', 'matching');
        $matchingQuestions = [];

        foreach ($matchingItems as $item) {
            $matchingQuestion = MatchingQuestion::with('matchingAnswers')
                ->find($item->chapter_quiz_matching_id);

            if ($matchingQuestion) {
                $matchingResults = ChapterQuizResultMatching::where('chapter_quiz_result_item_id', $item->id)->get();
                $matches = [];

                foreach ($matchingResults as $matchResult) {
                    $matchingAnswer = MatchingAnswer::find($matchResult->matching_answer_id);

                    if ($matchingAnswer) {
                        $matches[] = [
                            'matching_answer_id' => $matchingAnswer->id,
                            'left_item' => $matchingAnswer->left_item,
                            'correct_right_item' => $matchingAnswer->right_item,
                            'selected_right_item' => $matchResult->selected_right_item,
                            'is_correct' => $matchResult->is_correct,
                        ];
                    }
                }

                $matchingQuestions[] = [
                    'id' => $matchingQuestion->id,
                    'question_text' => $matchingQuestion->question_text,
                    'is_correct' => $item->is_correct,
                    'matches' => $matches,
                    'explanation_text' => $matchingQuestion->explanation_text,
                    'explanation_image' => $matchingQuestion->explanation_image,
                ];
            }
        }

        // Get written questions and answers
        $answer->written_question = ChapterQuizWrittenQuestion::where('chapter_quiz_id', $answer->chapter_quiz_id)->first();
        $answer->written_answers = ChapterQuizWrittenAttachment::where('chapter_quiz_result_id', $result_id)->get();
        $writtenResult = ChapterQuizWrittenMark::where('chapter_quiz_result_id', $result_id)->first();
        $answer->written_result = $writtenResult;

        // Add all question types to the response
        $responseData = $answer->toArray();
        $responseData['true_false_questions'] = $trueFalseQuestions;
        $responseData['fill_in_blank_questions'] = $fillInBlankQuestions;
        $responseData['matching_questions'] = $matchingQuestions;

        return $this->apiResponse($responseData, 'Answer Successful!', true, 200);
    }

    public function quizSubjectWiseAnswerDetails(Request $request)
    {
        $result_id = $request->result_id ? $request->result_id : 0;

        $answer = ChapterQuizSubjectWiseResult::select(
            'chapter_quiz_subject_wise_results.*',
            'quiz_core_subjects.name',
            'quiz_core_subjects.name_bn'
        )
            ->leftJoin('quiz_core_subjects', 'quiz_core_subjects.id', 'chapter_quiz_subject_wise_results.quiz_core_subject_id')
            ->where('chapter_quiz_subject_wise_results.chapter_quiz_result_id', $result_id)
            ->orderBy('quiz_core_subjects.name', 'ASC')
            ->get();

        return response()->json([
            'status' => true,
            'message' => 'Answer Details Successful!',
            'data' => $answer,
        ], 200);
    }

    public function mentorStudentList(Request $request)
    {
        try {
            $user_id = Auth::id();
            $mentor = MentorInformation::forOrganization('mentor_informations')
                ->where('user_id', $user_id)->first();

            $student = CourseStudentMapping::forOrganization('course_student_mappings')
                ->select(
                    'course_student_mappings.student_id',
                    'course_student_mappings.id as mapping_id',
                    'courses.title as course_title',
                    'mentor_informations.name as mentor_name',
                    'student_informations.name as student_name',
                    'student_informations.contact_no as student_contact_no'
                )
                ->where('course_student_mappings.mentor_id', $mentor->id)
                ->leftJoin('courses', 'courses.id', 'course_student_mappings.course_id')
                ->leftJoin('mentor_informations', 'mentor_informations.id', 'course_student_mappings.mentor_id')
                ->leftJoin('student_informations', 'student_informations.id', 'course_student_mappings.student_id')
                ->get();

            return $this->apiResponse($student, 'Student List', true, 200);
        } catch (\Throwable $th) {
            return $this->apiResponse([], $th->getMessage(), false, 500);
        }
    }

    public function mentorStudentListByCourse(Request $request)
    {
        try {
            $user_id = Auth::id();

            $course_id = $request->course_id ? $request->course_id : 0;

            if (! $course_id) {
                return response()->json([
                    'status' => false,
                    'message' => 'Please, attach ID',
                    'data' => [],
                ], 422);
            }

            $mentor = MentorInformation::forOrganization('mentor_informations')->where('user_id', $user_id)->first();

            $student = CourseStudentMapping::forOrganization('course_student_mappings')
                ->select(
                    'course_student_mappings.student_id',
                    'course_student_mappings.id as mapping_id',
                    'courses.title as course_title',
                    'mentor_informations.name as mentor_name',
                    'student_informations.name as student_name',
                    'student_informations.contact_no as student_contact_no'
                )
                ->where('course_student_mappings.mentor_id', $mentor->id)
                ->where('course_student_mappings.course_id', $course_id)
                ->leftJoin('courses', 'courses.id', 'course_student_mappings.course_id')
                ->leftJoin('mentor_informations', 'mentor_informations.id', 'course_student_mappings.mentor_id')
                ->leftJoin('student_informations', 'student_informations.id', 'course_student_mappings.student_id')
                ->get();

            return response()->json([
                'status' => true,
                'message' => 'Successful',
                'data' => $student,
            ], 200);
        } catch (\Throwable $th) {
            return $this->apiResponse([], $th->getMessage(), false, 500);
        }
    }

    public function mentorClassScheduleList(Request $request)
    {
        $mapping_id = $request->mapping_id ? $request->mapping_id : 0;

        if (! $mapping_id) {
            return response()->json([
                'status' => false,
                'message' => 'Please, attach Class ID',
                'data' => [],
            ], 422);
        }

        $class = ClassSchedule::forOrganization('class_schedules')
            ->select(
                'class_schedules.*',
                'courses.title as course_title',
                'mentor_informations.name as mentor_name',
                'student_informations.name as student_name',
                'student_informations.contact_no as student_contact_no'
            )
            ->where('class_schedules.course_student_mapping_id', $mapping_id)
            ->leftJoin('courses', 'courses.id', 'class_schedules.course_id')
            ->leftJoin('mentor_informations', 'mentor_informations.id', 'class_schedules.mentor_id')
            ->leftJoin('student_informations', 'student_informations.id', 'class_schedules.student_id')
            ->orderBy('class_schedules.schedule_datetime', 'DESC')
            ->get();

        foreach ($class as $item) {

            if ($item->start_time) {
                $item->start_time = $this->addHour($item->start_time, 6);
            }

            if ($item->end_time) {
                $item->end_time = $this->addHour($item->end_time, 6);
            }

            $isToday = date('Ymd') == date('Ymd', strtotime($item->schedule_datetime));

            $zoomLink = MentorZoomLink::forOrganization('mentor_zoom_links')->where('mentor_id', $item->mentor_id)->first();

            if ($isToday) {
                $item->can_join = true;
                $item->join_link = $zoomLink->live_link;
            } else {
                $item->can_join = false;
                $item->join_link = null;
            }

            $item->has_passed = false;
            if (time() > strtotime($item->schedule_datetime)) {
                $item->has_passed = true;
            }
        }

        return response()->json([
            'status' => true,
            'message' => 'Successful',
            'data' => $class,
        ], 200);
    }

    public function addClassSchedule(Request $request)
    {
        $mapping_id = $request->mapping_id ? $request->mapping_id : 0;
        $schedule_date = $request->schedule_date ? $request->schedule_date : 0;

        if (! $mapping_id) {
            return response()->json([
                'status' => false,
                'message' => 'Please, attach Class ID',
                'data' => [],
            ], 422);
        }

        $mapping_details = CourseStudentMapping::where('id', $mapping_id)->first();

        ClassSchedule::create([
            'course_student_mapping_id' => $mapping_id,
            'course_id' => $mapping_details->course_id,
            'student_id' => $mapping_details->student_id,
            'mentor_id' => $mapping_details->mentor_id,
            'schedule_datetime' => $schedule_date,
            'has_started' => false,
            'has_completed' => false,
            'start_time' => null,
            'end_time' => null,
            'is_active' => true,
        ]);

        return response()->json([
            'status' => true,
            'message' => 'Class added successful!',
            'data' => [],
        ], 200);
    }

    public function updateClassSchedule(Request $request)
    {
        $schedule_id = $request->schedule_id ? $request->schedule_id : 0;
        $schedule_date = $request->schedule_date ? $request->schedule_date : 0;

        if (! $schedule_id) {
            return response()->json([
                'status' => false,
                'message' => 'Please, attach ID',
                'data' => [],
            ], 422);
        }

        $schedule_details = ClassSchedule::where('id', $schedule_id)->first();

        $schedule_details->update([
            'schedule_datetime' => $schedule_date,
        ]);

        return response()->json([
            'status' => true,
            'message' => 'Class updated successful!',
            'data' => [],
        ], 200);
    }

    public function startLiveClass(Request $request)
    {
        $schedule_id = $request->schedule_id ? $request->schedule_id : 0;

        if (! $schedule_id) {
            return response()->json([
                'status' => false,
                'message' => 'Please, attach ID',
                'data' => [],
            ], 422);
        }

        $schedule_details = ClassSchedule::where('id', $schedule_id)->first();

        if ($schedule_details->has_started) {
            return response()->json([
                'status' => false,
                'message' => 'You can not start this class! Because it\'s already been started!',
                'data' => null,
            ], 422);
        }

        $schedule_details->update([
            'start_time' => date('Y-m-d H:i:s'),
            'has_started' => true,
        ]);

        return response()->json([
            'status' => true,
            'message' => 'The class has been started! Please take care of your student!',
            'data' => new MentorClassDetailsResource($schedule_details),
        ], 200);
    }


    public function startLiveClassWeb(Request $request)
    {
        $schedule_id = $request->schedule_id ? $request->schedule_id : 0;

        if (! $schedule_id) {
            return response()->json([
                'status' => false,
                'message' => 'Please, attach ID',
                'data' => [],
            ], 422);
        }

        $schedule_details = ClassSchedule::where('id', $schedule_id)->first();

        if (!$schedule_details->has_started) {

            $schedule_details->update([
                'start_time' => date('Y-m-d H:i:s'),
                'has_started' => true,
            ]);
        }

        return response()->json([
            'status' => true,
            'message' => 'The class has been started! Please take care of your student!',
            'data' => new MentorClassDetailsResource($schedule_details),
        ], 200);
    }

    public function studentJoinClass(Request $request)
    {
        $schedule_id = $request->schedule_id ? $request->schedule_id : 0;

        if (! $schedule_id) {
            return response()->json([
                'status' => false,
                'message' => 'Please, attach ID',
                'data' => [],
            ], 422);
        }

        $schedule_details = ClassSchedule::where('id', $schedule_id)->first();

        if (! $schedule_details->has_started) {
            return response()->json([
                'status' => false,
                'message' => 'You can not join this class! Because this class has not been started yet!!',
                'data' => [],
            ], 422);
        }

        StudentJoinHistory::create([
            'class_schedule_id' => $schedule_id,
            'student_id' => $schedule_details->student_id,
            'join_time' => date('Y-m-d H:i:s'),
        ]);

        return response()->json([
            'status' => true,
            'message' => 'Enjoy your class!!',
            'data' => [],
        ], 200);
    }

    public function studentClassJoinHistory(Request $request)
    {
        $schedule_id = $request->schedule_id ? $request->schedule_id : 0;

        if (! $schedule_id) {
            return response()->json([
                'status' => false,
                'message' => 'Please, attach ID',
                'data' => [],
            ], 422);
        }

        $schedule_details = ClassSchedule::forOrganization('class_schedules')
            ->select(
                'class_schedules.*',
                'courses.title as course_title',
                'mentor_informations.name as mentor_name'
            )
            ->leftJoin('courses', 'courses.id', 'class_schedules.course_id')
            ->leftJoin('mentor_informations', 'mentor_informations.id', 'class_schedules.mentor_id')
            ->where('class_schedules.id', $schedule_id)
            ->first();

        $history = StudentJoinHistory::forOrganization('student_join_histories')
            ->where('class_schedule_id', $schedule_id)->get();
        foreach ($history as $item) {
            $item->join_time = $this->addHour($item->join_time, 6);
            $item->schedule_datetime = $schedule_details->schedule_datetime;
            $item->course_title = $schedule_details->course_title;
            $item->mentor_name = $schedule_details->mentor_name;
        }

        return response()->json([
            'status' => true,
            'message' => 'History List!',
            'data' => $history,
        ], 200);
    }

    public function endLiveClass(Request $request)
    {
        $schedule_id = $request->schedule_id ? $request->schedule_id : 0;

        if (! $schedule_id) {
            return response()->json([
                'status' => false,
                'message' => 'Please, attach ID',
                'data' => [],
            ], 422);
        }

        $schedule_details = ClassSchedule::where('id', $schedule_id)->first();

        if (! $schedule_details->has_started) {
            return response()->json([
                'status' => false,
                'message' => 'Please start class first! You can not end a class before starts!',
                'data' => [],
            ], 422);
        }

        $schedule_details->update([
            'end_time' => date('Y-m-d H:i:s'),
            'has_completed' => true,
        ]);

        return response()->json([
            'status' => true,
            'message' => 'The class has been ended! Thank You!',
            'data' => [],
        ], 200);
    }

    public function studentEndLiveClass(Request $request)
    {
        $schedule_id = $request->schedule_id ? $request->schedule_id : 0;

        if (! $schedule_id) {
            return response()->json([
                'status' => false,
                'message' => 'Please, attach ID',
                'data' => [],
            ], 422);
        }

        $schedule_details = ClassSchedule::where('id', $schedule_id)->first();

        if (! $schedule_details->has_started) {
            return response()->json([
                'status' => false,
                'message' => 'Please start class first! You can not end a class before starts!',
                'data' => [],
            ], 422);
        }

        $schedule_details->update([
            'student_end_time' => date('Y-m-d H:i:s'),
        ]);

        return response()->json([
            'status' => true,
            'message' => 'The class has been ended! Thank You!',
            'data' => [],
        ], 200);
    }

    public function deleteClassSchedule(Request $request)
    {
        $schedule_id = $request->schedule_id ? $request->schedule_id : 0;

        if (! $schedule_id) {
            return response()->json([
                'status' => false,
                'message' => 'Please, attach ID',
                'data' => [],
            ], 422);
        }

        $schedule_details = ClassSchedule::where('id', $schedule_id)->first();
        if ($schedule_details->has_started) {
            return response()->json([
                'status' => false,
                'message' => 'You cannot delete the class, because it\'s already been started!',
                'data' => [],
            ], 422);
        }

        ClassSchedule::where('id', $schedule_id)->delete();

        return response()->json([
            'status' => true,
            'message' => 'Class deleted successful!',
            'data' => [],
        ], 200);
    }

    public function mentorCompletedClassList(Request $request)
    {
        try {
            $user_id = Auth::id();
            $from = $request->start_date ? $request->start_date . ' 00:00:00' : '';
            $to = $request->end_date ? $request->end_date . ' 23:59:59' : '';
            $mentor = MentorInformation::forOrganization('mentor_informations')
                ->where('user_id', $user_id)->first();

            $class = ClassSchedule::forOrganization('class_schedules')
                ->select(
                    'class_schedules.*',
                    'courses.title as course_title',
                    'mentor_informations.name as mentor_name',
                    'student_informations.name as student_name',
                    'student_informations.contact_no as student_contact_no'
                )
                ->where('class_schedules.mentor_id', $mentor->id)
                ->where('class_schedules.has_completed', true)
                ->whereBetween('schedule_datetime', [$from, $to])
                ->leftJoin('courses', 'courses.id', 'class_schedules.course_id')
                ->leftJoin('mentor_informations', 'mentor_informations.id', 'class_schedules.mentor_id')
                ->leftJoin('student_informations', 'student_informations.id', 'class_schedules.student_id')
                ->get();

            $times = [];
            foreach ($class as $key => $item) {
                $item->start_time_gmt = $this->addHour($item->start_time, 6);
                $item->end_time_gmt = $this->addHour($item->end_time, 6);
                $item->total_minutes = $this->getTimeDifference($item->start_time, $item->end_time);
                array_push($times, $this->getTimeDifference($item->start_time, $item->end_time));
            }

            $response = [
                'total_time' => $this->calculateTime($times),
                'list' => $class,
            ];

            return response()->json([
                'status' => true,
                'message' => 'Successful',
                'data' => $response,
            ], 200);
        } catch (\Throwable $th) {
            return $this->apiResponse([], $th->getMessage(), false, 500);
        }
    }

    public function studentClassList(Request $request)
    {
        $user_id = Auth::id();
        return $student = StudentInformation::forOrganization('student_informations')->where('user_id', $user_id)->first();

        $class = ClassSchedule::forOrganization('class_schedules')
            ->select(
                'class_schedules.*',
                'courses.title as course_title',
                'mentor_informations.name as mentor_name',
                'student_informations.name as student_name',
                'student_informations.contact_no as student_contact_no'
            )
            ->where('class_schedules.student_id', $student->id)
            ->leftJoin('courses', 'courses.id', 'class_schedules.course_id')
            ->leftJoin('mentor_informations', 'mentor_informations.id', 'class_schedules.mentor_id')
            ->leftJoin('student_informations', 'student_informations.id', 'class_schedules.student_id')
            ->get();

        foreach ($class as $item) {

            if ($item->start_time) {
                $item->start_time = $this->addHour($item->start_time, 6);
            }

            if ($item->end_time) {
                $item->end_time = $this->addHour($item->end_time, 6);
            }

            $isToday = date('Ymd') == date('Ymd', strtotime($item->schedule_datetime));

            $zoomLink = MentorZoomLink::forOrganization('mentor_zoom_links')
                ->where('mentor_id', $item->mentor_id)->first();

            if (! empty($zoomLink)) {
                $item->join_link = $zoomLink->live_link;
            } else {
                $item->join_link = null;
            }

            if ($isToday) {
                $item->can_join = true;
                $item->join_link = $zoomLink->live_link;
            } else {
                $item->can_join = false;
                $item->join_link = null;
            }
        }

        return response()->json([
            'status' => true,
            'message' => 'Successful',
            'data' => $class,
        ], 200);
    }

    public function mentorOngoingClassList(Request $request)
    {
        try {
            $user_id = Auth::id();
            $mentor = MentorInformation::forOrganization('mentor_informations')
                ->where('user_id', $user_id)->first();

            $zoomLink = MentorZoomLink::forOrganization('mentor_zoom_links')
                ->where('mentor_id', $mentor->id)->first();

            $class = ClassSchedule::forOrganization('class_schedules')
                ->select(
                    'class_schedules.*',
                    'courses.title as course_title',
                    'mentor_informations.name as mentor_name',
                    'student_informations.name as student_name',
                    'student_informations.contact_no as student_contact_no'
                )
                ->where('class_schedules.mentor_id', $mentor->id)
                ->where('class_schedules.has_completed', false)
                ->leftJoin('courses', 'courses.id', 'class_schedules.course_id')
                ->leftJoin('mentor_informations', 'mentor_informations.id', 'class_schedules.mentor_id')
                ->leftJoin('student_informations', 'student_informations.id', 'class_schedules.student_id')
                ->get();

            $class_list = [];

            foreach ($class as $item) {

                if ($item->start_time) {
                    $item->start_time = $this->addHour($item->start_time, 6);
                }

                if ($item->end_time) {
                    $item->end_time = $this->addHour($item->end_time, 6);
                }

                $isToday = date('Ymd') == date('Ymd', strtotime($item->schedule_datetime));

                if (! empty($zoomLink)) {
                    $item->join_link = $zoomLink->live_link;
                } else {
                    $item->join_link = null;
                }

                if ($isToday) {
                    $item->can_join = true;
                    array_push($class_list, $item);
                } else {
                    $item->can_join = false;
                    //array_push($class_list, $item);
                }
            }

            return response()->json([
                'status' => true,
                'message' => 'Successful',
                'data' => $class_list,
            ], 200);
        } catch (\Throwable $th) {
            return $this->apiResponse(null, $th->getMessage(), false, 500);
        }
    }

    public function saveOrUpdateCourse(CourseRequest $request)
    {
        // try {
            $course = new Course();


            $data = $request->only($course->getFillable());


            if (empty($request->id)) {
                $course = Course::create($data);
                if (!$request->hasFile('thumbnail')) {
                    $course->update([
                        'thumbnail' => $this->generateCourseThumbnail($course),
                    ]);
                }
            } else {
                $course = Course::findOrFail($request->id);

                $course->update($data);

            }

            if ($request->hasFile('icon')) {
                $course->update([
                    'icon' => $this->imageUpload($request, 'icon', 'icon', $course->icon ?? null),
                ]);
            }

            if ($request->hasFile('thumbnail')) {
                $course->update([
                    'thumbnail' => $this->imageUpload($request, 'thumbnail', 'thumbnail', $course->thumbnail ?? null),
                ]);
            }

            $message = empty($request->id) ? 'Course Created Successfully' : 'Course Updated Successfully';

            return $this->apiResponse($course, $message, true, empty($request->id) ? 201 : 200);
        // } catch (\Throwable $th) {
        //     return $this->errorResponse($th->getMessage(), 'Something went wrong', 500);
        // }
    }

    public function courseDetails($id)
    {

        try {
            $course = Course::where('id', $id)
                ->withCount('courseCategory as module_count')
                ->withCount('batches as batch_number')
                ->withCount('externalLibrary as external_library_number')
                ->with([
                    'courseCategory' => function ($query) {
                        $query->whereNull('subject_id')
                        ->withCount('courseOutlines as course_outlines_count')
                        ->withCount('courseVideo as videos_number')
                        ->withCount('courseScript as scripts_number')
                        ->withCount('courseQuiz as quizzes_number');
                    },
                    'subjects.courseCategory.courseOutlines.script',
                    'subjects.courseCategory.courseOutlines.video',
                    'subjects.courseCategory.courseOutlines.quiz',
                    'courseCategory.courseOutlines',
                    'courseCategory.courseOutlines.script',
                    'courseCategory.courseOutlines.video',
                    'courseCategory.courseOutlines.quiz',
                    'courseRoutine',
                    'courseFeature',
                    'courseLearningItems',
                    'category',
                    'subCategory',
                    'courseMentor' => function ($query) {
                        $query->with(['mentor:id,name,education,institute,image,email']);
                    },
                    'courseFaq',
                ])
                ->withCount('courseCategory as module_count')
                ->get()
                ->map(function ($course) {
                    $basePercentage = 20;
                    $progressPercentage = $basePercentage +
                        ($course->courseCategory()->exists() ? 20 : 0) +
                        ($course->courseFeature()->exists() ? 20 : 0) +
                        ($course->courseMentor()->exists() ? 20 : 0) +
                        ($course->routine_text ? 20 : 0);
                    $course->percentage = min($progressPercentage, 100);

                    return $course;
                })
                ->first();

            if (! $course) {
                return $this->apiResponse([], 'Course not found', false, 404);
            }

            return $this->successResponse($course, 'Course Details');
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Something went wrong', 500);
        }
    }

    public function courseDelete($id)
    {
        try {
            $course = Course::findOrFail($id);
            if ($course->course_outlines()->exists()) {
                return $this->apiResponse([], 'Course has module, So you can not delete it', false, 422);
            }
            $course->delete();

            return $this->successResponse([], 'Course Deleted Successfully');
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Something went wrong', 500);
        }
    }

    public function courseList(Request $request)
    {
        // Build the initial query
        $query = Course::select([
            'id',
            'organization_id',
            'title',
            'title_bn',
            'category_id',
            'sub_category_id',
            'description',
            'thumbnail',
            'icon',
            'number_of_enrolled',
            'regular_price',
            'sale_price',
            'minimum_enroll_amount',
            'installment_type',
            'monthly_amount',
            'discount_percentage',
            'rating',
            'has_life_coach',
            'is_free',
            'sequence',
            'is_active',
            'is_draft',
            'is_featured',
            'currency',
            'created_by',
            'created_at',
            'updated_at'
        ])
            ->with('category:id,name');

        $filters = ['category_id' => '=',
        'sub_category_id' => '=',
        'is_free' => '=',
        'is_active' => '='
    ]; // add your filter columns
        $searchKeys = ['courses.title', 'courses.title_bn', 'courses.description', ]; // Adjust the fields you want to search by

        $this->applySorting($query, $request);
        $this->applySearch($query, $request->input('search'), $searchKeys);
        $this->applyFilters($query, $request, $filters);

        // Pagination flag
        $pagination = $request->boolean('pagination', true);

        // Handle pagination
        if ($pagination) {
            $itemsPerPage = $request->input('itemsPerPage', 10);
            $currentPage = Paginator::resolveCurrentPage('page');
            $results = $query->paginate($itemsPerPage, ['*'], 'page', $currentPage);

            return $this->apiResponse($results, 'Course List', true, 200);
        } else {
            // No pagination, order by 'sequence' and get all results
            $results = $query->orderBy('courses.sequence', 'ASC')->get();

            return $this->apiResponse($results, 'Course List', true, 200);
        }
    }

    public function saveOrUpdateCourseOutline(Request $request)
    {
        try {
            $courseOutline = empty($request->id) ? new CourseOutline() : CourseOutline::findOrFail($request->id);
            $courseAttributes = $request->only($courseOutline->getFillable());
            $courseOutline->fill($courseAttributes);
            $courseOutline->save();
            $message = empty($request->id) ? 'Course Outline Created Successfully' : 'Course Outline Updated Successfully';
            $statusCode = empty($request->id) ? 201 : 200;

            return $this->apiResponse([], $message, true, $statusCode);
        } catch (\Throwable $th) {
            return $this->apiResponse([], $th->getMessage(), false, 500);
        }
    }

    public function courseOutlineList(Request $request, $id)
    {
        $courseOutlineList = CourseOutline::with('course:id,title', 'courseCategory:id,name')
            ->ForOrganization('course_outlines')
            ->where('course_id', $id)
            ->get();

        return $this->apiResponse($courseOutlineList, 'Course Outline List', true, 200);
    }

    public function courseOutlineDelete(Request $request)
    {
        try {
            CourseOutline::where('id', $request->id)->delete();

            return $this->apiResponse([], 'Course Outline Deleted Successfully', true, 200);
        } catch (\Throwable $th) {
            return response()->json([
                'status' => false,
                'message' => $th->getMessage(),
                'data' => [],
            ], 500);
        }
    }

    public function faqList(Request $request)
    {
        $id = $request->id;
        $faqList = CourseFaq::ForOrganization('course_faqs')
            ->where(
                'course_id',
                $id
            )->leftJoin('courses', 'courses.id', 'course_faqs.course_id')
            ->select(
                'course_faqs.title',
                'course_faqs.answer',
                'course_faqs.id',
                'course_faqs.course_id',
                'course_faqs.is_active',
                'courses.title as course_title'
            )
            ->get();

        return $this->apiResponse($faqList, 'FAQ List', true, 200);
    }

    public function saveOrUpdateFaq(Request $request)
    {
        try {
            if (empty($request->id)) {

                $faqArr = json_decode($request->faq, true);
                if ($faqArr) {
                    $faq = [];
                    foreach ($faqArr as $key => $value) {
                        $faq[] = [
                            'title' => $value['title'],
                            'answer' => $value['answer'],
                            'course_id' => $value['course_id'],
                            'is_active' => $value['is_active'],
                            'created_by' => auth()->user()->id,
                            'organization_id' => auth()->user()->organization_id,
                        ];
                    }
                    CourseFaq::insert($faq);
                }

                return $this->apiResponse([], 'Course FAQ Created Successfully', true, 201);
            } else {

                $faq = CourseFaq::where('id', $request->id)->first();
                $faq->update($request->all());

                return $this->apiResponse([], 'Course FAQ Updated Successfully', true, 200);
            }
        } catch (\Throwable $th) {
            return $this->apiResponse([], $th->getMessage(), false, 500);
        }
    }

    public function faqDelete(Request $request)
    {
        try {
            CourseFaq::where('id', $request->id)->delete();

            return $this->apiResponse([], 'Course FAQ Deleted Successfully', true, 200);
        } catch (\Throwable $th) {
            return $this->apiResponse([], $th->getMessage(), false, 500);
        }
    }

    public function saveOrUpdateFeature(Request $request)
    {
        try {
            $courseId = $request->course_id;

            // if (empty($request->id)) {

                $featureArr = json_decode($request->feature, true);

                CourseFeature::where('course_id', $featureArr[0]['course_id'])->delete();

                if ($featureArr) {
                    $feature = [];
                    foreach ($featureArr as $key => $value) {

                        $feature[] = [
                            'title' => $value['title'],
                            'title_bn' => $value['title_bn'],
                            'icon' => $value['icon'],
                            'course_id' => $value['course_id'],
                            'created_by' => auth()->user()->id,
                            'organization_id' => auth()->user()->organization_id,
                        ];
                    }

                    CourseFeature::insert($feature);
                }

                return $this->apiResponse([], 'Course feature Created Successfully', true, 201);
            // } else {

            //     $feature = CourseFeature::where('id', $request->id)->first();
            //     $feature->update($request->all());

            //     return $this->apiResponse([], 'Course feature Updated Successfully', true, 200);
            // }
        } catch (\Throwable $th) {
            return $this->apiResponse([], $th->getMessage(), false, 500);
        }
    }



    public function saveOrUpdateLearningItems (Request $request)
    {
        try {
            $courseId = $request->course_id;

                $featureArr = json_decode($request->feature, true);

                CourseLearningItem::where('course_id', $featureArr[0]['course_id'])->delete();

                if ($featureArr) {
                    $feature = [];
                    foreach ($featureArr as $key => $value) {

                        $feature[] = [
                            'title' => $value['title'],
                            'title_bn' => $value['title_bn'],
                            'icon' => $value['icon'],
                            'course_id' => $value['course_id'],
                            'organization_id' => auth()->user()->organization_id,
                        ];
                    }

                    CourseLearningItem::insert($feature);
                }

                return $this->apiResponse([], 'Course feature Created Successfully', true, 201);

        } catch (\Throwable $th) {
            return $this->apiResponse([], $th->getMessage(), false, 500);
        }
    }

    public function featureList(Request $request)
    {
        $id = $request->id;
        $featureList = CourseFeature::ForOrganization('course_features')
            ->where(
                'course_id',
                $id
            )->leftJoin('courses', 'courses.id', 'course_features.course_id')
            ->select(
                'course_features.title',
                'course_features.title_bn',
                'course_features.id',
                'course_features.course_id',
                'courses.title as course_title'
            )
            ->get();

        return $this->apiResponse($featureList, 'Feature List', true, 200);
    }

    public function featureDelete(Request $request)
    {
        try {
            CourseFeature::where('id', $request->id)->delete();

            return $this->apiResponse([], 'Course Feature Deleted Successfully', true, 200);
        } catch (\Throwable $th) {
            return $this->apiResponse([], $th->getMessage(), false, 500);
        }
    }

    public function saveOrUpdateRoutine(Request $request)
    {
        try {
            if (empty($request->id)) {
                $routineArr = json_decode($request->routine, true);
                if ($routineArr) {
                    $routine = [];
                    foreach ($routineArr as $key => $value) {
                        $routine[] = [
                            'day' => $value['day'],
                            'class_title' => $value['class_title'],
                            'course_id' => $value['course_id'],
                            'is_note' => $value['is_note'],
                            'created_by' => auth()->user()->id,
                            'organization_id' => auth()->user()->organization_id,

                        ];
                    }
                    CourseClassRoutine::insert($routine);
                }

                return $this->apiResponse([], 'Course routine Created Successfully', true, 201);
            } else {
                $routine = CourseClassRoutine::where('id', $request->id)->first();
                $routine->update($request->all());

                return $this->apiResponse([], 'Course routine Updated Successfully', true, 200);
            }
        } catch (\Throwable $th) {
            return $this->apiResponse([], $th->getMessage(), false, 500);
        }
    }

    public function routineList(Request $request)
    {
        $id = $request->id;
        $RoutineList = CourseClassRoutine::ForOrganization('course_class_routines')
            ->where(
                'course_id',
                $id
            )->leftJoin('courses', 'courses.id', 'course_class_routines.course_id')
            ->select(
                'course_class_routines.day',
                'course_class_routines.class_title',
                'course_class_routines.is_note',
                'course_class_routines.id',
                'course_class_routines.course_id',
                'courses.title as course_title'
            )
            ->get();

        return $this->apiResponse($RoutineList, 'Routine List', true, 200);
    }

    public function routineDelete(Request $request)
    {
        try {
            CourseClassRoutine::where('id', $request->id)->delete();

            return $this->apiResponse([], 'Course Routine Deleted Successfully', true, 200);
        } catch (\Throwable $th) {
            return $this->apiResponse([], $th->getMessage(), false, 500);
        }
    }

    public function courseMentorList(Request $request)
    {
        $mentorList = MentorInformation::select('id', 'name', 'user_id', 'email', 'username', 'contact_no')
            ->latest()->get();

        return $this->apiResponse($mentorList, 'Mentor List', true, 200);
    }

    public function courseStudentList(Request $request)
    {
        $studentList = StudentInformation::select(
            'id',
            'name',
            'user_id',
            'email',
            'username',
            'contact_no'
        )->latest()->get();

        return $this->apiResponse($studentList, 'Student List', true, 200);
    }

    public function assignMentorByCourse(Request $request, $id)
    {
        try {
            $mentor = CourseMentor::where('course_id', $request->id)->first();

            return $this->apiResponse($mentor, 'Mentor Assigned Successfully', true, 200);
        } catch (\Throwable $th) {
            return $this->apiResponse([], $th->getMessage(), false, 500);
        }
    }

    public function saveOrUpdateAssignMentor(Request $request)
    {

        $validator = Validator::make($request->all(), [
            'course_id' => 'required|exists:courses,id',
            'mentor_ids' => 'required|array'
        ]);


        if ($validator->fails()) {
            return $this->apiResponse([], $validator->errors(), false, 422);
        }

        DB::beginTransaction();
        try {
            CourseMentor::where('course_id', $request->course_id)->delete();

            foreach ($request->mentor_ids as $mentor_id) {
                CourseMentor::create([
                    'organization_id' => auth()->user()->organization_id,
                    'created_by' => auth()->id(),
                    'course_id' => $request->course_id,
                    'mentor_id' => $mentor_id,
                    'is_active' => true,
                    'created_at' => now(),
                ]);
            }

            $message = 'Course mentor assigned successfully';
            $statusCode = 200;

            DB::commit();

            return $this->apiResponse([], $message, true, $statusCode);
        } catch (\Throwable $th) {
            DB::rollBack();

            return $this->apiResponse([], $th->getMessage(), false, 500);
        }
    }


    public function removeMentorFromCourse(Request $request)
    {

        $validator = Validator::make($request->all(), [
            'course_id' => 'required|exists:course_mentors,course_id',
            'mentor_id' => 'required|exists:course_mentors,mentor_id'
        ]);

        if ($validator->fails()) {
            return $this->apiResponse([], $validator->errors(), false, 422);
        }

        DB::beginTransaction();
        try {
            CourseMentor::where('course_id', $request->course_id)->where('mentor_id', $request->mentor_id)->delete();
            $message = 'Course mentor removed successfully';
            $statusCode = 200;

            DB::commit();

            return $this->apiResponse([], $message, true, $statusCode);
        } catch (\Throwable $th) {
            DB::rollBack();

            return $this->apiResponse([], $th->getMessage(), false, 500);
        }
    }

    public function mentorAssignList(Request $request)
    {
        $id = $request->id;
        $mentorList = CourseMentor::forOrganization('course_mentors')
            ->where(
                'course_id',
                $id
            )->leftJoin('courses', 'courses.id', 'course_mentors.course_id')
            ->leftJoin('mentor_informations', 'mentor_informations.id', 'course_mentors.mentor_id')
            ->select(
                'course_mentors.id',
                'course_mentors.course_id',
                'mentor_informations.id as mentor_id',
                'course_mentors.is_active',
                'courses.title as course_title',
                'mentor_informations.name as mentor_name',
                'mentor_informations.email as mentor_email',
            )
            ->get();

        return $this->apiResponse($mentorList, 'Mentor assign List', true, 200);
    }

    public function mentorAssignDelete(Request $request)
    {
        try {
            CourseMentor::where('id', $request->id)->delete();

            return $this->apiResponse([], 'Course Mentor Deleted Successfully', true, 200);
        } catch (\Throwable $th) {
            return $this->apiResponse([], $th->getMessage(), false, 500);
        }
    }

    public function saveOrUpdateStudentMapping(Request $request)
    {
        try {
            DB::beginTransaction();
            if (empty($request->id)) {
                $mapping = json_decode($request->mapping, true);
                if ($mapping) {
                    $studentMapping = [];
                    foreach ($mapping as $key => $value) {
                        $studentMapping[] = [
                            'course_id' => $value['course_id'],
                            'mentor_id' => $value['mentor_id'],
                            'student_id' => $value['student_id'],
                            'is_active' => $value['is_active'],
                            'created_at' => date('Y-m-d H:i:s'),
                            'organization_id' => auth()->user()->organization_id,
                            'created_by' => auth()->id(),

                        ];
                    }
                    CourseStudentMapping::insert($studentMapping);
                }
                DB::commit();

                return $this->apiResponse([], 'Course studentMapping Created Successfully', true, 201);
            } else {

                $studentMapping = CourseStudentMapping::where('id', $request->id)->first();
                $studentMapping->update([
                    'is_active' => $request->is_active,
                ]);
                DB::commit();

                return $this->apiResponse([], 'Course studentMapping Updated Successfully', true, 200);
            }
        } catch (\Throwable $th) {
            DB::rollBack();

            return $this->apiResponse([], $th->getMessage(), false, 500);
        }
    }

    public function courseListForStudentMapping(Request $request)
    {
        $courseList = Course::select('id', 'title')->latest()->get();

        return $this->apiResponse($courseList, 'Course List', true, 200);
    }

    public function mentorListByCourse(Request $request, $id)
    {
        $mentorList = CourseMentor::forOrganization('course_mentors')
            ->where('course_id', $id)
            ->leftJoin('mentor_informations', 'mentor_informations.id', 'course_mentors.mentor_id')

            ->select(
                'course_mentors.id',
                'course_mentors.course_id',
                'course_mentors.mentor_id',
                'course_mentors.is_active',
                'mentor_informations.name as mentor_name'
            )
            ->get();

        return $this->apiResponse($mentorList, 'Mentor List', true, 200);
    }

    public function studentMappingList(Request $request)
    {
        $query = CourseStudentMapping::forOrganization('course_student_mappings')
            ->leftJoin('courses', 'courses.id', '=', 'course_student_mappings.course_id')
            ->leftJoin('mentor_informations', 'mentor_informations.id', '=', 'course_student_mappings.mentor_id')
            ->leftJoin('student_informations', 'student_informations.id', '=', 'course_student_mappings.student_id')
            ->select(
                'course_student_mappings.id',
                'course_student_mappings.course_id',
                'course_student_mappings.mentor_id',
                'course_student_mappings.student_id',
                'course_student_mappings.is_active',
                'course_student_mappings.created_at',
                'courses.title as course_title',
                'mentor_informations.name as mentor_name',
                'student_informations.name as student_name'
            );

        $this->applySorting($query, $request);

        $searchKeys = ['courses.title', 'mentor_informations.name', 'student_informations.name'];
        $this->applySearch($query, $request->input('search'), $searchKeys);

        $pagination = $request->boolean('pagination', true);
        if ($pagination) {
            $itemsPerPage = $request->input('itemsPerPage', 10);
            $currentPage = Paginator::resolveCurrentPage('page');
            $results = $query->paginate($itemsPerPage, ['*'], 'page', $currentPage);

            return $this->apiResponse($results, 'Student Mapping List', true, 200);
        }

        $results = $query->get();

        return $this->apiResponse($results, 'Student Mapping List', true, 200);
    }

    public function courseStudentMappingDelete(Request $request, $id)
    {
        CourseStudentMapping::where('id', $id)->delete();

        return $this->apiResponse([], 'Course Student Mapping Deleted Successfully', true, 200);
    }

    public function courseTypeList(Request $request)
    {
        $courseType = CourseType::select(
            'course_types.id',
            'course_types.name',
            'course_types.name_bn',
            'course_types.is_active',
        )->latest()->get();

        return $this->apiResponse($courseType, 'Course Type List', true, 200);
    }

    public function enrollMentorList(Request $request)
    {
        $id = $request->id ? $request->id : 0;
        $mentorList = CourseParticipant::forOrganization('course_participants')
            ->where('item_type', 'Course')
            ->leftJoin('student_informations', 'student_informations.user_id', 'course_participants.user_id')
            ->leftJoin('courses', 'courses.id', 'course_participants.item_id')
            ->select(
                'course_participants.*',
                'student_informations.name as student_name',
                'student_informations.email as student_email',
                'courses.title as course_title',

            )->when($id, function ($query, $id) {
                return $query->where('course_participants.item_id', $id);
            })->latest()->get();

        return $this->apiResponse($mentorList, 'Mentor List', true, 200);
    }

    public function courseFreeEnrollment(Request $request)
    {
        try {
            DB::beginTransaction();
            $item = Course::where('id', $request->item_id)->first();
            foreach ($request->user_id as $value) {
                $alreadyEnroll = CourseParticipant::where('item_id', $request->item_id)
                    ->where('user_id', $value['user_id'])
                    ->where('item_type', 'Course')
                    ->first();

                if ($alreadyEnroll) {
                    continue;
                }

                $payment = Payment::create([
                    'user_id' => $value['user_id'],
                    'item_id' => $request->item_id,
                    'item_type' => 'Course',
                    'is_promo_applied' => true,
                    'promo_id' => $request->promo_id,
                    'payable_amount' => $item->sale_price,
                    'paid_amount' => 0.00,
                    'discount_amount' => $item->sale_price,
                    'currency' => $item->currency,
                    'transaction_id' => uniqid(),
                    'payment_type' => 'BACBON',
                    'payment_method' => 'BACBON',
                    'status' => 'Completed',
                ]);

                PaymentDetail::create([
                    'payment_id' => $payment->id,
                    'user_id' => $value['user_id'],
                    'item_id' => $request->item_id,
                    'unit_price' => $item->sale_price,
                    'quantity' => 1,
                    'total' => $item->sale_price,
                ]);

                CourseParticipant::create([
                    'item_id' => $request->item_id,
                    'user_id' => $value['user_id'],
                    'item_type' => 'course',
                    'payment_id' => $payment->id,
                    'item_price' => $item->sale_price,
                    'paid_amount' => 0.00,
                    'discount' => $item->sale_price,
                    'item_type' => 'Course',
                    'is_trial_taken' => false,
                    'is_active' => $request->is_active,
                ]);
            }
            DB::commit();

            return $this->apiResponse([], 'Course Free Enrollment Updated Successfully', true, 200);
        } catch (\Throwable $th) {
            DB::rollBack();

            return $this->apiResponse([], $th->getMessage(), false, 500);
        }
    }

    //

    public function adminCompletedClassList(Request $request)
    {
        $course_id = $request->course_id ? $request->course_id : 0;
        $mentor_id = $request->mentor_id ? $request->mentor_id : 0;
        $student_id = $request->student_id ? $request->student_id : 0;
        $from = $request->from ? $request->from . ' 00:00:00' : '';
        $to = $request->to ? $request->to . ' 23:59:59' : '';

        $class = ClassSchedule::forOrganization('class_schedules')
            ->select(
                'class_schedules.*',
                'courses.title as course_title',
                'mentor_informations.name as mentor_name',
                'student_informations.name as student_name',
                'student_informations.contact_no as student_contact_no'
            )

            ->leftJoin('courses', 'courses.id', 'class_schedules.course_id')
            ->leftJoin('mentor_informations', 'mentor_informations.id', 'class_schedules.mentor_id')
            ->leftJoin('student_informations', 'student_informations.id', 'class_schedules.student_id')
            ->where('class_schedules.course_id', $course_id)
            ->where('class_schedules.mentor_id', $mentor_id)
            ->where('class_schedules.has_completed', true)
            ->whereBetween('schedule_datetime', [$from, $to])
            ->when($student_id, function ($query, $student_id) {
                return $query->where('class_schedules.student_id', $student_id);
            })
            ->get();

        // return $class;
        // $class = ClassSchedule::select(
        //     'class_schedules.*',
        //     'courses.title as course_title',
        //     'mentor_informations.name as mentor_name',
        //     'student_informations.name as student_name',
        //     'student_informations.contact_no as student_contact_no'
        // )
        //     ->where('class_schedules.course_id', $course_id)
        //     ->where('class_schedules.mentor_id', $mentor_id)
        //     ->where('class_schedules.student_id', $student_id)
        //     ->where('class_schedules.has_completed', true)
        //     ->whereBetween('schedule_datetime', [$from, $to])
        //     ->leftJoin('courses', 'courses.id', 'class_schedules.course_id')
        //     ->leftJoin('mentor_informations', 'mentor_informations.id', 'class_schedules.mentor_id')
        //     ->leftJoin('student_informations', 'student_informations.id', 'class_schedules.student_id')
        //     ->get();

        $times = [];
        foreach ($class as $key => $item) {
            $item->start_time_gmt = $this->addHour($item->start_time, 6);
            $item->end_time_gmt = $this->addHour($item->end_time, 6);
            $item->total_minutes = $this->getTimeDifference($item->start_time, $item->end_time);
            array_push($times, $this->getTimeDifference($item->start_time, $item->end_time));
        }

        $response = [
            'total_time' => $this->calculateTime($times),
            'list' => $class,
        ];

        return $this->apiResponse($response, 'Successful', true, 200);
    }

    public function quizAnswerListAdmin(Request $request)
    {
        try {
            // Start building the query
            $query = ChapterQuizResult::forOrganization('chapter_quiz_results')
                ->select(
                    'chapter_quiz_results.*',
                    'chapter_quizzes.title',
                    'chapter_quizzes.title_bn',
                    'chapter_quizzes.duration',
                    'chapter_quizzes.positive_mark',
                    'chapter_quizzes.negative_mark',
                    'chapter_quizzes.total_mark as exam_mark',
                    'chapter_quizzes.number_of_question',
                    'class_levels.name as class_name',
                    'subjects.name as subject_name',
                    'chapters.name as chapter_name',
                    'users.name as student_name',
                )
                ->leftJoin('chapter_quizzes', 'chapter_quizzes.id', 'chapter_quiz_results.chapter_quiz_id')
                ->leftJoin('class_levels', 'class_levels.id', 'chapter_quizzes.class_level_id')
                ->leftJoin('subjects', 'subjects.id', 'chapter_quizzes.subject_id')
                ->leftJoin('chapters', 'chapters.id', 'chapter_quizzes.chapter_id')
                ->leftJoin('users', 'users.id', 'chapter_quiz_results.user_id');

            // Apply search functionality (assuming you want to search within 'title', 'class_name', etc.)
            $searchKeys = ['chapter_quizzes.title', 'class_levels.name', 'subjects.name', 'chapters.name', 'users.name'];
            $this->applySearch($query, $request->input('search'), $searchKeys);

            // Apply sorting
            $this->applySorting($query, $request);

            // Handle pagination
            $pagination = $request->boolean('pagination', true);
            if ($pagination) {
                $itemsPerPage = $request->input('itemsPerPage', 10);
                $currentPage = Paginator::resolveCurrentPage('page');
                $results = $query->paginate($itemsPerPage, ['*'], 'page', $currentPage);

                return $this->apiResponse($results, 'Successful', true, 200);
            }

            // If pagination is disabled, return all results
            $results = $query->orderBy('chapter_quiz_results.id', 'DESC')->get();

            return $this->apiResponse($results, 'Successful', true, 200);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Something went wrong!', 500);
        }
    }

    public function deleteCourse (Request $request, $id) {

        try {
            $user = Auth::user();
            if ($user->user_type == 'Student' || $user->user_type == 'Mentor') {
                return $this->apiResponse(null, 'You are not authorized to perform this action', false, 403);
            }

            if (!$request->id) {
                return $this->apiResponse(null, 'Course id is required', false, 400);
            }

            $id = $request->id;

            $course = Course::findOrFail($id);
            // delete all children of course
            Subject::where('course_id', $id)->delete();
            CourseCategory::where('course_id', $id)->delete();
            CourseFeature::where('course_id', $id)->delete();
            CourseMentor::where('course_id', $id)->delete();

            $courseOutlines = CourseOutline::where('course_id', $id)->get();
            foreach ($courseOutlines as $outline) {

                $script = $outline->script;
                if ($script) {
                    $otherOutline = CourseOutline::where('chapter_script_id', $script->id)->where('course_id', '!=', $id)->first();
                    if (!$otherOutline) {
                        $script->delete();
                    }
                }

                $video = $outline->video;
                if ($video) {
                    $otherOutline = CourseOutline::where('chapter_video_id', $video->id)->where('course_id', '!=', $id)->first();
                    if (!$otherOutline) {
                        $video->delete();
                    }
                }

                $quiz = $outline->quiz;
                if ($quiz) {
                    $otherOutline = CourseOutline::where('chapter_quiz_id', $quiz->id)->where('course_id', '!=', $id)->first();
                    if (!$otherOutline) {
                        $quiz->delete();
                    }
                }
                $outline->delete();
            }


            CourseParticipant::where('item_id', $id)->delete();
            CourseStudentMapping::where('course_id', $id)->delete();

            $course->delete();
            return $this->apiResponse(null, 'Course Deleted Successfully', true, 200);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Something went wrong!', 500);
        }
    }



    public function duplicateCourse (Request $request) {
        try {

            $user = Auth::user();
            if ($user->user_type == 'Student' || $user->user_type == 'Mentor') {
                return $this->apiResponse(null, 'You are not authorized to perform this action', false, 403);
            }
            $id = $request->id;
            $course = Course::findOrFail($id);

            $newCourse = $course->replicate();
            $newCourse->title = $course->title.' (Copy)';
            $newCourse->save();

            // duplicate all children of course


            $subjects = Subject::where('course_id', $id)->get();

            foreach ($subjects as $subject) {
                $newSubject = $subject->replicate();
                $newSubject->course_id = $newCourse->id;
                $newSubject->save();

                $categories = CourseCategory::where('subject_id', $subject->id)->get();
                foreach ($categories as $category) {
                    $newCategory = $category->replicate();
                    $newCategory->course_id = $newCourse->id;
                    $newCategory->subject_id = $newSubject->id;
                    $newCategory->save();


                    $outlines = CourseOutline::where('course_category_id', $category->id)->get();
                    foreach ($outlines as $outline) {
                        $newOutline = $outline->replicate();
                        $newOutline->course_id = $newCourse->id;
                        $newOutline->course_category_id = $newCategory->id;
                        $newOutline->save();
                    }
                }
            }


            $categories = CourseCategory::where('course_id', $id)->whereNull('subject_id')->get();
            foreach ($categories as $category) {
                $newCategory = $category->replicate();
                $newCategory->course_id = $newCourse->id;
                $newCategory->save();

                $outlines = CourseOutline::where('course_category_id', $category->id)->get();
                foreach ($outlines as $outline) {
                    $newOutline = $outline->replicate();
                    $newOutline->course_id = $newCourse->id;
                    $newOutline->course_category_id = $newCategory->id;
                    $newOutline->save();
                }
            }

            $features = CourseFeature::where('course_id', $id)->get();
            foreach ($features as $feature) {
                $newFeature = $feature->replicate();
                $newFeature->course_id = $newCourse->id;
                $newFeature->save();
            }

            $faqs = CourseFaq::where('course_id', $id)->get();
            foreach ($faqs as $faq) {
                $newFaq = $faq->replicate();
                $newFaq->course_id = $newCourse->id;
                $newFaq->save();
            }

            return $this->successResponse($newCourse, 'Course Duplicated Successfully');
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Something went wrong!', 500);
        }
    }


    public function cloneCourseContent (Request $request) {

        $validator = Validator::make($request->all(), [
            'course_id' => 'required|exists:courses,id',
            'external_course_id' => 'required|integer',
            'external_subject_id' => 'required|integer',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse($validator->errors(), 'Validation errors!', 422);
        }

        DB::beginTransaction();

        try {

        $exCourseId = $request->external_course_id;
        $exSubjectId = $request->external_subject_id;


        $fetchedSubject = Http::get("https://api.bacbonschool.com/api/edupack/course/getChapterList/$exCourseId/$exSubjectId")->json();

        $newSubject = Subject::create([
            'organization_id' => auth()->user()->organization_id,
            'created_by'=> auth()->user()->id,
            'name' => $fetchedSubject['name'],
            'name_bn' => $fetchedSubject['name'],
            'subject_code' => $this->codeGenerator('SC', Subject::class),
            'course_id' => $request->course_id
        ]);

        // Chapter as CourseCategory
        $categories = [];
        foreach ($fetchedSubject['chapters'] as $index => $chapter) {


        $courseCategory = CourseCategory::create([
                'organization_id' => auth()->user()->organization_id,
                'created_by'=> auth()->user()->id,
                'name' => $chapter['name'],
                'name_bn' => $chapter['name_bn'],
                'sequence' => $chapter['sequence'],
                'subject_id' => $newSubject->id,
                'course_id' => $request->course_id,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),

        ]);
        $count = 0;
            foreach ($chapter['lecture_videos'] as $video) {
                ChapterVideo::create([
                'video_code' => $this->codeGenerator('CHV', ChapterVideo::class),
                'organization_id' => auth()->user()->organization_id,
                'created_by' => auth()->user()->id,
                'title' => $video['title'],
                'title_bn' => $video['title_bn'],
                'description' => $video['description'],
                's3_url' => $video['full_url'],
                'author_name' => $video['tutor_name'],
                'author_details' => $video['tutor_info'],
                'thumbnail' => $video['thumbnail'],
                'duration' => $video['duration'],
                'sequence' => $count++,
                'is_free' => $video['isFree'],
                'course_id' => $request->course_id,
                'course_id' => $request->course_id,
                'subject_id' => $newSubject->id,
                'course_category_id' => $courseCategory->id,
                'rating' => $video['rating'],
                'is_active' => 1
                ]);
            }

            $count = 0;
            foreach ($chapter['chapter_script'] as $script) {

                if ($this->urlExists('https://api.bacbonschool.com/uploads/Lectures/' . $script['url'])) {

                    $fileUrl = 'https://api.bacbonschool.com/uploads/Lectures/' . $script['url'];

                    $directoryPath = 'uploads/scripts/';
                    $filePath = $directoryPath . basename($fileUrl);

                    if (!file_exists($directoryPath)) {
                        mkdir($directoryPath, 0777, true);
                    }

                    $client = new \GuzzleHttp\Client();
                    $response = $client->get($fileUrl);
                    $contents = $response->getBody()->getContents();
                    file_put_contents($filePath, $contents);


                    ChapterScript::create([
                    'script_code' => $this->codeGenerator('SCR', ChapterScript::class),
                    'organization_id' => auth()->user()->organization_id,
                    'created_by' => auth()->user()->id,
                    'title' => $script['title'],
                    'title_bn' => $script['title_bn'],
                    'description' => null,
                    'raw_url' => asset($filePath),
                    'thumbnail' => null,
                    'course_id' => $request->course_id,
                    'subject_id' => $newSubject->id,
                    'course_category_id' => $courseCategory->id,
                    'is_active' => 1,
                    'is_free' => !$script['is_premium'],
                    'sequence' => $count++
                    ]);
                }


            }


            $count = 0;
            foreach ($chapter['chapter_exams'] as $exam) {

                $quiz =ChapterQuiz::create([
                'created_by' => auth()->user()->id,
                'organization_id' => auth()->user()->organization_id,
                'title' => $exam['exam_name'],
                'title_bn' => $exam['exam_name_bn'],
                'description' => null,
                'quiz_code' => $this->codeGenerator('EXM', ChapterQuiz::class),
                'class_level_id' => null,
                'quiz_type_id' => 1,
                'course_id' => $request->course_id,
                'course_category_id' => $courseCategory->id,
                'subject_id' => $newSubject->id,
                'chapter_id',
                'duration' => $exam['duration'],
                'positive_mark' => $exam['positive_mark'],
                'negative_mark' => $exam['negative_mark'],
                'total_mark' => $exam['total_mark'],
                'number_of_question' => $exam['question_number'],
                'is_free' => 0,
                'sequence' => $count++,
                'sufficient_question' => 1,
                'is_active' => 1,
                ]);


                $questions = [];

                foreach ($exam['questions'] as $question) {

                    $questions[] = [

                        'created_by' => auth()->user()->id,
                        'organization_id' => auth()->user()->organization_id,
                        'chapter_quiz_id' => $quiz->id,
                        'class_level_id' => null,
                        'subject_id' => $newSubject->id,
                        'chapter_id' => null,
                        'question_text' => $question['question'],
                        'question_text_bn' => null,
                        'question_image' => null,
                        'option1' => $question['option1'],
                        'option2' => $question['option2'],
                        'option3' => $question['option3'],
                        'option4' => $question['option4'],
                        'option1_image' => null,
                        'option2_image' => null,
                        'option3_image' => null,
                        'option4_image' => null,
                        'answer1' => $question['correct_answer'] == 1 ? 1 : 0,
                        'answer2' => $question['correct_answer'] == 2 || $question['correct_answer2'] == 2 ? 1 : 0,
                        'answer3' => $question['correct_answer'] == 3 || $question['correct_answer3'] == 3 ? 1 : 0,
                        'answer4' => $question['correct_answer'] == 4 || $question['correct_answer4'] == 4 ? 1 : 0,
                        'explanation_text' => $question['explanation_text'],
                        'explanation_image' => null,
                        'is_active' => 1,
                        'created_at' => now(),

                    ];

                    ChapterQuizQuestion::insert($questions);
                }
            }


        }

        DB::commit();

            return $this->apiResponse(null, 'External API data fetched successfully', true, 200);

    } catch (\Exception $e) {
        DB::rollBack();
        return $this->errorResponse($e->getMessage(), 'An error occurred while calling the external API', 500);
    }

    }

    /**
     * Update CourseStudent quiz participation count
     */
    private function updateCourseStudentQuizCount($userId, $courseId)
    {
        try {
            $student = \App\Models\StudentInformation::where('user_id', $userId)->first();
            if ($student) {
                \App\Models\CourseStudent::where('course_id', $courseId)
                    ->where('student_id', $student->id)
                    ->increment('quiz_participation_count');
            }
        } catch (\Exception $e) {
            \Log::error('Failed to update CourseStudent quiz count: ' . $e->getMessage());
        }
    }

}
