<?php

namespace App\Http\Controllers;

use App\Http\Requests\CreateUserRequest;
use App\Http\Requests\UpdateUserRequest;
use App\Http\Traits\HelperTrait;
use App\Models\MentorInformation;
use App\Models\StudentInformation;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Hash;

class UserController extends Controller
{
    use HelperTrait;

    // List Users

    public function getProfile(Request $request)
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return $this->apiResponse(null, 'User not authenticated', false, 401);
            }

            $profileData = $user->load('organization');

            return $this->apiResponse($profileData, 'Profile retrieved successfully', true, 200);

        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Something went wrong', 500);
        }

    }
    public function index(Request $request)
    {
        $users = User::forOrganization('users')
            ->where(function ($query) use ($request) {
                if ($request->has('search')) {
                    $query->where('name', 'like', '%' . $request->input('search') . '%')
                        ->orWhere('email', 'like', '%' . $request->input('search') . '%');
                }
                if ($request->has('user_type') && $request->input('user_type') != "All") {
                    $query->where('user_type', $request->input('user_type'));
                }
            })
            ->with('organization:id,name')->paginate(10);

        return $this->successResponse($users, 'Users retrieved successfully');
    }

    // Show a single user
    public function show($id)
    {
        try {
            $user = User::with('organization')->findOrFail($id);

            return $this->successResponse($user, 'User retrieved successfully');
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'something went wrong', 500);
        }

    }

    // Store a new user
    public function store(CreateUserRequest $request)
    {
        $data = $request->only([
            'name',
            'email',
            'contact_no',
            'address',
            'username',
            // 'organization_id',
            'image',
            'password',
            'user_type',
            'is_active',
        ]);

        // Handle file upload
        if ($request->hasFile('image')) {
            $data['image'] = $this->imageUpload($request, 'image', 'user');
        }

        // Hash the password before saving
        $data['password'] = bcrypt($data['password']);

        // Create the user
        $user = User::create($data);

        $user->sendConfirmationEmail($data['password']);

        if ($user->user_type == 'Student') {
            $student = new StudentInformation();
            $student->user_id = $user->id;
            $student->name = $user->name;
            $student->email = $user->email;
            $student->username = $user->username;
            $student->contact_no = $user->contact_no;
            $student->current_address = $user->address;
            $student->student_code = $this->codeGenerator('SC', StudentInformation::class);
            $student->image = $user->image;
            $student->save();

        } else if ($user->user_type == 'Mentor') {

            $mentor = new MentorInformation();
            $mentor->user_id = $user->id;
            $mentor->name = $user->name;
            $mentor->email = $user->email;
            $mentor->username = $user->username;
            $mentor->contact_no = $user->contact_no;
            $mentor->current_address = $user->address;
            $mentor->mentor_code = $this->codeGenerator('MC', MentorInformation::class);
            $mentor->image = $user->image;
            $mentor->save();
        }


        return $this->successResponse($user, 'User created successfully');
    }

    // Update an existing user
    public function update(UpdateUserRequest $request, $id)
    {
        $user = User::findOrFail($id);

        $data = $request->only([
            'name',
            'email',
            'contact_no',
            'address',
            'username',
            'organization_id',
            'image',
            'password',
            'user_type',
            'is_active',
        ]);

        // Handle file upload
        if ($request->hasFile('image')) {
            $data['image'] = $this->imageUpload($request, 'image', 'user');
        }

        // Hash the password if it was updated
        if ($request->filled('password')) {
            $data['password'] = bcrypt($data['password']);
        } else {
            unset($data['password']);
        }

        // Update the user
        $user->update($data);

        if ($user->user_type == 'Student') {

            $student = StudentInformation::where('user_id', $user->id)->first();
            $student->name = $user->name;
            $student->email = $user->email;
            $student->username = $user->username;
            $student->contact_no = $user->contact_no;
            $student->current_address = $user->address;
            $student->image = $user->image;
            $student->save();

        } else if ($user->user_type == 'Mentor') {

            $mentor = MentorInformation::where('user_id', $user->id)->first();
            $mentor->name = $user->name;
            $mentor->email = $user->email;
            $mentor->username = $user->username;
            $mentor->contact_no = $user->contact_no;
            $mentor->current_address = $user->address;
            $mentor->image = $user->image;
            $mentor->save();
        }

        return $this->successResponse($user, 'User updated successfully');
    }

    // Delete a user
    public function destroy($id)
    {
        $user = User::findOrFail($id);

        if ($user->user_type == 'Student') {
            $student = StudentInformation::where('user_id', $user->id)->first();
            $student->delete();
        } else if ($user->user_type == 'Mentor') {
            $mentor = MentorInformation::where('user_id', $user->id)->first();
            $mentor->delete();
        }
        $user->delete();

        return $this->successResponse($user, 'User deleted successfully');
    }

    public function userProfile(Request $request)
    {
        try {
            $user = Auth::user();
            if ($user->user_type == 'Student') {
                $student = StudentInformation::where('user_id', $user->id)
                    ->with('user')
                    ->first();

                return $this->apiResponse($student, 'Successful', true, 200);
            } elseif ($user->user_type == 'Mentor') {
                $student = MentorInformation::where('user_id', $user->id)
                    ->with('user')
                    ->first();

                return $this->apiResponse($student, 'Successful', true, 200);
            } else {
                return $this->apiResponse($user, 'Successful', true, 200);
            }
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Something went wrong', 500);
        }
    }

    public function imageUpdate(Request $request)
    {
        try {
            $user = Auth::user();
            $user->image = $this->imageUpload($request, 'image', 'user', $user->image);
            $user->save();

            return $this->apiResponse($user, 'Successful', true, 200);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Something went wrong', 500);
        }
    }


    public function changePassword (Request $request) {
        $validator = Validator::make($request->all(), [
            'id' => 'required',
            'password' => 'required|min:8',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse($validator->errors(), 'Something went wrong', 500);
        }

        $user = Auth::user();
        $types = ['SystemAdmin','SuperAdmin','OrganizationAdmin'];
        if (in_array($user->user_type, $types)) {
            $targetUser = User::find($request->id);
            $targetUser->password = bcrypt($request->password);
            $targetUser->save();
        }

        return $this->apiResponse(null, 'Password update successfully', true, 200);
    }

    public function updatePassword (Request $request) {
        $validator = Validator::make($request->all(), [
            'old_password' => 'required',
            'new_password' => 'required|min:8',
            'confirm_new_password' => 'required|same:new_password',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse($validator->errors(), 'Something went wrong', 500);
        }

        $user = Auth::user();
        if (!$user) {
            return $this->apiResponse(null, 'User not authenticated', false, 401);
        }

        if (Hash::check($request->old_password, $user->password)) {
            $user->password = bcrypt($request->new_password);
            $user->save();
            return $this->apiResponse(null, 'Password updated successfully', true, 200);
        } else {
            return $this->apiResponse(['old_password' => ['Old password is incorrect']], 'Something went wrong', false, 500);
        }
    }

}
