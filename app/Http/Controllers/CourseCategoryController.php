<?php

namespace App\Http\Controllers;

use App\Http\Requests\CreateCourseCategoryRequest;
use App\Http\Traits\HelperTrait;
use App\Models\CourseCategory;
use Illuminate\Http\Request;
use Illuminate\Pagination\Paginator;

class CourseCategoryController extends Controller
{
    use HelperTrait;

    public function courseCategoryList(Request $request)
    {

        $validatedData = $request->validate([
            'course_id' => 'required|exists:courses,id',
        ]);

        $query = CourseCategory::query();

        // $filters = ['category_id' => '=', 'is_free' => '='];
        // $this->applyFilters($query, $request, $filters);

        $searchKeys = ['title', 'description'];
        $this->applySearch($query, $request->input('search'), $searchKeys);

        $this->applySorting($query, $request);

        $courseId = $validatedData['course_id'];
        $query->where('course_id', $courseId);

        $pagination = $request->boolean('pagination', true);
        if ($pagination) {
            $itemsPerPage = $request->input('itemsPerPage', 8);
            $currentPage = Paginator::resolveCurrentPage('page');
            $results = $query->paginate($itemsPerPage, ['*'], 'page', $currentPage);

            return $this->apiResponse($results, 'Course Category List', true, 200);
        }

        $results = $query->get();

        return $this->apiResponse($results, 'Course Category List', true, 200);
    }

    public function courseCategoryCreate(CreateCourseCategoryRequest $request)
    {
        $category = new CourseCategory();
        $fillable = $category->getFillable();

        $requestData = $request->only($fillable);
        $validatedData = $request->validated();

        if ($requestData != $validatedData) {
            return $this->apiResponse($validatedData, 'Invalid data', false, 400);
        }

        $category->fill($validatedData);
        $category->save();

        return $this->apiResponse($category, 'Module created successfully ', true, 200);
    }

    public function courseCategoryUpdate(CreateCourseCategoryRequest $request, $id)
    {
        $category = CourseCategory::findOrFail($id);
        $fillable = $category->getFillable();

        $requestData = $request->only($fillable);
        $validatedData = $request->validated();

        $category->fill($validatedData);
        $category->save();

        return $this->apiResponse($category, 'Module updated successfully', true, 200);
    }

    public function courseCategoryDelete($id)
    {
        $category = CourseCategory::findOrFail($id);
        $category->delete();

        return $this->apiResponse($category, 'Module deleted successfully', true, 200);
    }

    public function courseCategoryShow($id)
    {
        $category = CourseCategory::findOrFail($id);

        return $this->apiResponse($category, 'successfully', true, 200);
    }
}
