<?php

namespace App\Http\Controllers;

use App\Http\Requests\Discussion\AddCommentRequest;
use App\Http\Requests\Discussion\CreateDiscussionRequest;
use App\Http\Requests\Discussion\ReportContentRequest;
use App\Http\Requests\Discussion\ToggleLikeRequest;
use App\Models\Course;
use App\Models\Discussion;
use App\Models\DiscussionBan;
use App\Models\DiscussionComment;
use App\Models\DiscussionLike;
use App\Models\DiscussionReport;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class DiscussionController extends Controller
{
    /**
     * Check if a user is banned from discussions
     */
    private function checkUserBan($userId, $courseId = null)
    {
        // Check for course-specific ban
        $courseBan = DiscussionBan::where('user_id', $userId)
            ->where(function ($query) use ($courseId) {
                $query->where('course_id', $courseId)
                    ->orWhereNull('course_id'); // Global ban
            })
            ->where(function ($query) {
                $query->whereNull('expires_at')
                    ->orWhere('expires_at', '>', now());
            })
            ->first();

        return $courseBan;
    }

    /**
     * List discussions for a course
     */
    public function index(Request $request)
    {
        $courseId = $request->course_id;
        $userId = Auth::id();

        if (!$courseId) {
            return $this->apiResponse([], 'Course ID is required', false, 422);
        }

        // Check if course exists
        $course = Course::find($courseId);
        if (!$course) {
            return $this->apiResponse([], 'Course not found', false, 404);
        }

        // Build the base query
        $query = Discussion::where('course_id', $courseId)
            ->where('is_active', true)
            ->where('is_approved', true)
            ->with(['user:id,name,email,image', 'comments' => function ($query) {
                $query->where('is_active', true)
                    ->whereNull('parent_id')
                    ->with(['user:id,name,email,image', 'likes'])
                    ->withCount(['likes', 'replies']);
            }])
            ->withCount(['comments', 'likes'])
            ->orderBy('is_pinned', 'desc')
            ->orderBy('created_at', 'desc');

        // Handle pagination
        if ($request->input('pagination') == 'false') {
            $discussions = $query->get();

            // Transform the collection to add flags
            $this->addUserFlags($discussions, $userId);

            return $this->apiResponse($discussions, 'Discussions retrieved successfully', true, 200);
        }

        // Paginated results
        $itemsPerPage = $request->input('items_per_page', 10);
        $currentPage = $request->input('current_page', 1);
        $results = $query->paginate($itemsPerPage, ['*'], 'page', $currentPage + 1);

        // Add liked_by_me flag to each discussion, comment, and reply
        $this->addUserFlags($results->getCollection(), $userId);

        return $this->apiResponse([
            'data' => $results->getCollection(),
            'total' => $results->total(),
            'per_page' => $results->perPage(),
            'current_page' => $results->currentPage() - 1,
            'last_page' => $results->lastPage() - 1,
            'from' => $results->firstItem() ? $results->firstItem() - 1 : 0,
            'to' => $results->lastItem() ? $results->lastItem() - 1 : 0
        ], 'Discussions retrieved successfully', true, 200);
    }

    /**
     * Add user-specific flags (liked_by_me, reported_by_me) to discussions and their comments/replies
     *
     * @param \Illuminate\Database\Eloquent\Collection $discussions
     * @param int $userId
     * @return void
     */
    private function addUserFlags($discussions, $userId)
    {
        $discussions->transform(function ($discussion) use ($userId) {
            // Set flags for discussion
            $discussion->liked_by_me = $discussion->isLikedByUser($userId);
            $discussion->reported_by_me = $discussion->isReportedByUser($userId);

            // Set flags for comments and their replies
            if ($discussion->comments && $discussion->comments->count() > 0) {
                $discussion->comments->each(function ($comment) use ($userId) {
                    // Set flags for comment
                    $comment->liked_by_me = $comment->isLikedByUser($userId);
                    $comment->reported_by_me = $comment->isReportedByUser($userId);

                    // Set flags for replies
                    if ($comment->replies && $comment->replies->count() > 0) {
                        $comment->replies->each(function ($reply) use ($userId) {
                            $reply->liked_by_me = $reply->isLikedByUser($userId);
                            $reply->reported_by_me = $reply->isReportedByUser($userId);
                        });
                    }
                });
            }

            return $discussion;
        });
    }

    /**
     * Create a new discussion
     */
    public function store(CreateDiscussionRequest $request)
    {
        $userId = Auth::id();
        $courseId = $request->course_id;

        // Check if user is banned
        $ban = $this->checkUserBan($userId, $courseId);
        if ($ban) {
            $message = 'You are banned from participating in discussions' .
                ($ban->expires_at ? ' until ' . $ban->expires_at->format('Y-m-d H:i:s') : '');
            return $this->apiResponse([], $message, false, 403);
        }

        // Create discussion
        $discussion = new Discussion();
        $discussion->course_id = $courseId;
        $discussion->user_id = $userId;
        $discussion->title = $request->title;
        $discussion->content = $request->content;
        $discussion->is_pinned = false;
        $discussion->is_active = true;
        $discussion->is_approved = true; // Auto-approve for now, can be changed to require approval
        $discussion->organization_id = auth()->user()->organization_id;
        $discussion->save();

        return $this->apiResponse(
            $discussion->load('user:id,name,email'),
            'Discussion created successfully',
            true,
            201
        );
    }

    /**
     * Get a specific discussion with comments
     */
    public function show(Request $request, $id)
    {
        $userId = Auth::id();

        $discussion = $this->discussionDetails($id);

        return $this->apiResponse($discussion, 'Discussion retrieved successfully', true, 200);
    }

    /**
     * Add a comment to a discussion
     */
    public function addComment(AddCommentRequest $request)
    {
        $userId = Auth::id();
        $discussionId = $request->discussion_id;
        $discussion = Discussion::find($discussionId);

        if (!$discussion || !$discussion->is_active || !$discussion->is_approved) {
            return $this->apiResponse([], 'Discussion not found or inactive', false, 404);
        }

        // Check if user is banned
        $ban = $this->checkUserBan($userId, $discussion->course_id);
        if ($ban) {
            $message = 'You are banned from participating in discussions' .
                ($ban->expires_at ? ' until ' . $ban->expires_at->format('Y-m-d H:i:s') : '');
            return $this->apiResponse([], $message, false, 403);
        }

        // Create comment
        $comment = new DiscussionComment();
        $comment->discussion_id = $discussionId;
        $comment->user_id = $userId;
        $comment->parent_id = $request->parent_id;
        $comment->content = $request->content;
        $comment->is_active = true;
        $comment->organization_id = auth()->user()->organization_id;
        $comment->save();

        return $this->apiResponse(
            $this->discussionDetails($discussionId),
            'Comment added successfully',
            true,
            201
        );
    }

    /**
     * Like or unlike a discussion or comment
     */
    public function toggleLike(ToggleLikeRequest $request)
    {
        $userId = Auth::id();
        $discussionId = $request->discussion_id;
        $commentId = $request->comment_id;
        $result = null;
        // Check if user is banned
        if ($discussionId) {
            $discussion = Discussion::where('id', $discussionId)
            ->where('is_active', true)
            ->where('is_approved', true)
            ->with(['user:id,name,email,image', 'comments' => function ($query) {
                $query->where('is_active', true)
                    ->whereNull('parent_id')
                    ->with(['user:id,name,email,image', 'likes', 'replies' => function ($query) {
                        $query->where('is_active', true)
                            ->with(['user:id,name,email,image', 'likes'])
                            ->withCount(['likes']);
                    }])
                    ->withCount(['likes', 'replies']);
            }])
            ->withCount(['comments', 'likes'])
            ->first();
            $ban = $this->checkUserBan($userId, $discussion->course_id);
            // $discussion->load([])
            $result = $discussion;
        } else {
            $comment = DiscussionComment::where('id', $commentId)
            ->with(['user:id,name,email,image', 'likes', 'replies' => function ($query) {
                $query->where('is_active', true)
                    ->with(['user:id,name,email,image', 'likes'])
                    ->withCount(['likes']);
            }])
            ->withCount(['likes', 'replies'])
            ->first();
            $discussion = Discussion::find($comment->discussion_id);
            $ban = $this->checkUserBan($userId, $discussion->course_id);
            $result = $comment;
        }

        if ($ban) {
            $message = 'You are banned from participating in discussions' .
                ($ban->expires_at ? ' until ' . $ban->expires_at->format('Y-m-d H:i:s') : '');
            return $this->apiResponse([], $message, false, 403);
        }

        // Check if already liked
        $like = DiscussionLike::where('user_id', $userId)
            ->where(function ($query) use ($discussionId, $commentId) {
                if ($discussionId) {
                    $query->where('discussion_id', $discussionId);
                } else {
                    $query->where('comment_id', $commentId);
                }
            })
            ->first();

        if ($like) {
            // Unlike
            $like->delete();
            $message = 'Unliked successfully';
            $liked = false;
        } else {
            // Like
            $like = new DiscussionLike();
            $like->user_id = $userId;
            $like->discussion_id = $discussionId;
            $like->comment_id = $commentId;
            $like->organization_id = auth()->user()->organization_id;
            $like->save();
            $message = 'Liked successfully';
            $liked = true;
        }
        $dId = $discussionId ? $discussionId : $comment->discussion_id;
        $result = $this->discussionDetails($dId);

        return $this->apiResponse($result, $message, true, 200);
    }

    /**
     * Report a discussion or comment
     */
    public function report(ReportContentRequest $request)
    {
        $userId = Auth::id();
        $discussionId = $request->discussion_id;
        $commentId = $request->comment_id;

        // Check if already reported
        $report = DiscussionReport::where('user_id', $userId)
            ->where(function ($query) use ($discussionId, $commentId) {
                if ($discussionId) {
                    $query->where('discussion_id', $discussionId);
                } else {
                    $query->where('comment_id', $commentId);
                }
            })
            ->first();

        if ($report) {
            return $this->apiResponse([], 'You have already reported this content', false, 422);
        }

        // Create report
        $report = new DiscussionReport();
        $report->user_id = $userId;
        $report->discussion_id = $discussionId;
        $report->comment_id = $commentId;
        $report->reason = $request->reason;
        $report->status = 'pending';
        $report->organization_id = auth()->user()->organization_id;
        $report->save();

        $dId = $discussionId;

        if ($commentId) {
            $comment = DiscussionComment::find($commentId);
            $dId = $comment->discussion_id;
        }

        $result = $this->discussionDetails($dId);
        return $this->apiResponse($result, 'Content reported successfully', true, 201);
    }

    public function discussionDetails ($id) {

        $discussion = Discussion::where('id', $id)
            ->where('is_active', true)
            ->where('is_approved', true)
            ->with(['user:id,name,email,image'])
            ->withCount(['comments', 'likes', 'reports'])
            ->first();

        if (!$discussion) {
            return $this->apiResponse([], 'Discussion not found', false, 404);
        }

        // Get comments
        $comments = DiscussionComment::where('discussion_id', $id)
            ->where('is_active', true)
            ->whereNull('parent_id')
            ->with(['likes','user:id,name,email,image', 'replies' => function ($query) {
                $query->where('is_active', true)
                    ->with(['user:id,name,email,image', 'likes'])
                    ->withCount(['likes']);
            }])
            ->withCount(['likes', 'replies'])
            ->orderBy('created_at', 'desc')
            ->get();

        $userId = auth()->user()->id;
        // Add liked_by_me flag to each comment and its replies
        $comments->each(function ($comment) use ($userId) {

            // Set liked_by_me for the comment
            $comment->liked_by_me = $comment->isLikedByUser($userId);
            $comment->reported_by_me = $comment->isReportedByUser($userId);

            // Set liked_by_me for each reply
            if ($comment->replies && $comment->replies->count() > 0) {
                $comment->replies->each(function ($reply) use ($userId) {
                    $reply->liked_by_me = $reply->isLikedByUser($userId);
                    $reply->reported_by_me = $reply->isReportedByUser($userId);
                });
            }
        });

        $discussion->liked_by_me = $discussion->isLikedByUser($userId);
        $discussion->reported_by_me = $discussion->isReportedByUser($userId);
        $discussion->comments = $comments;
        return $discussion;
    }
}
