<?php

namespace App\Http\Controllers;

use App\Http\Requests\OrganizationReg;
use App\Http\Requests\StoreOrganizationRequest;
use App\Http\Requests\StoreWebsiteSettingRequest;
use App\Http\Requests\UpdateOrganizationRequest;
use App\Http\Requests\UpdateWebsiteSettingRequest;
use App\Http\Traits\HelperTrait;
use App\Mail\OrgCreateMail;
use App\Models\Organization;
use App\Models\User;
use App\Models\WebsiteSetting;
use App\Models\OrganizationPackage;
use App\Models\OrganizationPayment;
use App\Models\Currency;
use Illuminate\Http\Request;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use App\Http\Resources\Website\LandingPageResource;
use App\Http\Resources\Website\LandingPage2Resource;

use App\Mail\ContactFormMail;

class OrganizationController extends Controller
{
    use HelperTrait;

    protected $model;
    protected $detailsResource;
    protected $detailsMessage = 'Organization details found successfully';


    public function generateOrganizationCode (Request $request) {
        $organizations = Organization::whereNull('organization_code')->get();

        foreach ($organizations as $organization) {

            // $organization->organization_code = $organization->generateUniqueOrganizationCode($organization->name);


            $prefix = strtoupper(substr(preg_replace('/[^A-Za-z]/', '', $organization->name), 0, 1)); // first character (letter only)
            $code = null;

            do {
                $number = str_pad(rand(0, 99999), 5, '0', STR_PAD_LEFT);
                $code = $prefix . $number;
            } while (Organization::where('organization_code', $code)->exists());

            $organization->organization_code = $code;

            $organization->save();
        }

        return $this->apiResponse($organizations, 'Organization code generated successfully', true, 200);

    }

    public function getLandinPageInformation (Request $request) {

        $result = Organization::find($request->id);
        if (empty($result)) {
            return $this->apiResponse([], 'No data found', false, 404);
        }
        return $this->apiResponse( new LandingPageResource($result), 'Landing page information fetched successfully', true, 200);
    }

    public function getLandinPageInformationTemp2 (Request $request) {

        $result = Organization::find($request->id);
        if (empty($result)) {
            return $this->apiResponse([], 'No data found', false, 404);
        }
        return $this->apiResponse( new LandingPage2Resource($result), 'Landing page information fetched successfully', true, 200);
    }


    public function index()
    {
        $organizations = Organization::all();

        return $this->apiResponse($organizations, 'Organizations retrieved successfully', true, 200);
    }

    public function store(StoreOrganizationRequest $request)
    {

        $organizationData = $request->validated();
        if ($request->hasFile('logo')) {
            $organizationData['logo'] = $this->imageUpload($request, 'logo', 'logo');
        }
        if ($request->hasFile('banner')) {
            $organizationData['banner'] = $this->imageUpload($request, 'banner', 'banner');
        }
        $organization = Organization::create($organizationData);

        return $this->apiResponse($organization, 'Organization created successfully', true, 201);
    }

    public function configure(StoreOrganizationRequest $request)
    {

        $organizationData = $request->validated();
        if ($request->hasFile('logo')) {
            $organizationData['logo'] = $this->imageUpload($request, 'logo', 'logo');
        }
        if ($request->hasFile('banner')) {
            $organizationData['banner'] = $this->imageUpload($request, 'banner', 'banner');
        }
        $organization = Organization::create($organizationData);

        $user = User::find(Auth::user()->id);
        $user->organization_id = $organization->id;
        $user->save();

        $response = [
            'organization' => $organization,
            'user' => $user,
        ];

        return $this->apiResponse($response, 'Organization created successfully', true, 201);
    }

    public function getOrganizationDetails(Request $request)
    {

        $hostUrl = $request->server('HTTP_ORIGIN');
        if (strpos($hostUrl, 'www.')) {
            $hostUrl = str_replace("www.", "", $hostUrl);
        }
        if (!$hostUrl || $hostUrl == 'http://localhost:4001') {

            $organization = Organization::with(['websiteSettings', 'template', 'lastPayment'])
            ->withCount('courses', 'scripts', 'videos', 'quizzes', 'students', 'mentors')
            ->where('id', 1)
            ->first();

        } else {
            $organization = Organization::with(['websiteSettings', 'template', 'lastPayment'])
                ->withCount('courses', 'scripts', 'videos', 'quizzes', 'students', 'mentors')
                ->where('host_url', $hostUrl)
                ->first();
        }

        $organization->feature_mentors = $organization->mentors()
            ->select('id', 'user_id', 'organization_id', 'name', 'email', 'username', 'education', 'institute', 'contact_no', 'bio', 'mentor_code', 'image', 'is_featured')
            ->where('is_featured', 1)
            ->where('is_active', 1)
            ->latest()
            ->take(8)
            ->get();

        if (is_null($organization)) {
            return $this->apiResponse(null, 'Organization not found', false, 404);
        }

        return $this->apiResponse($organization, 'Organization retrieved successfully', true, 200);
    }

    public function show($id)
    {
        $organization = Organization::with(['lastPayment', 'websiteSettings'])->find($id);
        if (is_null($organization)) {
            return $this->apiResponse(null, 'Organization not found', false, 404);
        }

        return $this->apiResponse($organization, 'Organization retrieved successfully', true, 200);
    }

    public function update(UpdateOrganizationRequest $request, $id)
    {
        // Find the organization by ID
        $organization = Organization::find($id);
        if (is_null($organization)) {
            return $this->apiResponse(null, 'Organization not found', false, 404);
        }

        // Get validated data from the request
        $data = $request->validated();

        // Log the request data for debugging
        \Log::info('Organization update request data:', [
            'id' => $id,
            'validated_data' => $data
        ]);

        // Handle file uploads
        if ($request->hasFile('logo')) {
            $data['logo'] = $this->imageUpload($request, 'logo', 'logo', $organization->logo);
        }
        if ($request->hasFile('banner')) {
            $data['banner'] = $this->imageUpload($request, 'banner', 'banner', $organization->banner);
        }
        if ($request->hasFile('footer_logo')) {
            $data['footer_logo'] = $this->imageUpload($request, 'footer_logo', 'footer_logo', $organization->footer_logo);
        }

        // Update the organization with the new data
        $organization->update($data);

        // Refresh the model to get the updated data
        $organization->refresh();

        // Set the current user to the organization
        $organization->user = Auth::user();

        // Return a success response
        return $this->apiResponse($organization, 'Organization updated successfully', true, 200);
    }



    public function destroy($id)
    {
        $organization = Organization::find($id);
        if (is_null($organization)) {
            return $this->apiResponse(null, 'Organization not found', false, 404);
        }

        $organization->delete();

        return $this->apiResponse(null, 'Organization deleted successfully', true, 200);
    }

    public function storeWebSettings(StoreWebsiteSettingRequest $request)
    {
        try {
            $organizationData = $request->validated();
            if ($request->hasFile('image')) {
                $organizationData['image'] = $this->imageUpload($request, 'image', 'image');
            }
            if ($request->hasFile('banner')) {
                $organizationData['banner'] = $this->imageUpload($request, 'banner', 'banner');
            }
            $organization = WebsiteSetting::create($organizationData);

            return $this->apiResponse($organization, 'Website Setting created successfully', true, 201);
        } catch (\Throwable $th) {
            return $this->apiResponse(null, $th->getMessage(), false, 500);
        }
    }

    public function updateWebSettings(UpdateWebsiteSettingRequest $request, $id)
    {
        try {
            $organization = WebsiteSetting::find($id);
            if (is_null($organization)) {
                return $this->apiResponse(null, 'Website Setting not found', false, 404);
            }

            $input = $request->validated();
            if ($request->hasFile('image')) {
                $input['image'] = $this->imageUpload($request, 'image', 'image', $organization->image);
            }
            if ($request->hasFile('banner')) {
                $input['banner'] = $this->imageUpload($request, 'banner', 'banner', $organization->banner);
            }

            $organization->update($input);

            return $this->apiResponse($organization, 'Website Setting updated successfully', true, 200);
        } catch (\Throwable $th) {
            return $this->apiResponse(null, $th->getMessage(), false, 500);
        }
    }

    public function getWebSettings(Request $request)
    {
        try {
            $query = WebsiteSetting::query();
            $query->with('organization');

            $searchKeys = ['title', 'description']; // add your search columns

            $this->applySorting($query, $request);
            $this->applySearch($query, $request->input('search'), $searchKeys);

            $pagination = $request->boolean('pagination', true);
            if ($pagination) {
                $itemsPerPage = $request->input('itemsPerPage', 8);
                $currentPage = Paginator::resolveCurrentPage('page');
                $results = $query->paginate($itemsPerPage, ['*'], 'page', $currentPage);

                return $this->successResponse($results, 'Website Settings retrieved successfully');
            }

            $results = $query->get();

            return $this->successResponse($results, 'Website Settings retrieved successfully');
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Something went wrong', 500);
        }
    }

    public function singleWebSetting($id)
    {
        try {
            $organization = WebsiteSetting::with('organization')->find($id);
            if (is_null($organization)) {
                return $this->apiResponse(null, 'Website Setting not found', false, 404);
            }

            return $this->apiResponse($organization, 'Website Setting fetched successfully', true, 200);
        } catch (\Throwable $th) {
            return $this->apiResponse(null, $th->getMessage(), false, 500);
        }
    }

    public function destroyWebSettings($id)
    {
        $organization = WebsiteSetting::find($id);
        if (is_null($organization)) {
            return $this->apiResponse(null, 'Website Setting not found', false, 404);
        }

        $organization->delete();

        return $this->apiResponse(null, 'Website Setting deleted successfully', true, 200);
    }

    public function shortName(Request $request)
    {
        $shortName = $request->input('short_name');
        if (preg_match('/\s|[.|\/]|[^\w]/', $shortName, $matches)) {
            $invalidChar = $matches[0];
            $message = sprintf('Format is not allowed: invalid character "%s"', $invalidChar);
            return $this->apiResponse(null, $message, false, 409);
        }
        $shortName = $request->input('short_name');
        $organization = Organization::where('short_name', $shortName)->first();
        if ($organization) {
            return $this->apiResponse(null, 'Sub domain already exists', false, 409);
        }

        return $this->successResponse(null, 'Sub domain available');
    }


    public function suggestSubDomain (Request $request) {
        if (! isset($request->title)) {
            return $this->apiResponse(null, 'Title is required', false, 422);
        }

        $title = $request->title;

        // $organization = Organization::where('title', $title)->first();

        $subdomains = [];
        $baseSubdomain = preg_replace('/\s+/', '_', strtolower($title)); // Replace spaces with hyphens and convert to lowercase

        for ($i = 0; $i <= 10; $i++) {
            $suggestedSubdomain = $i > 0 ? $baseSubdomain . $i : $baseSubdomain;
            $exists = Organization::where('short_name', $suggestedSubdomain)->exists();

            if (!$exists) {
                $subdomains[] = $suggestedSubdomain;
            }

            if (count($subdomains) >= 3) {
                break;
            }
        }

        return $this->apiResponse($subdomains, 'Suggested subdomains', true, 200);



    }
    public function organizationRegister(OrganizationReg $request)
    {
        DB::beginTransaction(); // Start the transaction

        try {
            // Prepare organization data
            $organizationData = $request->only([
                'name', 'headline', 'sub_headline', 'details', 'address', 'short_name',
                'email', 'contact_no', 'menu_position', 'contact_person', 'is_active',
                'contact_number', 'hotline_number', 'host_url', 'asset_host', 'color_theme',
                'promotional_video',
                'template_id'
            ]);

            // Modify fields for organization
            $organizationData['short_name'] = $request->input('short_name');
            $organizationData['logo'] = $request->hasFile('logo') ? $this->imageUpload($request, 'logo', 'logo') : 'default_logo.png';
            $organizationData['banner'] = $request->hasFile('banner') ? $this->imageUpload($request, 'banner', 'banner') : 'default_banner.png';
            $organizationData['email'] = $request->input('user_email');
            $organizationData['contact_no'] = $request->input('contact_no');
            $organizationData['hotline_number'] = $request->input('hotline_number');
            $organizationData['promotional_video'] = $request->input('promotional_video');
            $organizationData['host_url'] = "https://{$request->input('short_name')}.edupackbd.com";

            // Create organization
            $organization = Organization::create($organizationData);
            if (! $organization) {
                throw new \Exception('Organization registration failed');
            }

            // Prepare user data
            $userData = [
                'name' => $request->input('user_name'),
                'email' => $request->input('user_email'),
                'contact_no' => $request->input('contact_no'),
                'username' => $request->input('user_username'),
                'address' => $request->input('user_address'),
                'image' => $this->imageUpload($request, 'user_image', 'image'),
                'password' => Hash::make($request->input('user_password')),
                'user_type' => 'OrganizationAdmin',
                'organization_id' => $organization->id,
            ];

            // Create user
            $user = User::create($userData);

            if (! $user) {
                throw new \Exception('User registration failed');
            }


            $payment = $this->paymentOrganization($organization, $user, $request->is_trial, $request->package_id);

            $organization->last_payment_id = $payment->id;
            $organization->save();

            $user->sendEmailVerificationNotification();
            // Send email to the user
            // Mail::to($request->input('user_email'))->send(new OrgCreateMail($organization));

            DB::commit(); // Commit transaction if everything succeeds

            return $this->apiResponse($organization, 'Organization registered successfully', true, 201);

        } catch (\Throwable $th) {
            DB::rollBack(); // Rollback transaction on failure

            return $this->apiResponse(null, $th->getMessage(), false, 500);
        }
    }


    public function paymentOrganization ($organization, $user, $isFree = true, $packageId = null) {

        if ($packageId) {
            $package = OrganizationPackage::find($packageId);
        } else {
            $package = OrganizationPackage::first();
        }

        $isFreeTaken = OrganizationPayment::where('organization_id', $organization->id)->where('is_trial_taken', 1)->exists();
        if ($isFree) {
            if ($isFreeTaken) return false;
        }

        return OrganizationPayment::create([
            'organization_id' => $organization->id,
            'user_id' => $user->id,
            'package_id' => $package->id,
            'paid_amount' => $isFree ? 0 : $package->sale_price,
            'discount_amount' => 0,
            'expiry_date' => now()->addDays($package->trial_period_days),
            'is_trial_taken' => $isFree ? 1 : 0
        ]);


    }


    public function organizationPackageList (Request $request) {
        $package_list = OrganizationPackage::where('is_active', 1)->get();
        return $this->apiResponse($package_list, 'Package List', true, 200);
    }


    // Super Admin Routes


    public function organizationListForSuperAdmin (Request $request) {
        $query = Organization::query();
        $filters = ['name' => 'LIKE', 'short_name' => 'LIKE', 'is_active' => '='];
        $searchKeys = ['name', 'short_name', 'created_by', 'email', 'contact_no'];
        $this->applyFilters($query, $request, $filters);
        $this->applySorting($query, $request);
        $this->applySearch($query, $request->input('search'), $searchKeys);
        $pagination = $request->boolean('pagination', true);
        if ($pagination) {
            $itemsPerPage = $request->input('itemsPerPage', 10);
            $currentPage = Paginator::resolveCurrentPage('page');
            $results = $query->paginate($itemsPerPage, ['*'], 'page', $currentPage);
        } else {
            $results = $query->get();
        }
        return $this->apiResponse($results, 'Organization List', true, 200);
    }

    public function organizationDetailForSuperAdmin (Request $request, $id) {
        $organization = Organization::with(['lastPayment.package', 'all_payments.package', 'admins'])->find($id);
        return $this->apiResponse($organization, 'Organization Detail', true, 200);
    }

    public function submitContactForm (Request $request) {

        $request->validate([
            'name' => 'required',
            'organization_id' => 'required',
            'email' => 'required|email',
            'message' => 'required',
        ]);

        $organization = Organization::find($request->organization_id);

        try {
            // Send email to admin
            Mail::to($organization->email)->send(new ContactFormMail($request->all()));
            return $this->apiResponse(null, 'Thank you for your message!', true, 201);
        } catch (\Exception $e) {
            return $this->apiResponse(null, $e->getMessage(), false, 500);
        }
    }

    // ==================== CURRENCY MANAGEMENT METHODS ====================

    /**
     * Get organization currencies for the authenticated organization.
     */
    public function getCurrencies(Request $request)
    {
        $organization = auth()->user()->organization;

        if (!$organization->currencies) {
            return $this->apiResponse([], 'Organization currencies retrieved successfully', true, 200);
        }

        // Get currency details with organization-specific data
        $organizationCurrencies = collect($organization->currencies)->map(function ($currencyData) {
            $currency = Currency::find($currencyData['currency_id']);

            if (!$currency) {
                return null;
            }

            return [
                'currency_id' => $currency->id,
                'name' => $currency->name,
                'symbol' => $currency->symbol,
                'code' => $currency->code,
                'exchange_rate' => $currency->exchange_rate,
                'is_primary' => $currencyData['is_primary'] ?? false,
                'is_active' => $currencyData['is_active'] ?? true,
                'custom_exchange_rate' => $currencyData['custom_exchange_rate'] ?? null,
                'effective_exchange_rate' => $currencyData['custom_exchange_rate'] ?? $currency->exchange_rate,
                'assigned_at' => $currencyData['assigned_at'] ?? null
            ];
        })->filter()->values();

        // Filter by active status if requested
        if ($request->has('is_active')) {
            $organizationCurrencies = $organizationCurrencies->where('is_active', $request->boolean('is_active'));
        }

        // Filter by primary status if requested
        if ($request->has('is_primary')) {
            $organizationCurrencies = $organizationCurrencies->where('is_primary', $request->boolean('is_primary'));
        }

        return $this->apiResponse($organizationCurrencies->values(), 'Organization currencies retrieved successfully', true, 200);
    }

    /**
     * Get available currencies for assignment.
     */
    public function getAvailableCurrencies(Request $request)
    {
        $organization = auth()->user()->organization;

        // Get currencies not yet assigned to this organization
        $assignedCurrencyIds = collect($organization->currencies ?? [])->pluck('currency_id');

        $query = Currency::active()
            ->whereNotIn('id', $assignedCurrencyIds);

        // Search functionality
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%")
                  ->orWhere('symbol', 'like', "%{$search}%");
            });
        }

        $currencies = $query->orderBy('name')->get();

        return $this->apiResponse($currencies, 'Available currencies retrieved successfully', true, 200);
    }

    /**
     * Assign currency to organization.
     */
    public function assignCurrency(Request $request)
    {
        $organization = auth()->user()->organization;

        $request->validate([
            'currency_id' => 'required|exists:currencies,id',
            'is_primary' => 'boolean',
            'is_active' => 'boolean',
            'custom_exchange_rate' => 'nullable|numeric|min:0.00001'
        ]);

        // Check if currency is already assigned
        if ($organization->hasCurrency($request->currency_id)) {
            return $this->apiResponse(null, 'Currency is already assigned to this organization', false, 422);
        }

        // Check if currency exists and is active
        $currency = Currency::active()->find($request->currency_id);
        if (!$currency) {
            return $this->apiResponse(null, 'Currency not found or inactive', false, 404);
        }

        $organization->addCurrency(
            $request->currency_id,
            $request->boolean('is_primary', false),
            $request->boolean('is_active', true),
            $request->custom_exchange_rate
        );

        return $this->apiResponse(null, 'Currency assigned to organization successfully', true, 201);
    }

    /**
     * Update organization currency settings.
     */
    public function updateCurrency(Request $request, $currencyId)
    {
        $organization = auth()->user()->organization;

        $request->validate([
            'is_primary' => 'boolean',
            'is_active' => 'boolean',
            'custom_exchange_rate' => 'nullable|numeric|min:0.00001'
        ]);

        // Check if currency is assigned to organization
        if (!$organization->hasCurrency($currencyId)) {
            return $this->apiResponse(null, 'Currency is not assigned to this organization', false, 404);
        }

        $currencyDetails = $organization->getCurrencyDetails($currencyId);

        $organization->addCurrency(
            $currencyId,
            $request->boolean('is_primary', $currencyDetails['is_primary']),
            $request->boolean('is_active', $currencyDetails['is_active']),
            $request->has('custom_exchange_rate') ? $request->custom_exchange_rate : $currencyDetails['custom_exchange_rate']
        );

        return $this->apiResponse(null, 'Organization currency updated successfully', true, 200);
    }

    /**
     * Remove currency from organization.
     */
    public function removeCurrency($currencyId)
    {
        $organization = auth()->user()->organization;

        // Check if currency is assigned to organization
        if (!$organization->hasCurrency($currencyId)) {
            return $this->apiResponse(null, 'Currency is not assigned to this organization', false, 404);
        }

        // Check if it's the primary currency
        $currencyDetails = $organization->getCurrencyDetails($currencyId);
        if ($currencyDetails && $currencyDetails['is_primary']) {
            return $this->apiResponse(null, 'Cannot remove primary currency. Please set another currency as primary first.', false, 422);
        }

        $organization->removeCurrency($currencyId);

        return $this->apiResponse(null, 'Currency removed from organization successfully', true, 200);
    }

    /**
     * Set primary currency for organization.
     */
    public function setPrimaryCurrency($currencyId)
    {
        $organization = auth()->user()->organization;

        // Check if currency is assigned to organization
        if (!$organization->hasCurrency($currencyId)) {
            return $this->apiResponse(null, 'Currency is not assigned to this organization', false, 404);
        }

        $currencyDetails = $organization->getCurrencyDetails($currencyId);

        $organization->addCurrency(
            $currencyId,
            true, // Set as primary
            $currencyDetails['is_active'],
            $currencyDetails['custom_exchange_rate']
        );

        return $this->apiResponse(null, 'Primary currency set successfully', true, 200);
    }

}
