<?php

namespace App\Http\Controllers;

use App\Models\BatchMentor;
use Illuminate\Http\Request;

class BatchMentorController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(BatchMentor $batchMentor)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(BatchMentor $batchMentor)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, BatchMentor $batchMentor)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(BatchMentor $batchMentor)
    {
        //
    }
}
