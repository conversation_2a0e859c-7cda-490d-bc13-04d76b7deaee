<?php

namespace App\Http\Controllers;

use App\Models\OrganizationPaymentGateway;
use Illuminate\Http\Request;

class OrganizationPaymentGatewayController extends Controller
{
    public function index()
    {
        $gateways = OrganizationPaymentGateway::with('paymentGateway')->get();
        return $this->apiResponse($gateways, 'Payment gateways retrieved successfully', true, 200);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'payment_gateway_id' => 'required|exists:payment_gateways,id',
            'credentials' => 'required|array',
            'is_active' => 'boolean',
        ]);
        $validated['organization_id'] = auth()->user()->organization_id;
        $gateway = OrganizationPaymentGateway::create($validated);
        return $this->apiResponse($gateway->load('paymentGateway'), 'Payment gateway created successfully', true, 201);
    }

    public function show($id)
    {
        $gateway = OrganizationPaymentGateway::with('paymentGateway')->findOrFail($id);
        return $this->apiResponse($gateway, 'Payment gateway details retrieved successfully', true, 200);
    }

    public function update(Request $request, $id)
    {
        $gateway = OrganizationPaymentGateway::findOrFail($id);
        $validated = $request->validate([
            'payment_gateway_id' => 'sometimes|required|exists:payment_gateways,id',
            'credentials' => 'sometimes|required|array',
            'is_active' => 'boolean',
        ]);
        $gateway->update($validated);
        return $this->apiResponse($gateway->load('paymentGateway'), 'Payment gateway updated successfully', true, 200);
    }

    public function destroy($id)
    {
        $gateway = OrganizationPaymentGateway::findOrFail($id);
        $gateway->delete();
        return $this->apiResponse(null, 'Payment gateway deleted successfully', true, 200);
    }
}
