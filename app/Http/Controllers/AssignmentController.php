<?php

namespace App\Http\Controllers;

use App\Http\Requests\CreateAssignmentRequest;
use App\Http\Requests\CreateAssignmentSubmissionRequest;
use App\Http\Requests\UpdateAssignmentRequest;
use App\Http\Requests\UpdateAssignmentSubmissionRequest;
use App\Http\Traits\HelperTrait;
use App\Http\Traits\CommonTrait;
use App\Models\Assignment;
use App\Models\Course;
use App\Models\Payment;
use App\Models\AssignmentAttachment;
use App\Models\AssignmentSubmission;
use App\Models\MentorInformation;
use App\Models\StudentAssignment;
use App\Models\StudentInformation;
use App\Models\BatchStudent;
use Illuminate\Http\Request;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use DateTime;
use DateTimeZone;
class AssignmentController extends Controller
{
    use HelperTrait, CommonTrait;

    public function publishAssignment(Request $request)
    {
        try {

            $userId = Auth::user()->id;
            $mentor = MentorInformation::where('user_id', $userId)->first();

            if (! $mentor) {
                return $this->errorResponse([], 'Mentor not found', 404);
            }

            $assignment = Assignment::where('mentor_id', $mentor->id)
                ->where('id', $request->id)->first();

            if (! $assignment) {
                return $this->errorResponse([], 'Assignment not found', 404);
            }

            $assignment->update([
                'publish_date' => date('Y-m-d h:i:s'),
                'status' => 'Ongoing',
            ]);

            return $this->apiResponse([], 'Assignment published successfully', true, 200);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'something went wrong', 500);
        }
    }

    public function assignmentListAdmin(Request $request)
    {
        try {
            $courseId = $request->course_id;

            // Start building the query
            $query = Assignment::when($courseId, function ($query) use ($courseId) {
                return $query->where('course_id', $courseId);
            });

            // Include course relationship
            $query->with('course:id,title');
            $query->with('mentor:id,name,image');
            $query->withCount('students');

            // Apply sorting
            $this->applySorting($query, $request);

            // Apply search functionality (assuming you want to search within 'title' field)
            $searchKeys = ['title'];
            $this->applySearch($query, $request->input('search'), $searchKeys);

            // Handle pagination
            $pagination = $request->boolean('pagination', true);
            if ($pagination) {
                $itemsPerPage = $request->input('itemsPerPage', 10);
                $currentPage = Paginator::resolveCurrentPage('page');
                $results = $query->paginate($itemsPerPage, ['*'], 'page', $currentPage);

                return $this->successResponse($results, 'Assignment list successful!');
            }

            // If pagination is disabled, return all results
            $results = $query->latest()->get();

            return $this->successResponse($results, 'Assignment list successful!');
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Something went wrong!', 500);
        }
    }

    public function assignmentList(Request $request)
    {
        $user_id = Auth::user()->id;
        $mentor = MentorInformation::forOrganization('mentor_informations')
            ->where('user_id', $user_id)->first();

        $courseId = $request->course_id;
        $status = $request->status;
        $assignments = Assignment::forOrganization('assignments')
            ->select('assignments.*', 'courses.title as course_title', 'courses.title_bn as course_title_bn')
            ->withCount('studentAssignments')
            ->where('mentor_id', $mentor->id)
            ->when($courseId, function ($query) use ($courseId) {
                return $query->where('course_id', $courseId);
            })
            ->when($request->status, function ($query) use ($request) {
                if ($request->status == 'Ongoing') {
                    return $query->where('deadline', '>', date('Y-m-d h:i:s'))
                        ->where('publish_date', '<=', date('Y-m-d h:i:s'));
                } else if ($request->status == 'Upcoming') {
                    return $query->where('publish_date', '>', date('Y-m-d h:i:s'))
                    ->orWhereNull('publish_date');
                } else if ($request->status == 'Completed') {
                    return $query->where('deadline', '<', date('Y-m-d h:i:s'));
                }
            })
            ->leftJoin('courses', 'courses.id', 'assignments.course_id')
            ->orderBy('assignments.created_at', 'desc')
            ->get();


        foreach ($assignments as $key => $item) {
            $item->time_status = 'Upcoming';
            if ($item->publish_date) {
                $item->publish_date = $this->addHour($item->publish_date, 6);

                $currentTime = date('Y-m-d h:i:s');
                if ($item->publish_date <= $currentTime && $item->deadline > $currentTime) {
                    $item->time_status = 'Ongoing';
                } else if ($item->publish_date > $currentTime) {
                    $item->time_status = 'Upcoming';
                } else if ($item->deadline < $currentTime) {
                    $item->time_status = 'Completed';
                }
            }
        }

        return $this->apiResponse($assignments, 'Assignment list successful!', true, 200);
    }

    public function studentAssignmentList(Request $request)
    {
        $user_id = $request->user()->id;
        $student = StudentInformation::where('user_id', $user_id)->first();

        // Check if student information exists
        if (!$student) {
            return $this->errorResponse('Student information not found', 'Student record not found for this user', 404);
        }

        $assignment = StudentAssignment::select(
                'assignments.*',
                'courses.title as course_title',
                'courses.title_bn as course_title_bn'
            )
            ->when($request->course_id, function ($query) use ($request) {
                return $query->where('assignments.course_id', $request->course_id);
            })
            ->when($request->status, function ($query) use ($request) {
                return $query->where('assignments.status', $request->status);
            })
            ->where('student_assignments.student_id', $student->id)
            ->where('assignments.status', '!=', 'Unpublished')
            ->leftJoin('assignments', 'assignments.id', 'student_assignments.assignment_id')
            ->leftJoin('courses', 'courses.id', 'assignments.course_id')
            ->get();

        foreach ($assignment as $key => $item) {
            $item->is_submitted = AssignmentSubmission::where('student_id', $student->id)->where('assignment_id', $item->id)->exists();
            if ($item->publish_date) {
                $item->publish_date = $this->addHour($item->publish_date, 6);
                $item->publish_date = (new DateTime($item->publish_date))->setTimezone(new DateTimeZone('UTC'))->format('Y-m-d\TH:i:s.u\Z');
            }
        }

        return $this->apiResponse($assignment, 'Assignment list successful!', true, 200);
    }


    public function studentAssignmentListMobile(Request $request)
    {
        $user_id = $request->user()->id;
        $student = StudentInformation::where('user_id', $user_id)->first();

        // Check if student information exists
        if (!$student) {
            return $this->errorResponse('Student information not found', 'Student record not found for this user', 404);
        }

        $assignment = StudentAssignment::select(
                'assignments.*',
                'courses.title as course_title',
                'courses.title_bn as course_title_bn'
            )
            ->when($request->status, function ($query) use ($request) {
                return $query->where('assignments.status', $request->status);
            })
            ->when($request->course_id, function ($query) use ($request) {
                return $query->where('assignments.course_id', $request->course_id);
            })
            ->where('student_assignments.student_id', $student->id)
            ->where('assignments.status', '!=', 'Unpublished')
            ->leftJoin('assignments', 'assignments.id', 'student_assignments.assignment_id')
            ->leftJoin('courses', 'courses.id', 'assignments.course_id')
            ->orderBy('student_assignments.id', 'desc')
            ->get();


            foreach ($assignment as $key => $item) {
                if ($item->publish_date) {
                    $item->publish_date = $this->addHour($item->publish_date, 6);
                    $item->publish_date = (new DateTime($item->publish_date))->setTimezone(new DateTimeZone('UTC'))->format('Y-m-d\TH:i:s.u\Z');
                }

                if ($item->deadline) {
                    $item->deadline = $this->addHour($item->deadline, 6);
                    $item->deadline = (new DateTime($item->deadline))->setTimezone(new DateTimeZone('UTC'))->format('Y-m-d\TH:i:s.u\Z');
                }
            }

        return $this->apiResponse($assignment, 'Assignment list successful!', true, 200);
    }



    public function assignmentDetails(Request $request)
    {
        $studentId = StudentInformation::where('user_id', $request->user()->id)->first();

        // Check if student information exists
        if (!$studentId) {
            return $this->errorResponse('Student information not found', 'Student record not found for this user', 404);
        }

        $assignment = Assignment::with([
            'assignmentSubmission' => function ($query) use ($studentId) {
                $query->where('student_id', $studentId->id);
            },
            'assignmentSubmission.assignmentAttachments',
        ])

            ->where('id', $request->id)->first();

            if ($assignment) {

                if ($assignment->assignmentSubmission) {
                    if ($assignment->assignmentSubmission->marks != null) {
                        $percentage = 100 * $assignment->assignmentSubmission->marks / $assignment->mark;
                        if ($assignment->assignmentSubmission->marks < $assignment->pass_mark) {
                            $assignment->assignmentSubmission->grade_status = 'Fail';
                            // $assignment->assignmentSubmission->percentage = $percentage;
                        } else {
                            if ($percentage < 50) {
                                $assignment->assignmentSubmission->grade_status = 'Average';
                            } elseif ($percentage < 70) {
                                $assignment->assignmentSubmission->grade_status = 'Good';
                            } else {
                                $assignment->assignmentSubmission->grade_status = 'Excellent';
                            }
                            // $assignment->assignmentSubmission->percentage = $percentage;
                        }
                    } else {
                        $assignment->assignmentSubmission->grade_status = null;
                    }
                }


                if ($assignment->publish_date) {
                    $assignment->publish_date = $this->addHour($assignment->publish_date, 6);
                    $assignment->publish_date = (new DateTime($assignment->publish_date))->setTimezone(new DateTimeZone('UTC'))->format('Y-m-d\TH:i:s.u\Z');
                }

                if ($assignment->deadline) {
                    $assignment->deadline = $this->addHour($assignment->deadline, 6);
                    $assignment->deadline = (new DateTime($assignment->deadline))->setTimezone(new DateTimeZone('UTC'))->format('Y-m-d\TH:i:s.u\Z');
                }
            }

        return $this->apiResponse($assignment, 'Assignment Successful', true, 200);
    }

    /**
     * Create an assignment by a mentor and send notifications to assigned students
     *
     * @param CreateAssignmentRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function createAssignment(CreateAssignmentRequest $request)
    {
        DB::beginTransaction();
        try {
            $authId = Auth::user()->id;

            if (gettype($request->input('student_ids')) != 'array') {
                $student_ids = json_decode($request->input('student_ids'), true);
            } else {
                $student_ids = $request->input('student_ids');
            }

            // Get student IDs based on batch or course
            if ($request->batch_id) {
                $student_ids = BatchStudent::where('batch_id', $request->batch_id)->pluck('student_id')->toArray();
            } else {
                $users = Payment::where('item_id', $request->course_id)->where('is_approved', 1)->get(['user_id']);
                $userIds = $users->pluck('user_id')->toArray();
                $studentIds = StudentInformation::whereIn('user_id', $userIds)->pluck('id')->toArray();

                // Check if $schedule is defined, if not use a simpler approach
                if (isset($schedule)) {
                    $student_ids = array_map(function($student_id) use ($schedule) {
                        return [
                            'student_id' => $student_id,
                            'class_schedule_id' => $schedule->id,
                        ];
                    }, $studentIds);
                } else {
                    $student_ids = $studentIds;
                }
            }

            // Get mentor information
            $mentor = MentorInformation::where('user_id', $authId)->first();
            if (!$mentor) {
                return $this->apiResponse(null, 'Mentor not found', false, 404);
            }

            // Create the assignment
            $assignment = new Assignment();
            $assignment->fill($request->only($assignment->getFillable()));
            $assignment->supporting_doc = $this->imageUpload($request, 'supporting_doc', 'supporting_doc');
            $assignment->mentor_id = $mentor->id;
            $assignment->save();

            // Get course information for notification
            $course = Course::find($request->course_id);
            $courseName = $course ? $course->title : 'Course';

            // Create student assignments and collect student user IDs for notifications
            $studentUserIds = [];
            foreach ($student_ids as $student_id) {
                // Handle both array and scalar student_id formats
                $actualStudentId = is_array($student_id) ? $student_id['student_id'] : $student_id;

                $student = new StudentAssignment();
                $student->student_id = $actualStudentId;
                $student->assignment_id = $assignment->id;
                $student->save();

                // Get student user ID for notification
                $studentInfo = StudentInformation::find($actualStudentId);
                if ($studentInfo && $studentInfo->user_id) {
                    $studentUserIds[] = $studentInfo->user_id;
                }
            }

            DB::commit();

            // Prepare notification data
            $notificationData = [
                'assignment_id' => $assignment->id,
                'title' => $assignment->title,
                'course_id' => $request->course_id,
                'course_name' => $courseName,
                'deadline' => $assignment->deadline,
                'mark' => $assignment->mark,
                'created_at' => now()->format('Y-m-d H:i:s')
            ];

            // Send notifications to all assigned students
            foreach ($studentUserIds as $studentUserId) {
                $this->sendUserNotification(
                    $studentUserId,
                    "New assignment: {$assignment->title} has been assigned to you by {$mentor->name}",
                    "New Assignment from {$mentor->name}",
                    'assignment',
                    $notificationData
                );
            }

            return $this->apiResponse($assignment, 'Assignment Created Successfully', true, 201);
        } catch (\Throwable $th) {
            DB::rollBack();
            return $this->apiResponse(null, 'Something went wrong: ' . $th->getMessage(), false, 500);
        }
    }


    /**
     * Create an assignment from admin and send notifications to assigned students and mentor
     *
     * @param CreateAssignmentRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function createAssignmentAdmin(CreateAssignmentRequest $request)
    {
        // Log the assignment creation attempt
        \Illuminate\Support\Facades\Log::info('Creating assignment from admin', [
            'request_data' => $request->all()
        ]);

        DB::beginTransaction();
        try {
            if (!$request->mentor_id) {
                return $this->apiResponse(null, 'Please select a mentor', false, 422);
            }

            if (gettype($request->input('student_ids')) != 'array') {
                $student_ids = json_decode($request->input('student_ids'), true);
            } else {
                $student_ids = $request->input('student_ids');
            }

            // Get student IDs based on batch or course
            if ($request->batch_id) {
                $student_ids = BatchStudent::where('batch_id', $request->batch_id)->pluck('student_id')->toArray();
            } else {
                $users = Payment::where('item_id', $request->course_id)->where('is_approved', 1)->get(['user_id']);
                $userIds = $users->pluck('user_id')->toArray();
                $studentIds = StudentInformation::whereIn('user_id', $userIds)->pluck('id')->toArray();
                $student_ids = $studentIds;
            }

            // Get mentor information
            $mentor = MentorInformation::where('id', $request->mentor_id)->first();
            if (!$mentor) {
                return $this->apiResponse(null, 'Mentor not found', false, 404);
            }

            // Get mentor user information for name
            $mentorName =  $mentor->name ;

            // Create the assignment
            $assignment = new Assignment();
            $assignment->fill($request->only($assignment->getFillable()));
            $assignment->supporting_doc = $this->imageUpload($request, 'supporting_doc', 'supporting_doc');
            $assignment->mentor_id = $mentor->id;
            $assignment->save();

            // Get course information for notification
            $course = Course::find($request->course_id);
            $courseName = $course ? $course->title : 'Course';

            // Create student assignments and collect student user IDs for notifications
            $studentUserIds = [];
            foreach ($student_ids as $student_id) {
                $student = new StudentAssignment();
                $student->student_id = $student_id;
                $student->assignment_id = $assignment->id;
                $student->save();

                // Get student user ID for notification
                $studentInfo = StudentInformation::find($student_id);
                if ($studentInfo && $studentInfo->user_id) {
                    $studentUserIds[] = $studentInfo->user_id;
                }
            }

            DB::commit();

            // Prepare notification data
            $notificationData = [
                'assignment_id' => $assignment->id,
                'title' => $assignment->title,
                'course_id' => $request->course_id,
                'course_name' => $courseName,
                'deadline' => $assignment->deadline,
                'mark' => $assignment->mark,
                'created_at' => now()->format('Y-m-d H:i:s')
            ];

            // Send notification to mentor
            if ($mentor->user_id) {
                $this->sendUserNotification(
                    $mentor->user_id,
                    "You have been assigned as mentor for a new assignment: {$assignment->title}",
                    "New Assignment: {$assignment->title}",
                    'assignment',
                    $notificationData
                );
            }

            // Log student notification data
            \Illuminate\Support\Facades\Log::info('Preparing to send student notifications', [
                'student_user_ids' => $studentUserIds,
                'mentor_name' => $mentorName,
                'assignment_title' => $assignment->title
            ]);

            // Send notifications to all assigned students
            foreach ($studentUserIds as $studentUserId) {
                \Illuminate\Support\Facades\Log::info('Sending notification to student', [
                    'student_user_id' => $studentUserId
                ]);

                $this->sendUserNotification(
                    $studentUserId,
                    "New assignment: {$assignment->title} has been assigned to you by {$mentorName}",
                    "New Assignment from {$mentorName}",
                    'assignment',
                    $notificationData
                );
            }

            return $this->apiResponse($assignment, 'Assignment Created Successfully', true, 201);
        } catch (\Throwable $th) {
            DB::rollBack();

            // Log the detailed error
            \Illuminate\Support\Facades\Log::error('Error creating assignment', [
                'error' => $th->getMessage(),
                'trace' => $th->getTraceAsString(),
                'file' => $th->getFile(),
                'line' => $th->getLine()
            ]);

            return $this->apiResponse(null, 'Something went wrong: ' . $th->getMessage(), false, 500);
        }
    }



    public function updateAssignment(UpdateAssignmentRequest $request)
    {
        DB::beginTransaction();
        try {
            $assignment = Assignment::where('id', $request->id)->first();
            $assignment->fill($request->only($assignment->getFillable()));
            if ($request->hasFile('supporting_doc') ){
                $assignment->supporting_doc = $this->imageUpload($request, 'supporting_doc', 'supporting_doc', $assignment->supporting_doc);
            }


            $assignment->save();

            $assignment->studentAssignments()->delete();

            if (gettype($request->input('student_ids')) != 'array') {
                $student_ids = json_decode($request->input('student_ids'), true);
            } else {
                $student_ids = $request->input('student_ids');
            }

            if ($request->batch_id) {
                $student_ids = BatchStudent::where('batch_id', $request->batch_id)->pluck('student_id')->toArray();
            }

            foreach ($student_ids as $student_id) {
                $student = new StudentAssignment();
                $student->student_id = $student_id;
                $student->assignment_id = $assignment->id;
                $student->save();
            }

            DB::commit();

            return $this->apiResponse($assignment, 'Assignment Updated Successfully', true, 200);
        } catch (\Throwable $th) {
            DB::rollBack();

            return $this->errorResponse($th->getMessage(), 'Something went wrong', 500);
        }
    }

    public function deleteAssignment(Request $request)
    {
        try {
            $assignment = Assignment::where('id', $request->id)->first();
            $assignment->studentAssignments()->delete();
            $assignment->delete();

            return $this->apiResponse([], 'Assignment Deleted Successfully', true, 200);
        } catch (\Throwable $th) {
            return $this->apiResponse([], $th->getMessage(), false, 500);
        }
    }

    public function showAssignment(Request $request)
    {
        $assignment = Assignment::with('studentAssignments.student')

            ->where('id', $request->id)->first();

        return $this->apiResponse($assignment, 'Assignment Successful', true, 200);
    }

    public function assignmentDetailsForMentor(Request $request)
    {
        $assignment = Assignment::with('students')

            ->where('id', $request->id)->first();

            $assignment->submissions = $assignment->students->filter(function ($student) use ($request) {
                $submission = AssignmentSubmission::where('student_id', $student->student_id)->where('assignment_id', $request->id)->orderBy('id', 'desc')->first();
                if ($request->status == 'not_submitted' && $submission) {
                    return false;
                }
                if ($request->status == 'submitted' && (! $submission || $submission->marks)) {
                    return false;
                }
                if ($request->status == 'marked' && (! $submission && ! $submission->marks)) {
                    return false;
                }
                $student->submission_id = $submission?->id;
                $student->submission_status = $submission ? true : false;
                $student->mark = $submission?->marks ;
                return true;
            });
            unset($assignment->students);
            // $assignment->students = $assignment->students->load('student');

        return $this->apiResponse($assignment, 'Assignment Successful', true, 200);
    }
    public function assignmentDetailsForMentorWeb(Request $request)
    {
        $assignment = Assignment::with('students')

            ->where('id', $request->id)->first();

            $assignment->submissions = $assignment->students->filter(function ($student) use ($request) {
                $submission = AssignmentSubmission::where('student_id', $student->student_id)->where('assignment_id', $request->id)->orderBy('id', 'desc')->first();
                if ($request->status == 'not_submitted' && $submission) {
                    return false;
                }
                if ($request->status == 'submitted' && (! $submission || $submission->marks)) {
                    return false;
                }
                if ($request->status == 'marked' && (! $submission && ! $submission->marks)) {
                    return false;
                }
                $student->submission_id = $submission?->id;
                $student->submission_status = $submission ? true : false;
                $student->mark = $submission?->marks ;
                return true;
            });
            unset($assignment->students);
            // $assignment->students = $assignment->students->load('student');

        return $this->apiResponse($assignment, 'Assignment Successful', true, 200);
    }
    public function submissionDetailsForMentor (Request $request) {
        $submission = AssignmentSubmission::where('id', $request->id)->with(['assignment', 'assignmentAttachments'])->first();

        return $this->apiResponse($submission, 'Assignment Successful', true, 200);
    }

    /**
     * Mark an assignment submission and send notification to the student
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function markAssignment(Request $request) {
        $request->validate([
            'submission_id' => 'required',
            'marks' => 'required'
        ]);

        $mentor = MentorInformation::where('user_id', Auth::user()->id)->first();

        $submission = AssignmentSubmission::where('id', $request->submission_id)->first();
        if (!$submission) {
            return $this->apiResponse([], 'No Submission or Assignment not found', false, 404);
        }

        $assignment = $submission->assignment;

        if ($assignment->mentor_id != $mentor->id) {
            return $this->apiResponse([], 'You are not authorized to mark this assignment', false, 403);
        }

        if ($assignment->mark < $request->marks) {
            return $this->apiResponse([], 'Marks cannot be greater than assignment mark', false, 403);
        }

        $submission->marks = $request->marks;
        $submission->remarks = $request->remarks;
        $submission->status = 'Evaluated';
        $submission->evaluated_by = Auth::user()->id;
        $submission->save();

        // Get student information for notification
        $student = StudentInformation::find($submission->student_id);
        if ($student && $student->user_id) {
            // Get course information for notification
            $course = Course::find($assignment->course_id);
            $courseName = $course ? $course->title : 'Course';

            // Calculate grade status and percentage
            $percentage = 100 * $submission->marks / $assignment->mark;
            $gradeStatus = 'Fail';

            if ($submission->marks >= $assignment->pass_mark) {
                if ($percentage < 50) {
                    $gradeStatus = 'Average';
                } elseif ($percentage < 70) {
                    $gradeStatus = 'Good';
                } else {
                    $gradeStatus = 'Excellent';
                }
            }

            // Prepare notification data
            $notificationData = [
                'assignment_id' => $assignment->id,
                'submission_id' => $submission->id,
                'title' => $assignment->title,
                'course_id' => $assignment->course_id,
                'course_name' => $courseName,
                'marks' => $submission->marks,
                'total_marks' => $assignment->mark,
                'percentage' => round($percentage, 2),
                'grade_status' => $gradeStatus,
                'remarks' => $submission->remarks,
                'evaluated_at' => now()->format('Y-m-d H:i:s')
            ];

            // Send notification to student
            $this->sendUserNotification(
                $student->user_id,
                "Your assignment '{$assignment->title}' has been graded. You received {$submission->marks} out of {$assignment->mark} marks.",
                "Assignment Graded",
                'assignment_graded',
                $notificationData
            );
        }

        return $this->apiResponse($submission, 'Assignment marked successfully', true, 200);
    }



    /**
     * Store a student's assignment submission and notify the mentor
     *
     * @param CreateAssignmentSubmissionRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function storeSubmitAssignment(CreateAssignmentSubmissionRequest $request)
    {
        DB::beginTransaction();
        try {
            $authId = Auth::user()->id;
            $student = StudentInformation::where('user_id', $authId)->first();

            // Check if student information exists
            if (!$student) {
                return $this->errorResponse('Student information not found', 'Student record not found for this user', 404);
            }

            // Create new submission
            $submission = new AssignmentSubmission();
            $submission->fill($request->only($submission->getFillable()));
            $submission->ip_address = $request->ip();
            $submission->student_id = $student->id;
            $submission->status = 'Submitted';
            $submission->save();

            // Process attachments if any
            if ($request->hasFile('attachment_files')) {
                $this->syncAttachments($submission, $request);
            }

            // Load attachments for response
            $submission->load('assignmentAttachments');

            // Get assignment details for notification
            $assignment = Assignment::find($request->assignment_id);

            // Get course information for notification
            $course = Course::find($request->course_id);
            $courseName = $course ? $course->title : 'Course';

            // Prepare notification data
            $notificationData = [
                'assignment_id' => $assignment->id,
                'submission_id' => $submission->id,
                'title' => $assignment->title,
                'course_id' => $request->course_id,
                'course_name' => $courseName,
                'student_id' => $student->id,
                'student_name' => $student->name,
                'submitted_at' => now()->format('Y-m-d H:i:s')
            ];

            // Get mentor information and send notification
            if ($assignment && $assignment->mentor_id) {
                $mentor = MentorInformation::find($assignment->mentor_id);
                if ($mentor && $mentor->user_id) {
                    // Send notification to mentor
                    $this->sendUserNotification(
                        $mentor->user_id,
                        "Student {$student->name} has submitted the assignment: {$assignment->title}",
                        "Assignment Submission",
                        'assignment_submission',
                        $notificationData
                    );
                }
            }

            // Update CourseStudent assignment submission count
            $this->updateCourseStudentAssignmentCount($authId, $assignment->course_id);

            DB::commit();

            return $this->apiResponse($submission, 'Assignment Submitted Successfully', true, 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return $this->errorResponse($th->getMessage(), 'Something went wrong', 500);
        }
    }

    public function updateSubmitAssignment(UpdateAssignmentSubmissionRequest $request)
    {
        DB::beginTransaction();
        try {
            $authId = Auth::user()->id;
            $studentId = StudentInformation::where('user_id', $authId)->first();

            // Check if student information exists
            if (!$studentId) {
                return $this->errorResponse('Student information not found', 'Student record not found for this user', 404);
            }

            $assignment = AssignmentSubmission::where('id', $request->id)
                ->where('student_id', $studentId->id)
                ->first();

            if (!$assignment) {
                return $this->errorResponse('Assignment submission not found', 'Assignment submission not found', 404);
            }

            $assignment->fill($request->only($assignment->getFillable()));
            $assignment->save();

            if (! empty($request->attachment_delete_ids)) {
                $this->syncDeleteAttachments($assignment, $request);
            }

            if ($request->hasFile('attachment_files')) {
                $this->syncAttachments($assignment, $request);
            }

            //load attachment
            $assignment->load('assignmentAttachments');

            DB::commit();

            return $this->apiResponse($assignment, 'Assignment Submitted Successfully', true, 200);
        } catch (\Throwable $th) {
            DB::rollBack();

            return $this->errorResponse($th->getMessage(), 'Something went wrong', 500);
        }
    }

    private function syncDeleteAttachments($assignmentSubmission, $request)
    {
        try {
            DB::beginTransaction();
            $deleteAttachmentIds = explode(',', $request->attachment_delete_ids) ?? [];
            if (! empty($deleteAttachmentIds)) {
                AssignmentAttachment::whereIn('id', $deleteAttachmentIds)->delete();
            }
            DB::commit();

            return true;
        } catch (\Throwable $th) {
            DB::rollBack();
            throw $th;
        }
    }
    // protected function imageUpload($request, $imageField, $destination, $oldImage = null)

    private function syncAttachments1($assignmentSubmission, $request)
    {
        try {

            DB::beginTransaction();

            $attachments = [];
            foreach ($request->file('attachment_files') as $index => $image) {

                $imageName = 'answer_'.time().$index.'.'.$image->getClientOriginalExtension();
                $imageDestination = public_path('uploads/submission');
                $image->move($imageDestination, $imageName);
                $filePath = 'submission/'.$imageName;
                $attachments[] = [
                    'assignment_submission_id' => $assignmentSubmission->id,
                    'file' => $filePath,
                ];
            }
            AssignmentAttachment::insert($attachments);
            DB::commit();

            return true;
        } catch (\Throwable $th) {
            DB::rollBack();
            throw $th;
        }
    }


    private function syncAttachments($assignmentSubmission, $request)
    {
        try {
            DB::beginTransaction();

            if (!$request->hasFile('attachment_files')) {
                return true;
            }

            // Use the trait method to handle uploads
            $uploadedFiles = $this->uploadMultipleFiles(
                $request->file('attachment_files'),
                'submission',
                true, // Use S3 if configured
                'answer' // File prefix
            );

            // Prepare attachments data
            $attachments = array_map(function ($filePath) use ($assignmentSubmission) {
                return [
                    'assignment_submission_id' => $assignmentSubmission->id,
                    'file' => $filePath,
                ];
            }, $uploadedFiles);

            // Insert all attachments
            AssignmentAttachment::insert($attachments);

            DB::commit();

            return true;
        } catch (\Throwable $th) {
            DB::rollBack();
            throw $th;
        }
    }


    public function assignmentSubmissionCount ($startDate, $endDate, $studentId) {
        return AssignmentSubmission::where('student_id', $studentId)->whereBetween('created_at', [$startDate, $endDate])->count();
    }

    public function totalAssignmentCount ($startDate, $endDate, $studentId) {
        return StudentAssignment::where('student_id', $studentId)->whereBetween('created_at', [$startDate, $endDate])->count();
    }


    public function allStudentsAssignment (Request $request) {

        if(!$request->assignment_id){
            return $this->apiResponse(null, 'Please select an assignment', false, 422);
        }
        $courseId = null;


        $assignment = Assignment::find($request->assignment_id);

        $course = Course::find($assignment->course_id);

        $list = Payment::where('item_id', $course->id)->pluck('user_id')->toArray();
        $students = StudentInformation::whereIn('user_id', $list)
        ->select('id', 'user_id', 'name', 'email', 'contact_no', 'image')
        ->get();
        foreach ($students as $student) {
            $student->is_selected = StudentAssignment::where('assignment_id', $assignment->id)->where('student_id', $student->id)->exists();
        }

        return $this->apiResponse($students, 'Student List', true, 200);
    }

    /**
     * Update CourseStudent assignment submission count
     */
    private function updateCourseStudentAssignmentCount($userId, $courseId)
    {
        try {
            $student = \App\Models\StudentInformation::where('user_id', $userId)->first();
            if ($student) {
                \App\Models\CourseStudent::where('course_id', $courseId)
                    ->where('student_id', $student->id)
                    ->increment('assignment_submission_count');
            }
        } catch (\Exception $e) {
            \Log::error('Failed to update CourseStudent assignment count: ' . $e->getMessage());
        }
    }
}
