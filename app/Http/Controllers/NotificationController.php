<?php

namespace App\Http\Controllers;

use App\Models\Notification;
use App\Models\User;
use App\Notifications\PushNotification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Notification as NotificationFacade;
use App\Http\Traits\HelperTrait;

class NotificationController extends Controller
{
    use HelperTrait;
    /**
     * Get notifications for the authenticated user.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUserNotifications(Request $request)
    {
        try {
            $user = Auth::user();
            $perPage = $request->input('per_page', 10);
            $page = $request->input('page', 1);
            $filterRead = $request->input('filter_read'); // 'read', 'unread', or null for all

            // Log the request
            \Illuminate\Support\Facades\Log::info('Getting user notifications', [
                'user_id' => $user->id,
                'per_page' => $perPage,
                'page' => $page,
                'filter_read' => $filterRead
            ]);

            // Start building the query
            $query = Notification::forUser($user->id);

            // Apply read/unread filter if specified
            if ($filterRead === 'read') {
                $query->where('is_read', true);
            } elseif ($filterRead === 'unread') {
                $query->where('is_read', false);
            }

            // Get the notifications with pagination
            $notifications = $query->orderBy('created_at', 'desc')
                ->paginate($perPage);

            // Add pagination metadata to the notifications object
            $notificationsWithMeta = $notifications->toArray();
            $notificationsWithMeta['meta'] = [
                'total' => $notifications->total(),
                'per_page' => $notifications->perPage(),
                'current_page' => $notifications->currentPage(),
                'last_page' => $notifications->lastPage(),
                'from' => $notifications->firstItem(),
                'to' => $notifications->lastItem()
            ];

            return $this->apiResponse($notificationsWithMeta, 'Notifications retrieved successfully', true, 200);
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error getting user notifications', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->apiResponse(null, 'Failed to retrieve notifications: ' . $e->getMessage(), false, 500);
        }
    }

    /**
     * Get notifications for the user's organization.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getOrganizationNotifications(Request $request)
    {
        try {
            $user = Auth::user();
            $perPage = $request->input('per_page', 10);
            $page = $request->input('page', 1);
            $filterRead = $request->input('filter_read'); // 'read', 'unread', or null for all

            // Log the request
            \Illuminate\Support\Facades\Log::info('Getting organization notifications', [
                'user_id' => $user->id,
                'organization_id' => $user->organization_id,
                'per_page' => $perPage,
                'page' => $page,
                'filter_read' => $filterRead
            ]);

            // Start building the query
            $query = Notification::forOrganization($user->organization_id)
                ->whereNull('user_id'); // Only get organization-wide notifications

            // Apply read/unread filter if specified
            if ($filterRead === 'read') {
                $query->where('is_read', true);
            } elseif ($filterRead === 'unread') {
                $query->where('is_read', false);
            }

            // Get the notifications with pagination
            $notifications = $query->orderBy('created_at', 'desc')
                ->paginate($perPage);

            // Add pagination metadata to the notifications object
            $notificationsWithMeta = $notifications->toArray();
            $notificationsWithMeta['meta'] = [
                'total' => $notifications->total(),
                'per_page' => $notifications->perPage(),
                'current_page' => $notifications->currentPage(),
                'last_page' => $notifications->lastPage(),
                'from' => $notifications->firstItem(),
                'to' => $notifications->lastItem()
            ];

            return $this->apiResponse($notificationsWithMeta, 'Organization notifications retrieved successfully', true, 200);
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error getting organization notifications', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->apiResponse(null, 'Failed to retrieve organization notifications: ' . $e->getMessage(), false, 500);
        }
    }

    /**
     * Mark a notification as read.
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function markAsRead(Request $request, $id)
    {
        try {
            $user = Auth::user();

            // Log the attempt to mark notification as read
            \Illuminate\Support\Facades\Log::info('Marking notification as read', [
                'user_id' => $user->id,
                'notification_id' => $id
            ]);

            $notification = Notification::where('id', $id)
                ->where(function($query) use ($user) {
                    $query->where('user_id', $user->id)
                        ->orWhere(function($q) use ($user) {
                            $q->where('organization_id', $user->organization_id)
                                ->whereNull('user_id');
                        });
                })
                ->first();

            if (!$notification) {
                \Illuminate\Support\Facades\Log::warning('Notification not found or not accessible', [
                    'user_id' => $user->id,
                    'notification_id' => $id
                ]);

                return $this->apiResponse(null, 'Notification not found or you do not have permission to access it', false, 404);
            }

            $notification->markAsRead();

            \Illuminate\Support\Facades\Log::info('Notification marked as read successfully', [
                'user_id' => $user->id,
                'notification_id' => $id
            ]);

            return $this->apiResponse($notification, 'Notification marked as read', true, 200);
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error marking notification as read', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'notification_id' => $id
            ]);

            return $this->apiResponse(null, 'Failed to mark notification as read: ' . $e->getMessage(), false, 500);
        }
    }

    /**
     * Mark all notifications as read for the authenticated user.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function markAllAsRead(Request $request)
    {
        try {
            $user = Auth::user();

            // Log the attempt to mark all notifications as read
            \Illuminate\Support\Facades\Log::info('Marking all notifications as read', [
                'user_id' => $user->id
            ]);

            // Count notifications before marking as read
            $count = Notification::forUser($user->id)
                ->where('is_read', false)
                ->count();

            // Mark user's personal notifications as read
            Notification::forUser($user->id)
                ->where('is_read', false)
                ->update([
                    'is_read' => true,
                    'read_at' => now()
                ]);

            // Mark organization notifications as read for this user
            // This would require a separate table to track which users have read which org notifications
            // For simplicity, we're not implementing that here

            \Illuminate\Support\Facades\Log::info('All notifications marked as read successfully', [
                'user_id' => $user->id,
                'count' => $count
            ]);

            return $this->apiResponse(['count' => $count], 'All notifications marked as read', true, 200);
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error marking all notifications as read', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->apiResponse(null, 'Failed to mark all notifications as read: ' . $e->getMessage(), false, 500);
        }
    }

    /**
     * Get the count of unread notifications for the authenticated user.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUnreadCount(Request $request)
    {
        try {
            $user = Auth::user();

            // Count user's personal unread notifications
            $userNotificationsCount = Notification::forUser($user->id)
                ->where('is_read', false)
                ->count();

            // Count organization-wide unread notifications
            $orgNotificationsCount = Notification::forOrganization($user->organization_id)
                ->whereNull('user_id')
                ->where('is_read', false)
                ->count();

            // Total unread notifications
            $totalCount = $userNotificationsCount + $orgNotificationsCount;

            $data = [
                'user_notifications' => $userNotificationsCount,
                'organization_notifications' => $orgNotificationsCount,
                'total' => $totalCount
            ];

            return $this->apiResponse($data, 'Unread notifications count retrieved successfully', true, 200);
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error getting unread notifications count', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->apiResponse(null, 'Failed to get unread notifications count: ' . $e->getMessage(), false, 500);
        }
    }

    /**
     * Send a notification to a specific user.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    // public function sendUserNotification(Request $request)
    // {
    //     $request->validate([
    //         'user_id' => 'required|exists:users,id',
    //         'message' => 'required|string',
    //         'title' => 'nullable|string',
    //         'type' => 'nullable|string',
    //         'data' => 'nullable|array'
    //     ]);

    //     $user = User::find($request->user_id);
    //     $options = [
    //         'title' => $request->title,
    //         'type' => $request->type ?? 'general',
    //         'user_id' => $user->id,
    //         'organization_id' => $user->organization_id
    //     ];

    //     // Create a notification record
    //     $notification = new Notification([
    //         'user_id' => $user->id,
    //         'organization_id' => $user->organization_id,
    //         'title' => $request->title,
    //         'message' => $request->message,
    //         'type' => $options['type'],
    //         'data' => $request->data ?? [],
    //         'channel' => "user.{$user->id}"
    //     ]);

    //     $notification->save();

    //     // Send the notification
    //     NotificationFacade::send($notification, new PushNotification(
    //         $request->message,
    //         $request->data ?? [],
    //         $options
    //     ));

    //     return $this->apiResponse($notification, 'Notification sent successfully', true, 200);
    // }

    /**
     * Send a notification to all users in an organization.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    // public function sendOrganizationNotification(Request $request)
    // {
    //     $request->validate([
    //         'organization_id' => 'required|exists:organizations,id',
    //         'message' => 'required|string',
    //         'title' => 'nullable|string',
    //         'type' => 'nullable|string',
    //         'data' => 'nullable|array'
    //     ]);

    //     $options = [
    //         'title' => $request->title,
    //         'type' => $request->type ?? 'general',
    //         'organization_id' => $request->organization_id
    //     ];

    //     // Create a notification record
    //     $notification = new Notification([
    //         'organization_id' => $request->organization_id,
    //         'title' => $request->title,
    //         'message' => $request->message,
    //         'type' => $options['type'],
    //         'data' => $request->data ?? [],
    //         'channel' => "organization.{$request->organization_id}"
    //     ]);

    //     $notification->save();

    //     // Send the notification
    //     NotificationFacade::send($notification, new PushNotification(
    //         $request->message,
    //         $request->data ?? [],
    //         $options
    //     ));

    //     return $this->apiResponse($notification, 'Organization notification sent successfully', true, 200);
    // }
}
