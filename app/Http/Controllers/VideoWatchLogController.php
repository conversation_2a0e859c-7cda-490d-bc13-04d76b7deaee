<?php

namespace App\Http\Controllers;

use App\Models\VideoWatchLog;
use Illuminate\Http\Request;

class <PERSON>WatchLogController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(VideoWatchLog $videoWatchLog)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(VideoWatchLog $videoWatchLog)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, VideoWatchLog $videoWatchLog)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(VideoWatchLog $videoWatchLog)
    {
        //
    }
}
