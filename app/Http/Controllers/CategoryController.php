<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreSubCategoryRequest;
use App\Http\Requests\UpdateSubCategoryRequest;
use App\Http\Traits\HelperTrait;
use App\Models\SubCategory;
use Illuminate\Http\Request;
use Illuminate\Pagination\Paginator;

class CategoryController extends Controller
{
    use HelperTrait;

    public function subCategoryCreate(StoreSubCategoryRequest $request)
    {
        try {
            $categoryData = $request->validated();
            if ($request->hasFile('icon')) {
                $categoryData['icon'] = $this->imageUpload($request, 'icon', 'icon');
            }

            $category = SubCategory::create($categoryData);

            return $this->successResponse($category, 'Category created successfully');
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Something went wrong', 500);
        }
    }

    public function subCategoryUpdate(UpdateSubCategoryRequest $request, $id)
    {
        try {
            $categoryData = $request->validated();
            $category = SubCategory::find($id);
            if ($request->hasFile('icon')) {
                $categoryData['icon'] = $this->imageUpload($request, 'icon', 'icon', $category->icon);
            }
            $category->update($categoryData);

            return $this->successResponse($category, 'Category updated successfully');
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Something went wrong', 500);
        }
    }

    public function subCategoryDelete($id)
    {
        try {
            $category = SubCategory::find($id);
            $category->delete();

            return $this->successResponse($category, 'Category deleted successfully');
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Something went wrong', 500);
        }
    }

    public function subCategoryShow($id)
    {
        try {
            $category = SubCategory::find($id);

            return $this->successResponse($category, 'Category retrieved successfully');
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Something went wrong', 500);
        }
    }

    public function subCategoryList(Request $request)
    {
        try {
            $query = SubCategory::query();
            $filters = ['category_id' => '=']; // add your filter columns
            $searchKeys = ['name', 'description']; // add your search columns

            $this->applyFilters($query, $request, $filters);
            $this->applySorting($query, $request);
            $this->applySearch($query, $request->input('search'), $searchKeys);

            $pagination = $request->boolean('pagination', true);
            if ($pagination) {
                $itemsPerPage = $request->input('itemsPerPage', 8);
                $currentPage = Paginator::resolveCurrentPage('page');
                $results = $query->paginate($itemsPerPage, ['*'], 'page', $currentPage);

                return $this->successResponse($results, 'Sub Category List');
            }

            $results = $query->get();

            return $this->successResponse($results, 'Sub Category List');
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Something went wrong', 500);
        }
    }

    public function subCategoryListByCategory($id)
    {
        try {
            $subCategories = SubCategory::where('category_id', $id)->get();

            return $this->successResponse($subCategories, 'Sub Category List');
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Something went wrong', 500);
        }
    }
}
