<?php

namespace App\Http\Controllers;

use App\Http\Traits\HelperTrait;
use App\Models\CourseParticipant;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class EnrollmentController extends Controller
{
    use HelperTrait;

    public function itemEnrollment(Request $request)
    {
        try {

            $authId = Auth::user()->id;
            $enrollment = CourseParticipant::where('item_id', $request->item_id)
                ->where('user_id', $authId)
                ->where('item_type', $request->item_type)
                ->first();

            if ($enrollment) {
                return $this->errorResponse('Already enrolled', 'Already enrolled', 422);
            }

            $enrollment = new CourseParticipant();
            $enrollment->user_id = $authId;
            $enrollment->item_id = $request->item_id;
            $enrollment->item_type = $request->item_type;
            $enrollment->is_active = 1;
            $enrollment->save();

            return $this->successResponse($enrollment, 'Enrollment Successful');
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Something went wrong', 500);
        }

    }
}
