<?php

namespace App\Http\Controllers\Mobile;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\ClassSchedule;
use App\Models\ClassScheduleStudents;
use App\Models\MentorInformation;
use App\Models\StudentInformation;
use App\Models\StudentAssignment;
use App\Models\Assignment;
use App\Models\Course;
use App\Models\Payment;
use App\Models\BatchStudent;
use App\Models\StudentJoinHistory;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use App\Http\Resources\Mobile\ClassSheduleResource;
use App\Http\Resources\Mobile\MentorClassSheduleResource;
use App\Http\Resources\Mobile\AdminClassSheduleResource;
use App\Http\Resources\Mobile\MentorClassDetailsResource;
use App\Http\Traits\CommonTrait;
use Carbon\Carbon;
use DateTime;
use DateTimeZone;

class ClassScheduleController extends Controller
{
    use CommonTrait;

    public function getLiveClassList (Request $request) {

        $status = $request->status ? $request->status : 'Upcoming';
        $today = Carbon::today();
        $student = StudentInformation::where('user_id', Auth::user()->id)->first();
        $classList = ClassSchedule::join('class_schedule_students', 'class_schedules.id', '=', 'class_schedule_students.class_schedule_id')
            ->where('class_schedule_students.student_id', $student->id)
            ->when($request->schedule_date, function ($query, $schedule_date) {
                return $query->whereDate('class_schedules.schedule_datetime', '=', $schedule_date);
            })
            ->when($request->course_id, function ($query, $course_id) {
                return $query->where('class_schedules.course_id', $course_id);
            })
            ->when($status, function ($query, $status) use ($today) {
                if ($status =='Upcoming') {
                    return $query->where('class_schedules.schedule_datetime', '>=', $today);
                } else if ($status == 'Past') {
                    return $query->where('class_schedules.schedule_datetime', '<', $today);
                }
            })
            ->get();
        $schedule = ClassSheduleResource::collection($classList);

        return $this->apiResponse($schedule, 'Live Class Schedule Fetched Successful!', true, 200);
    }

    public function getLiveClassListForStudent (Request $request) {


        $student = StudentInformation::where('user_id', Auth::user()->id)->first();
        $classList = ClassSchedule::join('class_schedule_students', 'class_schedules.id', '=', 'class_schedule_students.class_schedule_id')
            ->where('class_schedule_students.student_id', $student->id)
            ->when($request->schedule_date, function ($query, $schedule_date) {
                return $query->whereDate('class_schedules.schedule_datetime', '=', $schedule_date);
            })
            ->get();
        $schedule = ClassSheduleResource::collection($classList);
        $data = [
            'schedules' => $schedule,
            'assignments' => $this->studentAssignmentListMobile($student->id)

        ];
        return $this->apiResponse($data, 'Live Class Schedule Fetched Successful!', true, 200);
    }

    public function getLiveClassListForWeb (Request $request) {


        $student = StudentInformation::where('user_id', Auth::user()->id)->first();
        $classList = ClassSchedule::join('class_schedule_students', 'class_schedules.id', '=', 'class_schedule_students.class_schedule_id')
            ->where('class_schedule_students.student_id', $student->id)
            ->when($request->schedule_date, function ($query, $schedule_date) {
                return $query->whereDate('class_schedules.schedule_datetime', '=', $schedule_date);
            })
            ->get();
        $schedule = ClassSheduleResource::collection($classList);

        return $this->apiResponse($schedule, 'Live Class Schedule Fetched Successful!', true, 200);
    }


    public function mentorLiveClassList (Request $request) {

        $mentor = MentorInformation::where('user_id', Auth::user()->id)->first();

        $classList = ClassSchedule::where('mentor_id', $mentor->id)
            ->when($request->course_id, function ($query, $course_id) {
                return $query->where('course_id', $course_id);
            })
            ->when($request->schedule_date, function ($query, $schedule_date) {
                return $query->whereDate('schedule_datetime', $schedule_date);
            }, function ($query) {
                return $query->whereDate('schedule_datetime', '>=', Carbon::today());
            })
            ->orderBy('created_at', 'desc')
            ->get();
        $schedule = MentorClassSheduleResource::collection($classList);

        return $this->apiResponse($schedule, 'Live Class Schedule List Fetched Successful!', true, 200);
    }


    public function adminLiveClassList (Request $request) {

        $user = Auth::user();
        $classList = ClassSchedule::when($request->course_id, function ($query, $course_id) {
                return $query->where('course_id', $course_id);
            })
            ->when($request->start_date && $request->end_date, function ($query) use ($request) {
                return $query->whereBetween('schedule_datetime', [$request->start_date, $request->end_date]);
            }, function ($query) {
                return $query->whereMonth('schedule_datetime', Carbon::now()->month);
            })
            ->orderBy('schedule_datetime', 'asc')
            ->where('organization_id', $user->organization_id)
            ->get();
        $schedule = AdminClassSheduleResource::collection($classList);

        return $this->apiResponse($schedule, 'Live Class Schedule List Fetched Successful!', true, 200);
    }

    public function adminLiveClassDetails (Request $request, $id) {

        $classDetails = ClassSchedule::find($id);

        return $this->apiResponse($classDetails, 'Class Schedule Fetched Successful!', true, 200);
    }

    public function mentorLiveClassDetails (Request $request) {
        $liveClass = ClassSchedule::find($request->id);
        if (!$liveClass) {
            return $this->apiResponse([], 'No Class Found', false, 404);
        }

        return $this->apiResponse(new MentorClassDetailsResource($liveClass), 'Live Class Schedule Details Fetched Successful!', true, 200);

    }
    public function getNextClass (Request $request) {


        $student = StudentInformation::where('user_id', Auth::user()->id)->first();
        $nextClass = ClassSchedule::join('class_schedule_students', 'class_schedules.id', '=', 'class_schedule_students.class_schedule_id')
            ->where('class_schedule_students.student_id', $student->id)
            ->whereDate('class_schedules.schedule_datetime', '>=', Carbon::now())
            ->first();
        if ($nextClass) {
            return new ClassSheduleResource($nextClass);
        }
        return null;
    }



    public function studentAssignmentListMobile($studentId)
    {
        $assignments = StudentAssignment::select(
                'assignments.*',
                'courses.title as course_title',
                'courses.title_bn as course_title_bn'

            )
            ->where('assignments.status', 'Ongoing')
            ->where('student_assignments.student_id', $studentId)
            ->where('assignments.status', '!=', 'Unpublished')
            ->leftJoin('assignments', 'assignments.id', 'student_assignments.assignment_id')
            ->leftJoin('courses', 'courses.id', 'assignments.course_id')
            ->where('assignments.publish_date', '<=', Carbon::now()->format('Y-m-d H:i:s'))
            ->where('assignments.deadline', '>', Carbon::now()->format('Y-m-d H:i:s'))
            ->get();


            foreach ($assignments as $key => $item) {
                $item->is_active = $item->is_active ? true : false;

            }


        return $assignments;
    }


    public function studentNextAssignment($studentId)
    {
        $assignments = StudentAssignment::select(
                'assignments.id',
                'assignments.title',
                'assignments.deadline',
                'courses.title as course_title',
            )
            ->leftJoin('assignments', 'assignments.id', 'student_assignments.assignment_id')
            ->leftJoin('courses', 'courses.id', 'assignments.course_id')
            ->leftJoin('assignment_submissions', 'assignments.id', 'assignment_submissions.assignment_id')
            ->where('assignments.status', 'Ongoing')
            ->whereNotExists(function ($query) use ($studentId) {
                $query->selectRaw(1)
                    ->from('assignment_submissions')
                    ->whereColumn('assignment_submissions.assignment_id', 'assignments.id')
                    ->where('assignment_submissions.student_id', $studentId);
            })
            ->where('student_assignments.student_id', $studentId)
            ->whereDate('assignments.deadline', '>=', Carbon::today())
            ->take(1)
            ->get();

            foreach ($assignments as $assignment) {
                if ($assignment) {
                    $assignment->deadline = Carbon::parse($assignment->deadline)->setTimezone('UTC')->format('Y-m-d\TH:i:s\Z');
                }
            }

        // $assignment->is_active = $assignment->is_active ? true : false;


        return $assignments;
    }


    /**
     * Create a live class schedule by mentor and send notifications to students for online classes
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function createLiveClassSchedule(Request $request)
    {
        $request->validate([
            'title' => 'required|string',
            'course_id' => 'required|integer|exists:courses,id',
            'schedule_datetime' => 'required|date_format:Y-m-d H:i:s',
            'student_ids' => 'nullable|array',
            'batch_id' => 'nullable|integer|exists:batches,id',
            'class_url' => 'nullable|string',
            'is_physical' => 'nullable|boolean',
        ]);

        // Get course information for notifications
        $course = Course::find($request->course_id);
        $courseName = $course ? $course->title : 'Course';

        // Get mentor information
        $mentor = MentorInformation::where('user_id', Auth::user()->id)->first();
        $mentorName = Auth::user()->name ?? 'Mentor';

        // Calculate end time
        $endTime = Carbon::parse($request->schedule_datetime)->addMinutes((int) $request->duration)->format('Y-m-d H:i:s');

        // Create the class schedule
        $schedule = ClassSchedule::create([
            'title' => $request->title,
            'course_id' => $request->course_id,
            'batch_id' => $request?->batch_id,
            'mentor_id' => $mentor->id,
            'schedule_datetime' => $request->schedule_datetime,
            'class_url' => $request->class_url,
            'has_started' => false,
            'has_completed' => false,
            'start_time' => $request->schedule_datetime,
            'end_time' => $endTime,
            'is_active' => true,
            'is_physical' => $request->is_physical ?? false, // Default to online class if not specified
        ]);

        // Collect student IDs based on the request
        $studentIds = [];
        $studentUserIds = [];

        if ($request->student_ids) {
            $studentIds = $request->input('student_ids', []);
            $scheduleStudents = array_map(function($student_id) use ($schedule) {
                return [
                    'student_id' => $student_id,
                    'class_schedule_id' => $schedule->id,
                ];
            }, $studentIds);
        } else if ($request->batch_id) {
            $studentIds = BatchStudent::where('batch_id', $request->batch_id)->pluck('student_id')->toArray();
            $scheduleStudents = array_map(function($student_id) use ($schedule) {
                return [
                    'student_id' => $student_id,
                    'class_schedule_id' => $schedule->id,
                ];
            }, $studentIds);
        } else {
            $users = Payment::where('item_id', $request->course_id)->where('is_approved', 1)->get(['user_id']);
            $userIds = $users->pluck('user_id')->toArray();
            $studentIds = StudentInformation::whereIn('user_id', $userIds)->pluck('id')->toArray();
            $scheduleStudents = array_map(function($student_id) use ($schedule) {
                return [
                    'student_id' => $student_id,
                    'class_schedule_id' => $schedule->id,
                ];
            }, $studentIds);
        }

        // Insert student schedule records
        ClassScheduleStudents::insert($scheduleStudents);

        // If this is an online class (is_physical = false), send notifications to students
        $isPhysical = $request->is_physical ?? false;
        if ($isPhysical == false || $isPhysical == "0" || $isPhysical == 0) {
            // Get user IDs for all students
            $studentUserIds = StudentInformation::whereIn('id', $studentIds)
                ->pluck('user_id')
                ->toArray();

            // Format the class date and time for display
            $classDateTime = Carbon::parse($request->schedule_datetime)->format('l, F j, Y \a\t g:i A');

            // Prepare notification data
            $notificationData = [
                'class_id' => $schedule->id,
                'title' => $schedule->title,
                'course_id' => $request->course_id,
                'course_name' => $courseName,
                'mentor_id' => $mentor->id,
                'mentor_name' => $mentorName,
                'schedule_datetime' => $request->schedule_datetime,
                'formatted_datetime' => $classDateTime,
                'duration' => $request->duration,
                'class_url' => $request->class_url,
                'is_physical' => false
            ];

            // Send notifications to all students
            foreach ($studentUserIds as $studentUserId) {
                $this->sendUserNotification(
                    $studentUserId,
                    "New online class: {$schedule->title} scheduled for {$classDateTime} with {$mentorName}",
                    "New Online Class for {$courseName}",
                    'online_class',
                    $notificationData
                );
            }
        }

        return $this->apiResponse($schedule, 'Live Class Schedule Created Successful!', true, 200);
    }



    /**
     * Create a live class schedule by admin and send notifications to students for online classes
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function createLiveClassScheduleAdmin(Request $request)
    {
        $request->validate([
            'title' => 'required|string',
            'mentor_id' => 'required|integer|exists:mentor_informations,id',
            'course_id' => 'required|integer|exists:courses,id',
            'schedule_datetime' => 'required|date_format:Y-m-d H:i:s',
            'student_ids' => 'nullable|array',
            'batch_id' => 'nullable|integer|exists:batches,id',
            'class_url' => 'nullable|string',
            'is_physical' => 'required',
        ]);

        // Get course and mentor information for notifications
        $course = Course::find($request->course_id);
        $mentor = MentorInformation::find($request->mentor_id);
        $mentorName = $mentor ? User::find($mentor->user_id)->name ?? 'Mentor' : 'Mentor';
        $courseName = $course ? $course->title : 'Course';

        $endTime = Carbon::parse($request->schedule_datetime)->addMinutes((int) $request->duration)->format('Y-m-d H:i:s');
        $schedule = ClassSchedule::create([
            'title' => $request->title,
            'course_id' => $request->course_id,
            'batch_id' => $request->batch_id,
            'mentor_id' => $request->mentor_id,
            'schedule_datetime' => $request->schedule_datetime,
            'class_url' => $request->class_url,
            'has_started' => false,
            'has_completed' => false,
            'start_time' => $request->schedule_datetime,
            'end_time' => $endTime,
            'is_active' => true,
            'is_physical' => $request->is_physical,
        ]);

        // Collect student IDs based on the request
        $studentIds = [];
        $studentUserIds = [];

        if ($request->student_ids) {
            $studentIds = $request->input('student_ids', []);
            $scheduleStudents = array_map(function($student_id) use ($schedule) {
                return [
                    'student_id' => $student_id,
                    'class_schedule_id' => $schedule->id,
                ];
            }, $studentIds);
        } else if ($request->batch_id) {
            $studentIds = BatchStudent::where('batch_id', $request->batch_id)->pluck('student_id')->toArray();
            $scheduleStudents = array_map(function($student_id) use ($schedule) {
                return [
                    'student_id' => $student_id,
                    'class_schedule_id' => $schedule->id,
                ];
            }, $studentIds);
        } else {
            $users = Payment::where('item_id', $request->course_id)->where('is_approved', 1)->get(['user_id']);
            $userIds = $users->pluck('user_id')->toArray();
            $studentIds = StudentInformation::whereIn('user_id', $userIds)->pluck('id')->toArray();
            $scheduleStudents = array_map(function($student_id) use ($schedule) {
                return [
                    'student_id' => $student_id,
                    'class_schedule_id' => $schedule->id,
                ];
            }, $studentIds);
        }

        // Insert student schedule records
        ClassScheduleStudents::insert($scheduleStudents);

        // If this is an online class (is_physical = false), send notifications to students
        if ($request->is_physical == false || $request->is_physical == "0" || $request->is_physical == 0) {
            // Get user IDs for all students
            $studentUserIds = StudentInformation::whereIn('id', $studentIds)
                ->pluck('user_id')
                ->toArray();

            // Format the class date and time for display
            $classDateTime = Carbon::parse($request->schedule_datetime)->format('l, F j, Y \a\t g:i A');

            // Prepare notification data
            $notificationData = [
                'class_id' => $schedule->id,
                'title' => $schedule->title,
                'course_id' => $request->course_id,
                'course_name' => $courseName,
                'mentor_id' => $request->mentor_id,
                'mentor_name' => $mentorName,
                'schedule_datetime' => $request->schedule_datetime,
                'formatted_datetime' => $classDateTime,
                'duration' => $request->duration,
                'class_url' => $request->class_url,
                'is_physical' => false
            ];

            // Send notifications to all students
            foreach ($studentUserIds as $studentUserId) {
                $this->sendUserNotification(
                    $studentUserId,
                    "New online class: {$schedule->title} scheduled for {$classDateTime} with {$mentorName}",
                    "New Online Class for {$courseName}",
                    'online_class',
                    $notificationData
                );
            }

            // Also notify the mentor
            if ($mentor && $mentor->user_id) {
                $this->sendUserNotification(
                    $mentor->user_id,
                    "You have been assigned to teach an online class: {$schedule->title} scheduled for {$classDateTime}",
                    "New Online Class Assignment",
                    'online_class',
                    $notificationData
                );
            }
        }

        return $this->apiResponse($schedule, 'Live Class Schedule Created Successful!', true, 200);
    }


    public function deleteLiveClassSchedule (Request $request) {
        $schedule = ClassSchedule::find($request->id);
        if (!$schedule) {
            return $this->apiResponse(null, 'Live Class Schedule Not Found!', false, 404);
        }
        $mentor = MentorInformation::where('user_id', Auth::user()->id)->first();
        if ($schedule->mentor_id != $mentor->id) {
            return $this->apiResponse(null, 'Unauthorized! You are not allowed to delete this live class schedule', false, 401);
        }

        $schedule->delete();

        return $this->apiResponse(null, 'Live Class Schedule Deleted Successful!', true, 200);
    }


    public function deleteLiveClassScheduleAdmin (Request $request) {
        $schedule = ClassSchedule::find($request->id);
        if (!$schedule) {
            return $this->apiResponse(null, 'Live Class Schedule Not Found!', false, 404);
        }

        $schedule->delete();

        return $this->apiResponse(null, 'Live Class Schedule Deleted Successful!', true, 200);
    }

    public function updateLiveClassSchedule(Request $request)
    {
        $request->validate([
            'id' => 'required',
            'title' => 'required|string',
            'course_id' => 'required|integer|exists:courses,id',
            'schedule_datetime' => 'required|date_format:Y-m-d H:i:s',
            'student_ids' => 'required_without:batch_id|array',
            'batch_id' => 'required_without:student_ids|integer|exists:batches,id',
            'class_url' => 'nullable|string',
        ]);


        $mentor = MentorInformation::where('user_id', Auth::user()->id)->first();
        $endTime = Carbon::parse($request->schedule_datetime)->addMinutes((int) $request->duration)->format('Y-m-d H:i:s');
        $schedule = ClassSchedule::find($request->id);

        if (!$schedule) {
            return $this->apiResponse(null, 'Live Class Schedule Not Found!', false, 404);
        }

        $schedule->title = $request->title;
        $schedule->course_id = $request->course_id;
        $schedule->batch_id = $request->batch_id;
        $schedule->schedule_datetime = $request->schedule_datetime;
        $schedule->class_url = $request->class_url;
        $schedule->has_started = false;
        $schedule->has_completed = false;
        $schedule->start_time = $request->schedule_datetime;
        $schedule->end_time = $endTime;
        $schedule->is_active = isset($request->is_active) ? ($request->is_active ? 1 : 0) : 1;
        $schedule->save();



        ClassScheduleStudents::where('class_schedule_id', $schedule->id)->delete();

        if ($request->student_ids) {
            $scheduleStudents = array_map(function($student_id) use ($schedule) {
                return [
                    'student_id' => $student_id,
                    'class_schedule_id' => $schedule->id,
                ];
            }, $request->input('student_ids', []));

            ClassScheduleStudents::insert($scheduleStudents);

        }

        if ($request->batch_id) {
            $studentIds = BatchStudent::where('batch_id', $request->batch_id)->pluck('student_id')->toArray();
            $scheduleStudents = array_map(function($student_id) use ($schedule) {
                return [
                    'student_id' => $student_id,
                    'class_schedule_id' => $schedule->id,
                ];
            }, $studentIds);

            ClassScheduleStudents::insert($scheduleStudents);
        }
        return $this->apiResponse($schedule, 'Live Class Schedule Updated Successful!', true, 200);

    }

    /**
     * Update a live class schedule by admin and send notifications to students for online classes
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateLiveClassScheduleAdmin (Request $request)
    {
        $request->validate([
            'id' => 'required',
            'title' => 'required|string',
            'mentor_id' => 'required|integer|exists:mentor_informations,id',
            'course_id' => 'required|integer|exists:courses,id',
            'schedule_datetime' => 'required|date_format:Y-m-d H:i:s',
            'student_ids' => 'required_without:batch_id|array',
            'batch_id' => 'required_without:student_ids|integer|exists:batches,id',
            'class_url' => 'nullable|string',
            'is_physical' => 'required',
        ]);

        // Get course and mentor information for notifications
        $course = Course::find($request->course_id);
        $mentor = MentorInformation::find($request->mentor_id);
        $mentorName = $mentor ? User::find($mentor->user_id)->name ?? 'Mentor' : 'Mentor';
        $courseName = $course ? $course->title : 'Course';

        $endTime = Carbon::parse($request->schedule_datetime)->addMinutes((int) $request->duration)->format('Y-m-d H:i:s');
        $schedule = ClassSchedule::find($request->id);

        if (!$schedule) {
            return $this->apiResponse(null, 'Live Class Schedule Not Found!', false, 404);
        }

        // Check if this is a change from physical to online class
        $wasPhysical = $schedule->is_physical;
        $nowOnline = ($request->is_physical == false || $request->is_physical == "0" || $request->is_physical == 0);
        $changedToOnline = ($wasPhysical && $nowOnline);

        // Check if schedule date/time has changed
        $scheduleChanged = $schedule->schedule_datetime != $request->schedule_datetime;

        $schedule->title = $request->title;
        $schedule->mentor_id = $request->mentor_id;
        $schedule->course_id = $request->course_id;
        $schedule->batch_id = $request->batch_id;
        $schedule->schedule_datetime = $request->schedule_datetime;
        $schedule->class_url = $request->class_url;
        $schedule->has_started = false;
        $schedule->has_completed = false;
        $schedule->start_time = $request->schedule_datetime;
        $schedule->end_time = $endTime;
        $schedule->is_physical = $request->is_physical;
        $schedule->is_active = isset($request->is_active) ? ($request->is_active ? 1 : 0) : 1;
        $schedule->save();

        // Collect student IDs based on the request
        $studentIds = [];

        ClassScheduleStudents::where('class_schedule_id', $schedule->id)->delete();

        if ($request->student_ids) {
            $studentIds = $request->input('student_ids', []);
            $scheduleStudents = array_map(function($student_id) use ($schedule) {
                return [
                    'student_id' => $student_id,
                    'class_schedule_id' => $schedule->id,
                ];
            }, $studentIds);

            ClassScheduleStudents::insert($scheduleStudents);
        }

        if ($request->batch_id) {
            $studentIds = BatchStudent::where('batch_id', $request->batch_id)->pluck('student_id')->toArray();
            $scheduleStudents = array_map(function($student_id) use ($schedule) {
                return [
                    'student_id' => $student_id,
                    'class_schedule_id' => $schedule->id,
                ];
            }, $studentIds);

            ClassScheduleStudents::insert($scheduleStudents);
        }

        // If this is an online class or changed to online, or if the schedule changed, send notifications to students
        if ($nowOnline && ($changedToOnline || $scheduleChanged)) {
            // Get user IDs for all students
            $studentUserIds = StudentInformation::whereIn('id', $studentIds)
                ->pluck('user_id')
                ->toArray();

            // Format the class date and time for display
            $classDateTime = Carbon::parse($request->schedule_datetime)->format('l, F j, Y \a\t g:i A');

            // Prepare notification data
            $notificationData = [
                'class_id' => $schedule->id,
                'title' => $schedule->title,
                'course_id' => $request->course_id,
                'course_name' => $courseName,
                'mentor_id' => $request->mentor_id,
                'mentor_name' => $mentorName,
                'schedule_datetime' => $request->schedule_datetime,
                'formatted_datetime' => $classDateTime,
                'duration' => $request->duration,
                'class_url' => $request->class_url,
                'is_physical' => false,
                'is_update' => true
            ];

            // Determine the notification message based on what changed
            $notificationMessage = "";
            $notificationTitle = "";

            if ($changedToOnline) {
                $notificationMessage = "Class '{$schedule->title}' has been changed to an online class scheduled for {$classDateTime} with {$mentorName}";
                $notificationTitle = "Class Changed to Online for {$courseName}";
            } else if ($scheduleChanged) {
                $notificationMessage = "Online class '{$schedule->title}' has been rescheduled to {$classDateTime} with {$mentorName}";
                $notificationTitle = "Online Class Rescheduled for {$courseName}";
            } else {
                $notificationMessage = "Online class '{$schedule->title}' has been updated. Scheduled for {$classDateTime} with {$mentorName}";
                $notificationTitle = "Online Class Updated for {$courseName}";
            }

            // Send notifications to all students
            foreach ($studentUserIds as $studentUserId) {
                $this->sendUserNotification(
                    $studentUserId,
                    $notificationMessage,
                    $notificationTitle,
                    'online_class_update',
                    $notificationData
                );
            }

            // Also notify the mentor if mentor changed or schedule changed
            if ($mentor && $mentor->user_id && ($schedule->mentor_id != $request->mentor_id || $scheduleChanged)) {
                $this->sendUserNotification(
                    $mentor->user_id,
                    "You have been assigned to teach an online class: {$schedule->title} scheduled for {$classDateTime}",
                    "Online Class Assignment Updated",
                    'online_class_update',
                    $notificationData
                );
            }
        }

        return $this->apiResponse($schedule, 'Live Class Schedule Updated Successful!', true, 200);
    }
    public function studentJoinClass(Request $request)
    {
        $class_id = $request->class_id ? $request->class_id : 0;

        if (! $class_id) {
            return response()->json([
                'status' => false,
                'message' => 'Please, select a class',
                'data' => [],
            ], 422);
        }

        return $schedule_details = ClassSchedule::where('id', $class_id)->first();

        if (! $schedule_details->has_started) {
            return response()->json([
                'status' => false,
                'message' => 'You can not join this class! Because this class has not been started yet!!',
                'data' => [],
            ], 422);
        }

        $studentId = Auth::user()->id;
        $student = StudentInformation::where('user_id', $studentId)->first();

        StudentJoinHistory::create([
            'class_schedule_id' => $schedule_details->id,
            'student_id' => $student->id,
            'join_time' => date('Y-m-d H:i:s'),
        ]);

        return response()->json([
            'status' => true,
            'message' => 'Enjoy your class!!',
            'data' => [
                "class_url" => $schedule_details->class_url
            ],
        ], 200);
    }

    public function getStudentClassJoinHistory($startDate, $endDate, $studentId) {
        // student_id

        $assignCount = ClassScheduleStudents::where('student_id', $studentId)
        ->whereHas('classSchedule', function($query) use ($startDate, $endDate) {
            $query->whereBetween('schedule_datetime', [$startDate, $endDate]);
        })
        ->count();

        $joinCount = ClassSchedule::whereHas('studentJoinHistory', function($query) use ($studentId) {
            $query->where('student_id', $studentId);
        })->whereBetween('schedule_datetime', [$startDate, $endDate])->count();


        $assignPercent = ($assignCount > 0) ? round(($joinCount / $assignCount) * 100, 2) : 0;
        return $assignPercent;
    }


    public function getStudentClassCount ($startDate, $endDate, $studentId) {
        // student_id

        return $joinCount = ClassSchedule::whereHas('studentJoinHistory', function($query) use ($studentId) {
            $query->where('student_id', $studentId);
        })->whereBetween('schedule_datetime', [$startDate, $endDate])->count();

    }


    public function getMentorActivitiesPerDay ($mentorId, $startDate, $endDate) {
        $activities = [];

        for ($date = $startDate; $date <= $endDate; $date = date('Y-m-d', strtotime($date . ' +1 day'))) {
            $classCount = ClassSchedule::where('mentor_id', $mentorId)
            ->whereDate('schedule_datetime', $date)
            ->count();

            $assignCount = Assignment::where('mentor_id', $mentorId)
            ->whereDate('created_at', $date)
            ->count();

            $activities[] = [
                'date' => $date,
                'class_count' => $classCount,
                'assignment_count' => $assignCount,
            ];
        }

        return $activities;

    }




    public function allStudentsLiveClass (Request $request) {

        if(!$request->class_id){
            return $this->apiResponse(null, 'Please select a live class', false, 422);
        }
        $courseId = null;


        $classSchedule = ClassSchedule::find($request->class_id);

        $course = Course::find($classSchedule->course_id);

        $list = Payment::where('item_id', $course->id)->pluck('user_id')->toArray();
        $students = StudentInformation::whereIn('user_id', $list)
        ->select('id', 'user_id', 'name', 'email', 'contact_no', 'image')
        ->get();
        foreach ($students as $student) {
            $student->is_selected = ClassScheduleStudents::where('class_schedule_id', $classSchedule->id)->where('student_id', $student->id)->exists();
        }

        return $this->apiResponse($students, 'Student List', true, 200);
    }
}
