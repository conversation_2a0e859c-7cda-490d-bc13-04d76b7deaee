<?php

namespace App\Http\Controllers\Mobile;

use App\Http\Controllers\Controller;
use App\Models\Organization;
use Illuminate\Http\Request;
use App\Http\Resources\Mobile\Organization\DetailsResource;

class OrganizationController extends Controller
{
    protected $model;
    protected $detailsResource;
    protected $detailsMessage = 'Organization details found successfully';

    public function __construct(Request $request)
    {
        $this->model = new Organization();
        $this->detailsResource = new DetailsResource($request);
    }
    
}
