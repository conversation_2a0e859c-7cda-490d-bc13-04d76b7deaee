<?php

namespace App\Http\Controllers\Mobile;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Payment;
use App\Models\Course;
use App\Models\Certificate;
use App\Http\Resources\Mobile\Payment\ListResource;
use App\Http\Resources\Mobile\Payment\PurchaseListResource;
use App\Http\Resources\Mobile\CertificateListResource;
use App\Http\Resources\Mobile\UserCertificateResource;
use \Carbon\Carbon;
use Auth;
// use Intervention\Image\Facades\Image;

use Barryvdh\DomPDF\Facade\Pdf;

class PaymentController extends Controller
{
    //
    protected $model;
    protected $listResource;
    protected $detailsResource;
    protected $listMessage = 'My payment list found successfully';
    protected $detailsMessage = 'Organization details found successfully';
    protected $searchKeys = ['transaction_id'];
    protected $joins = [];

    public function __construct(Request $request)
    {
        $this->model = new Payment();
        $this->listResource = new ListResource($request);
    }

    public function myPaymentList(Request $request)
    {
        $request->merge(['user_id' => $request->user()->id]);
        $request->merge(['pagination' => 'false']);
        return $this->getList($request);
    }

    public function myPurchaseList(Request $request)
    {
        //override
        $this->listMessage = 'My purchase list found successfully';
        $this->listResource = new PurchaseListResource($request);
        $request->merge(['user_id' => $request->user()->id]);
        $request->merge(['pagination' => 'false']);
        return $this->getList($request);
    }

    public function getCertificates(Request $request) {

        $certificates = Payment::where('user_id', $request->user()->id)
            ->whereHas('course', function($query) {
                $query->where('deleted_at', null);
            })
            ->get();

        $this->listMessage = 'Certificate list found successfully';
        return $this->apiResponse(CertificateListResource::collection($certificates), $this->listMessage, true, 200);

    }

    public function getCertificateDetails (Request $request) {
        $this->detailsMessage = 'Organization details found successfully';
        $this->detailsResource = new CertificateListResource($request);
        return $this->getDetails($request);

    }

    public function downloadCertificate (Request $request) {
        $user = Auth::user();

        $certificate = Certificate::where('user_id', $user->id)->where('course_id', $request->course_id)->first();

        if (!$certificate) {
            return $this->apiResponse(null, 'Certificate not found', false, 200);
        }

        return response()->file(public_path('uploads/' . $certificate->certificate_file));
    }


    public function generateCertificate (Request $request) {
        try {

            $request->validate([
                'course_id' => 'required|integer|exists:courses,id',
                'candidate_name' => 'required|string',
            ]);


            // $certificate = Certificate::where('user_id', $request->user()->id)->where('course_id', $request->course_id)->first();

            // if ($certificate) {
            //     return $this->apiResponse(null, 'Certificate already generated', false, 200);
            // }

            $course = Course::find($request->course_id);
            $template = $course->certificateTemplate;

            $data['name'] = $request->candidate_name;
            $data['course_id'] = $request->course_id;
            $data['user_id'] = $request->user()->id;
            $data['course_name'] = $course->title;
            $data['background_image'] = $template->background_image;
            $data['logo'] = $template->logo;
            $data['certification_text'] = $template->certification_text;
            $data['signature'] = $template->signature;
            $data['authorize_person'] = $template->authorize_person;
            $data['designation'] = $template->designation;

            $data['code'] = $this->generateCertificateCode($template, $request->course_id, $request->user()->id);

            $data['certificate_file'] = $this->generateCertificatePdf($data);
            // return $data;
            $certificate = Certificate::create($data);

            return $this->apiResponse(new UserCertificateResource($certificate), 'Certificate generated successfully', true, 200);


        } catch (\Exception $e) {
            return $this->apiResponse(null, $e->getMessage(), false, 400);
        }
    }


    protected function generateCertificateCode ($template, $courseId, $userId) {
        $lastCertificate = Certificate::where('course_id', $courseId)->orderBy('id', 'desc')
        ->whereYear('created_at', Carbon::now()->year)
        ->first();

        if ($lastCertificate) {
            $lastIncrement = (int)substr($lastCertificate->code, -4);
            $code = str_pad($lastIncrement + 1, 4, '0', STR_PAD_LEFT);
        } else {
            $code = 1001;
        }
        $code = Carbon::now()->year . $code ;
        return $template->prefix . $code;
    }



    public function generateCertificatePdf($data)
    {
        $directoryPath = public_path('certificates');

        if (!file_exists($directoryPath)) {
            mkdir($directoryPath, 0777, true);
        }
        $userName = str_replace(' ', '_', $data['name']);
        // $imagePath = $this->generateCertificateImage($userName); // Call the function to generate image
        $imagePath = public_path('uploads/certificates/certificate_bg.jpeg');
        // $data = [
        //     'userName' => $userName,
        //     'imagePath' => $imagePath
        // ];
        $pdf = Pdf::loadView('certificates.pdf_template', $data);
        $path = "certificates/certificate_". $userName ."_" . Carbon::now()->timestamp.".pdf";
        $pdfPath = public_path('uploads/'.$path);
        $pdf->save($pdfPath);

        return $path;
    }



}
