<?php

namespace App\Http\Controllers\Mobile;

use App\Http\Controllers\Controller;
use App\Models\CourseOutline;
use App\Models\Payment;
use App\Models\CourseParticipant;
use App\Models\StudentInformation;
use Illuminate\Http\Request;
use App\Http\Resources\Mobile\ContentDetailsResource as DetailsResource;
use App\Http\Resources\Mobile\VideoDetailsResource;
use App\Http\Resources\Website\VideoDetailsResource as WebsiteVideoDetailsResource;
use App\Http\Resources\Website\ScriptDetailsResource as WebsiteScriptDetailsResource;
use App\Http\Controllers\Mobile\ContentWatchLogController;

class ContentController extends Controller
{
    protected $model;
    protected $detailsResource;
    protected $detailsMessage = 'Course Content found successfully';

    public function __construct(Request $request)
    {
        $this->model = new CourseOutline();
        $this->detailsResource = new DetailsResource($request);
    }

    public function getContentDetails(Request $request) {
        $content = $this->model->find($request->id);
        if (empty($content)) {
            return $this->apiResponse( [], 'Course Content not found', false, 404);
        } else {

                // If token is present, try to find the associated user
               $user = $this->checkTokenUser($request);
                if ($user) {
                    // return $this->isBought($user, $content->course_id);
                    if ($this->isBought($user, $content->course_id)) {
                        $videoWatchLogController = new ContentWatchLogController($request);
                        if (!empty($content->chapter_video_id)) {
                            $videoWatchLogController->storeVideoWatchStarted([
                                'user_id' => $user->id,
                                'organization_id' => $content->organization_id,
                                'course_id' => $content->course_id,
                                'course_outline_id' => $content->id,
                                'chapter_video_id' => $content->chapter_video_id
                            ]);
                            $data = new VideoDetailsResource($content);
                            // $data->next_content = $videoWatchLogController->getNextContent($content->id);
                            return $this->apiResponse( $data, 'Course Content found successfully', true, 200);
                        }
                        if (!empty($content->chapter_script_id)) {
                            $videoWatchLogController->storeScriptWatchStarted([
                                'user_id' => $user->id,
                                'organization_id' => $content->organization_id,
                                'course_id' => $content->course_id,
                                'course_outline_id' => $content->id,
                                'chapter_script_id' => $content->chapter_script_id
                            ]);
                            return $this->apiResponse( new DetailsResource($content), 'Course Content found successfully', true, 200);
                        }
                        return $this->apiResponse( new DetailsResource($content), 'Course Content found successfully', true, 200);
                    } else {
                        if ($content->is_free) {
                            if ($content->chapter_video_id) {
                                return $this->apiResponse( new VideoDetailsResource($content), 'Course Content found successfully', true, 200);
                            }
                            return $this->apiResponse( new DetailsResource($content), 'Course Content found successfully', true, 200);

                        }

                    return $this->apiResponse( null, 'This is a premium content, please buy the course', false, 200);
                    }
                } else {
                    if ($content->is_free) {
                        if ($content->chapter_video_id) {
                            return $this->apiResponse( new VideoDetailsResource($content), 'Course Content found successfully', true, 200);
                        }

                        return $this->apiResponse( new DetailsResource($content), 'Course Content found successfully', true, 200);
                    }
                }

                // If token is invalid or not present, return an unauthorized response

                return $this->apiResponse( null, 'This is a premium content, please buy the course', false, 200);
            }
        }


    public function getWebContentDetails(Request $request) {
        $content = $this->model->find($request->id);
        if (empty($content)) {
            return $this->apiResponse( [], 'Course Content not found', false, 404);
        }

        // Check if content is locked and get completion status
        $contentStatus = $this->getContentLockingStatus($content, $request);

        // If content is locked, return locked message
        if ($contentStatus['is_locked'] && !$content->is_free) {
            return $this->apiResponse( null, 'This content is locked. Please complete previous content to unlock.', false, 403);
        }

        // If token is present, try to find the associated user
        $user = $this->checkTokenUser($request);
        if ($user) {
            if ($this->isBought($user, $content->course_id)) {
                $videoWatchLogController = new ContentWatchLogController($request);
                if (!empty($content->chapter_video_id)) {
                    $videoWatchLogController->storeVideoWatchStarted([
                        'user_id' => $user->id,
                        'organization_id' => $content->organization_id,
                        'course_id' => $content->course_id,
                        'course_outline_id' => $content->id,
                        'chapter_video_id' => $content->chapter_video_id
                    ]);
                    $data = new WebsiteVideoDetailsResource($content);
                    // Create response with additional status fields
                    $response = $this->apiResponse( $data, 'Course Content found successfully', true, 200);
                    $responseData = $response->getData(true);
                    $responseData['data']['is_completed'] = $contentStatus['is_completed'];
                    $responseData['data']['is_locked'] = $contentStatus['is_locked'];
                    return response()->json($responseData, 200);
                }
                if (!empty($content->chapter_script_id)) {
                    $videoWatchLogController->storeScriptWatchStarted([
                        'user_id' => $user->id,
                        'organization_id' => $content->organization_id,
                        'course_id' => $content->course_id,
                        'course_outline_id' => $content->id,
                        'chapter_script_id' => $content->chapter_script_id
                    ]);
                    $data = new WebsiteScriptDetailsResource($content);
                    // Create response with additional status fields
                    $response = $this->apiResponse( $data, 'Course Content found successfully', true, 200);
                    $responseData = $response->getData(true);
                    $responseData['data']['is_completed'] = $contentStatus['is_completed'];
                    $responseData['data']['is_locked'] = $contentStatus['is_locked'];
                    return response()->json($responseData, 200);
                }
                $data = new DetailsResource($content);
                // Create response with additional status fields
                $response = $this->apiResponse( $data, 'Course Content found successfully', true, 200);
                $responseData = $response->getData(true);
                $responseData['data']['is_completed'] = $contentStatus['is_completed'];
                $responseData['data']['is_locked'] = $contentStatus['is_locked'];
                return response()->json($responseData, 200);
            } else {
                if ($content->is_free) {
                    if ($content->chapter_video_id) {
                        $data = new WebsiteVideoDetailsResource($content);
                        $response = $this->apiResponse( $data, 'Course Content found successfully', true, 200);
                        $responseData = $response->getData(true);
                        $responseData['data']['is_completed'] = $contentStatus['is_completed'];
                        $responseData['data']['is_locked'] = $contentStatus['is_locked'];
                        return response()->json($responseData, 200);
                    }

                    if ($content->chapter_script_id) {
                        $data = new WebsiteScriptDetailsResource($content);
                        $response = $this->apiResponse( $data, 'Course Content found successfully', true, 200);
                        $responseData = $response->getData(true);
                        $responseData['data']['is_completed'] = $contentStatus['is_completed'];
                        $responseData['data']['is_locked'] = $contentStatus['is_locked'];
                        return response()->json($responseData, 200);
                    }
                    $data = new DetailsResource($content);
                    $response = $this->apiResponse( $data, 'Course Content found successfully', true, 200);
                    $responseData = $response->getData(true);
                    $responseData['data']['is_completed'] = $contentStatus['is_completed'];
                    $responseData['data']['is_locked'] = $contentStatus['is_locked'];
                    return response()->json($responseData, 200);
                }

                return $this->apiResponse( null, 'This is a premium content, please buy the course', false, 200);
            }
        } else {
            if ($content->is_free) {
                if ($content->chapter_video_id) {
                    $data = new WebsiteVideoDetailsResource($content);
                    $response = $this->apiResponse( $data, 'Course Content found successfully', true, 200);
                    $responseData = $response->getData(true);
                    $responseData['data']['is_completed'] = $contentStatus['is_completed'];
                    $responseData['data']['is_locked'] = $contentStatus['is_locked'];
                    return response()->json($responseData, 200);
                }

                if ($content->chapter_script_id) {
                    $data = new WebsiteScriptDetailsResource($content);
                    $response = $this->apiResponse( $data, 'Course Content found successfully', true, 200);
                    $responseData = $response->getData(true);
                    $responseData['data']['is_completed'] = $contentStatus['is_completed'];
                    $responseData['data']['is_locked'] = $contentStatus['is_locked'];
                    return response()->json($responseData, 200);
                }
                $data = new DetailsResource($content);
                $response = $this->apiResponse( $data, 'Course Content found successfully', true, 200);
                $responseData = $response->getData(true);
                $responseData['data']['is_completed'] = $contentStatus['is_completed'];
                $responseData['data']['is_locked'] = $contentStatus['is_locked'];
                return response()->json($responseData, 200);
            }
        }

        return $this->apiResponse( null, 'This is a premium content, please buy the course', false, 200);
    }

    public function isBought($user, $courseId) {
        // $student = StudentInformation::where('user_id', $user->id)->first();
        return Payment::where('user_id', $user->id)->where('item_id', $courseId)->where('is_approved', 1)->exists();
    }

    /**
     * Get content locking and completion status
     */
    private function getContentLockingStatus($content, $request)
    {
        $user = $this->checkTokenUser($request);
        $isCompleted = false;
        $isLocked = false;

        if ($user && $user->user_type === 'Student') {
            // Get completed items for this user and course
            $completedVideos = \App\Models\VideoWatchLog::where('course_id', $content->course_id)
                ->where('user_id', $user->id)
                ->where('is_watched', 1)
                ->pluck('course_outline_id')
                ->toArray();

            $completedScripts = \App\Models\ScriptViewLog::where('course_id', $content->course_id)
                ->where('user_id', $user->id)
                ->where('is_watched', 1)
                ->pluck('course_outline_id')
                ->toArray();

            $completedTests = \App\Models\ChapterQuizResult::where('chapter_quiz_results.course_id', $content->course_id)
                ->where('chapter_quiz_results.user_id', $user->id)
                ->where('submission_status', 'Submitted')
                ->join('course_outlines', 'chapter_quiz_results.chapter_quiz_id', '=', 'course_outlines.chapter_quiz_id')
                ->pluck('course_outlines.id')
                ->toArray();

            $completedItems = array_unique(array_merge($completedVideos, $completedScripts, $completedTests));
            $isCompleted = in_array($content->id, $completedItems);

            // Get all outlines for this course in sequence order
            $course = \App\Models\Course::find($content->course_id);
            $allOutlines = $course->courseCategory
                ->whereNull('subject_id')
                ->sortBy('sequence')
                ->flatMap(function ($category) {
                    return $category->courseOutlines->sortBy('sequence');
                });

            // Apply sequential locking logic
            $previousCompleted = true; // First item should be unlocked
            foreach ($allOutlines as $outline) {
                if ($outline->id == $content->id) {
                    $isLocked = !$outline->is_free && !$previousCompleted;
                    break;
                }
                $previousCompleted = in_array($outline->id, $completedItems) || $outline->is_free;
            }
        }

        return [
            'is_completed' => $isCompleted,
            'is_locked' => $isLocked
        ];
    }



}
