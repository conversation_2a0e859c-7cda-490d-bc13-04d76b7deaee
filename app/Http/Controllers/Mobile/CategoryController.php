<?php

namespace App\Http\Controllers\Mobile;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Category;
use App\Http\Resources\Mobile\CategoryResource;

class CategoryController extends Controller
{
    protected $listMessage = 'Course list by category found successfully';
    protected $joins = [];

    public function __construct(Request $request)
    {
        $this->model = new Category();
        $this->listResource = new CategoryResource($request);
    }

    public function getList(Request $request)
    {
        $categoryList = $this->model->where('organization_id', $request->organization_id)->whereHas('courses', function ($query) {
            $query->where('is_active', 1);
        })->get();

        $courseController = new CourseController($request);
        $boughtCourseIds = $courseController->getPurchasedCourseIds();

        return $this->apiResponse( [
            "data" => $this->listResource->collection($categoryList),
            'bought_items' => $boughtCourseIds,
        ],
         $this->listMessage, true, 200);

    }

    // for mobile
    public function getListForMobile(Request $request)
    {
        $categoryList = $this->model->where('organization_id', $request->organization_id)->whereHas('courses', function ($query) {
            $query->where('is_active', 1);
        })->get();

        $courseController = new CourseController($request);
        $boughtCourseIds = $courseController->getPurchasedCourseIds();

        return $this->apiResponse( $this->listResource->collection($categoryList),
         $this->listMessage, true, 200);

    }
}
