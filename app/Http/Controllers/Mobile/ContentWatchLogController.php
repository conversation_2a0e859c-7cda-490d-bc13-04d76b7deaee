<?php

namespace App\Http\Controllers\Mobile;

use App\Http\Controllers\Controller;
use App\Models\VideoWatchLog;
use App\Models\ScriptViewLog;
use App\Models\CourseOutline;
use App\Models\ChapterQuizResult;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Http\Resources\Mobile\Dashboard\LastOutlineResource;

class ContentWatchLogController extends Controller
{

    protected $model;

    public function __construct(Request $request)
    {
        $this->model = new VideoWatchLog();
        $this->modelScriptWatch = new ScriptViewLog();
    }


    public function storeVideoWatchStarted ($data) {
        $exist = $this->model->where('user_id', $data['user_id'])
                            ->where('course_id', $data['course_id'])
                            ->where('course_outline_id', $data['course_outline_id'])
                            ->first();
        if ($exist) {
            return true;
        }
        $data['is_started'] = true;
        $data['is_watched'] = false;

        // return $data;
        return $this->model->create($data);

    }

    public function markVideoCompleted (Request $request) {

        $validator = Validator::make($request->all(), [
            'content_id' => 'required',
        ]);

        if ($validator->fails()) {
            return $this->apiResponse(null, $validator->messages(), false, 422);
        }

        $user = auth()->user();
        $videoWatchLog = $this->model->where('user_id', $user->id)
            ->where('course_outline_id', $request->content_id)
            ->first();
        if ($videoWatchLog && $videoWatchLog->is_watched == false) {
            $videoWatchLog->completed_time = now();
            $videoWatchLog->is_watched = true;
            $videoWatchLog->save();

            // Update CourseStudent video watch count
            $this->updateCourseStudentVideoCount($user->id, $videoWatchLog->course_id);
        }

        return $this->apiResponse( null, 'You have completed this video', true, 200);
     }


    public function storeScriptWatchStarted ($data) {
        $exist = $this->modelScriptWatch->where('user_id', $data['user_id'])
                            ->where('course_id', $data['course_id'])
                            ->where('course_outline_id', $data['course_outline_id'])
                            ->first();
        if ($exist) {
            return true;
        }
        $data['is_started'] = true;
        $data['is_watched'] = false;

        // return $data;
        return $this->modelScriptWatch->create($data);

    }

    public function markScriptCompleted (Request $request) {

        $validator = Validator::make($request->all(), [
            'content_id' => 'required',
        ]);

        if ($validator->fails()) {
            return $this->apiResponse(null, $validator->messages(), false, 422);
        }

        $user = auth()->user();
        $scriptWatchLog = $this->modelScriptWatch->where('user_id', $user->id)
            ->where('course_outline_id', $request->content_id)
            ->first();
        if ($scriptWatchLog && $scriptWatchLog->is_watched == false) {
            $scriptWatchLog->completed_time = now();
            $scriptWatchLog->is_watched = true;
            $scriptWatchLog->save();

            // Update CourseStudent script watch count
            $this->updateCourseStudentScriptCount($user->id, $scriptWatchLog->course_id);
        }

        return $this->apiResponse( null, 'You have completed this script', true, 200);
     }


     public function getLastAccessedContent () {

        $userId = auth()->user()->id;
        $content = null;
        $videoWatchLog = $this->model::where('user_id', $userId)->latest()->first();
        $scriptWatchLog = $this->modelScriptWatch::where('user_id', $userId)->latest()->first();

        $quizLog = ChapterQuizResult::where('user_id', $userId)
        ->where('submission_status', 'Started')
        ->orderBy('id', 'desc')->first();


        // Assume $videoWatchLog, $scriptWatchLog, and $quizLog are objects or null
        $logs = collect([$videoWatchLog, $scriptWatchLog, $quizLog])
                    ->filter() // Remove null values
                    ->sortByDesc('created_at'); // Sort by created_at in descending order

        $latestLog = $logs->first(); // Get the latest log
        if (!empty($latestLog)) {
            if ($latestLog->course_outline_id) {
                $content = CourseOutline::where('id', $latestLog->course_outline_id)->first();
            } else if ($latestLog->chapter_quiz_id) {
                $content = CourseOutline::where('chapter_quiz_id', $latestLog->chapter_quiz_id)->first();
            }

        }

        return $content ? new LastOutlineResource($content) : null;
     }



     public function getCourseLastAccessedContent ($user, $courseId) {

        $userId = $user->id;
        $content = null;
        $videoWatchLog = $this->model::where('user_id', $userId)->where('course_id', $courseId)->latest()->first();
        $scriptWatchLog = $this->modelScriptWatch::where('user_id', $userId)->where('course_id', $courseId)->latest()->first();

        $quizLog = ChapterQuizResult::where('user_id', $userId)
        ->where('course_id', $courseId)
        ->orderBy('id', 'desc')->first();


        // Assume $videoWatchLog, $scriptWatchLog, and $quizLog are objects or null
        $logs = collect([$videoWatchLog, $scriptWatchLog, $quizLog])
                    ->filter() // Remove null values
                    ->sortByDesc('created_at'); // Sort by created_at in descending order

        $latestLog = $logs->first(); // Get the latest log
        if (!empty($latestLog)) {
            if ($latestLog->course_outline_id) {
                $content = CourseOutline::where('id', $latestLog->course_outline_id)->first();
            } else if ($latestLog->chapter_quiz_id) {
                $content = CourseOutline::where('chapter_quiz_id', $latestLog->chapter_quiz_id)->first();
            }

        }

        return $content ? new LastOutlineResource($content) : null;
     }
     public function getVideoCount ($startDate, $endDate, $user_id ) {
        return VideoWatchLog::where('user_id', $user_id)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();
     }

     public function getScriptCount ($startDate, $endDate, $user_id ) {
        return ScriptViewLog::where('user_id', $user_id)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();
     }

     /**
      * Update CourseStudent video watch count
      */
     private function updateCourseStudentVideoCount($userId, $courseId)
     {
         try {
             $student = \App\Models\StudentInformation::where('user_id', $userId)->first();
             if ($student) {
                 \App\Models\CourseStudent::where('course_id', $courseId)
                     ->where('student_id', $student->id)
                     ->increment('video_watch_count');
             }
         } catch (\Exception $e) {
             \Log::error('Failed to update CourseStudent video count: ' . $e->getMessage());
         }
     }

     /**
      * Update CourseStudent script watch count
      */
     private function updateCourseStudentScriptCount($userId, $courseId)
     {
         try {
             $student = \App\Models\StudentInformation::where('user_id', $userId)->first();
             if ($student) {
                 \App\Models\CourseStudent::where('course_id', $courseId)
                     ->where('student_id', $student->id)
                     ->increment('script_watch_count');
             }
         } catch (\Exception $e) {
             \Log::error('Failed to update CourseStudent script count: ' . $e->getMessage());
         }
     }
}
