<?php

namespace App\Http\Controllers\Mobile;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\StudentInformation;
use App\Models\User;
use App\Http\Resources\Mobile\Student\DetailsResource;
use App\Http\Requests\Mobile\Student\ProfileUpdateRequest;
use Auth;
use DB;
class StudentController extends Controller
{

    protected $detailsResource;
    protected $detailsMessage = 'Student found successfully';

    public function __construct(Request $request)
    {
        $this->model = new StudentInformation();
        $this->detailsResource = new DetailsResource($request);
    }


    public function studentDetails (Request $request)
    {

        $result = $this->model::where('user_id', $request->user()->id)->first();
        if (empty($result)) {
            return $this->apiResponse([], 'No data found', false, 404);
        }
        return $this->apiResponse( new $this->detailsResource($result), $this->detailsMessage, true, 200);
    }


    public function updateProfile(ProfileUpdateRequest $request)
    {
        try {
            DB::beginTransaction();
            $id = Auth::user()->id;
            $studentInfo = StudentInformation::where('user_id', $id)->first();
            $user = User::findOrFail($studentInfo->user_id);

            if ($request->contact_no) {
                if ($user->phone_verified_at && $user->contact_no != $request->contact_no) {
                    return $this->apiResponse(null, 'Phone number already verified', false, 403);
                }
            }

            if ($request->email && $user->email != $request->email) {
                if ($user->email_verified_at) {
                    return $this->apiResponse(null, 'Email already verified', false, 403);
                }
            }

            $user->update($request->only(['name', 'email', 'contact_no', 'address']));

            if ($request->hasFile('image')) {
                $user->update(['image' => $this->imageUpload($request, 'image', 'image')]);
            }

            $student = $request->only([
                'student_id',
                'education',
                'institute',
                'device_id',
                'alternative_contact_no',
                'gender',
                'blood_group',
                'bio',
                'father_name',
                'mother_name',
                'religion',
                'marital_status',
                'date_of_birth',
                'current_address',
                'permanent_address',
                'interests',
                'division_id',
                'city_id',
                'area_id',
                'nid_no',
                'birth_certificate_no',
                'passport_no',
                'status',
                'is_foreigner',
                'is_active',
                'rating',
            ]);

            $studentInfo->update(array_merge(
                $student,
                [
                    'name' => $user->name,
                    'email' => $user->email,
                    'contact_no' => $user->contact_no,
                    'address' => $user->address,
                    'image' => $user->image,
                ]
            ));

            DB::commit();

            return $this->apiResponse(new $this->detailsResource($studentInfo), 'Profile Updated Successfully', true, 200);
        } catch (\Throwable $th) {
            DB::rollBack();

            return $this->apiResponse($th->getMessage(), 'Something went wrong', false, 500);
        }
    }
}
