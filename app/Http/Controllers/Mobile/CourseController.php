<?php

namespace App\Http\Controllers\Mobile;

use App\Http\Controllers\Controller;
use Laravel\Sanctum\PersonalAccessToken;
use App\Models\Course;
use App\Models\CourseOutline;
use App\Models\Payment;
use App\Models\ChapterQuiz;
use App\Models\ChapterQuizQuestion;
use App\Models\ChapterQuizResult;
use App\Models\ChapterQuizWrittenAttachment;
use App\Models\ChapterQuizWrittenQuestion;
use App\Models\ChapterQuizWrittenMark;
use App\Models\ChapterQuizResultAnswer;
use App\Models\ChapterQuizItem;
use App\Models\StudentInformation;
use App\Models\VideoWatchLog;
use App\Models\ScriptViewLog;
use Illuminate\Http\Request;
use App\Http\Resources\Mobile\Course\ListResource;
use App\Http\Resources\Mobile\MentorOutlineDetailsResource;
use App\Http\Resources\Mobile\MentorOutlineQuizDetailsResource;
use App\Http\Resources\Mobile\Course\MyCourseListResource;
use App\Http\Resources\Mobile\Course\DetailsResource;
use App\Http\Resources\Mobile\Course\MentorDetailsResource;
use App\Http\Resources\Mobile\Course\MentorCategoryResource;
use App\Http\Resources\Mobile\Course\CategoryResource;
use App\Http\Resources\Mobile\ResultDetailsResource;
use App\Http\Resources\Mobile\ResultListResource;
use App\Http\Resources\CoursePaymentResource;
use App\Http\Resources\Mobile\Organization\MyCourseListResource as OrganizationMyCourseListResource;
use App\Http\Controllers\AssignmentController;
use App\Http\Controllers\CourseController as MasterCourseController;

use App\Http\Resources\Mobile\Course\RatingResource;
use App\Http\Traits\CourseDetailsSerializeTrait;
// use App\Http\Resources\Mobile\Course\CategoryResource;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

class CourseController extends Controller
{
    use CourseDetailsSerializeTrait;
    protected $model;
    protected $listResource;
    protected $detailsResource;
    protected $listMessage = 'Course list found successfully';
    protected $detailsMessage = 'Course found successfully';

    protected $searchKeys = ['courses.title',
    'courses.title_bn',
    'courses.description',
    'organizations.name'];
    protected $joins = [
        'organizations' => ['organizations.id', '=', 'courses.organization_id']
    ];
    public function __construct(Request $request)
    {
        $this->model = new Course();
        $this->detailsResource = new DetailsResource($request);
        $this->listResource = new ListResource($request);
    }


    public function getList(Request $request)
    {
        // Base query construction
        $query = $this->buildBaseQuery($request);

        // Handle pagination
        if ($request->input('pagination') == 'false') {
            $data = $this->listResource::collection($query->get());
            return $this->apiResponse($data, $this->listMessage, true, 200);
        }

        // Paginated results
        $itemsPerPage = $request->input('items_per_page', 10);
        $currentPage = $request->input('current_page', 0);
        $results = $query->paginate($itemsPerPage, ['*'], 'page', $currentPage + 1);

        $boughtCourseIds = $this->getPurchasedCourseIds();

        $courseList = $this->listResource::collection($results);


        return $this->apiResponse([
            'data' => $courseList,
            'bought_items' => $boughtCourseIds,
            'total' => $results->total(),
            'per_page' => $results->perPage(),
            'current_page' => $results->currentPage() - 1,
            'last_page' => $results->lastPage() - 1,
            'from' => $results->firstItem() - 1,
            'to' => $results->lastItem() - 1
        ], $this->listMessage, true, 200);
    }

    protected function buildBaseQuery(Request $request)
    {
        $query = $this->model::query()->select($this->model->getTable() . '.*');

        if (method_exists($this->model, 'scopeIsActive')) {
            $query->isActive();
        }

        foreach ($this->joins as $table => $on) {
            $query->join($table, $on[0], $on[1], $on[2]);
        }

        $query->where(function($q) use ($request) {
            // Search handling
            if ($request->search) {
                $search = $request->input('search');
                $q->where(function($subQuery) use ($search) {
                    foreach ($this->searchKeys as $key) {
                        $subQuery->orWhere($key, 'like', '%' . $search . '%');
                    }
                });
            }

            // Filter handling
            foreach ($request->except(['search', 'pagination', 'itemsPerPage', 'currentPage']) as $key => $value) {
                if (in_array($key, $this->model->getFillable()) && $value) {
                    $q->where($key, $value);
                }
            }
        });

        return $query;
    }

    public function getPurchasedCourseIds(): array
    {
        if (!auth('sanctum')->check()) {
            return [];
        }

        return Payment::where('user_id', auth('sanctum')->id())
            ->whereHas('course', function($query) {
                $query->whereNull('deleted_at');
            })
            ->where('is_approved', 1)
            ->pluck('item_id')
            ->toArray();


    }

    public function getDetails (Request $request) {



        $course = Course::find($request->id);

        if (empty($course)) {
            return $this->apiResponse(null, 'No course found', false, 404);
        }

        return $this->apiResponse($this->getCourseDetails($request, $course), 'Course details with subjects', true, 200);

    }

    /**
     * Retrieve course details by course slug.
     *
     * This method fetches the course using the provided slug. If the course is found,
     * it constructs a DetailsResource instance with the course data. If the authenticated
     * user is a student, it also gathers the list of completed course items (videos, scripts,
     * and quizzes) and includes this data in the response.
     *
     * @param string $slug The slug of the course to be retrieved.
     * @return \Illuminate\Http\JsonResponse The API response containing course details or an error message.
     */

    public function getDetailsBySlug (Request $request, $slug) {
        $course = Course::where('slug', $slug)->first();


        if (empty($course)) {
            return $this->apiResponse(null, 'No course found', false, 404);
        }

        return $this->apiResponse($this->getCourseDetails($request, $course), 'Course details with subjects', true, 200);




        // return $this->apiResponse($data, 'Course details with subjects', true, 200);
    }

    public function getMyCourseList (Request $request) {


        $list = Payment::where('user_id', $request->user()->id)
        ->whereHas('course', function($query) {
            $query->where('deleted_at', null);
        })
        ->where('organization_id', $request->user()->organization_id)
        ->where('is_approved', 1)
        ->pluck('item_id')->toArray();

        $data = Course::whereIn('id', $list)->get();

        return $this->apiResponse( MyCourseListResource::collection($data), $this->listMessage, true, 200);

    }

    public function getRatingList (Request $request) {
        if(!$request->course_id)
            return $this->apiResponse(null, 'Course id is required', false, 422);
        $course = $this->model::find($request->course_id);

        if(!$course)
            return $this->apiResponse(null, 'Course not found', false, 404);

        return $this->apiResponse( RatingResource::collection($course->courseRatings), 'Rating List', true, 200);
    }
    /**
     * Get the details of a course by mentor.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function mentorCourseDetails(Request $request)
    {
        $this->detailsResource = new MentorDetailsResource($request);
        return $this->getDetails($request);
    }


    /**
     * Get the details of a course by mentor.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function mentorCourseOutlines(Request $request)
    {
        $course = $this->model::with('courseCategory')->find($request->course_id);

        if(!$course) {
            return $this->apiResponse([], 'Course not found', false, 404);
        }
        // return $course;
        $data =  MentorCategoryResource::collection($course->courseCategory);

        return $this->apiResponse( $data, 'Course contents', true, 200);
    }

    public function mentorCourseContentsDetails (Request $request) {
        if (!$request->content_id) {
            return $this->apiResponse([], 'Please, supply a outline id', false, 422);
        }

        $outline = CourseOutline::find($request->content_id);
        if (!$outline) {
            return $this->apiResponse([], 'Course outline not found', false, 404);
        }

        $data =  new MentorOutlineDetailsResource($outline);
        return $this->apiResponse($data, 'Course outline contents', true, 200);

    }

    public function mentorQuizDetails (Request $request) {
        if (!$request->content_id) {
            return $this->apiResponse([], 'Please, supply a outline id', false, 422);
        }

        $outline = CourseOutline::find($request->content_id);
        if (!$outline) {
            return $this->apiResponse([], 'Course outline not found', false, 404);
        }
        if ($outline->chapter_quiz_id) {
            $data =  new MentorOutlineQuizDetailsResource($outline);
            return $this->apiResponse($data, 'Course outline contents', true, 200);
        } else {
            return $this->apiResponse([], 'This content is not a quiz', false, 404);
        }

    }

    /**
     * Get the payment details of a course.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getPaymentDetails(Request $request)
    {
        $course = $this->model::find($request->id);
        if (empty($course)) {
            return $this->apiResponse([], 'Course not found', false, 404);
        }
        return $this->apiResponse( new CoursePaymentResource($course),
        'Course payment details',
        true, 200);
        // Write your code here to fetch payment details of a course
    }



    public function chapterQuizDetails(Request $request)
    {
        try {
            $chapter_quiz_id = $request->quiz_id ? $request->quiz_id : 0;

            if (! $chapter_quiz_id) {
                return $this->apiResponse(null, 'Please, attach Quiz ID', false, 422);
            }

            if ($chapter_quiz_id) {
                $quiz_details = ChapterQuiz::where('chapter_quizzes.id', $chapter_quiz_id)
                    ->select('chapter_quizzes.*')

                    ->first();

                $quiz_details->written_question = null;

                $questions = ChapterQuizQuestion::inRandomOrder()
                ->where('chapter_quiz_id', $chapter_quiz_id)

                ->limit($quiz_details->number_of_question)
                ->get();


                $quiz_details->questions = $questions;
            }

            return $this->apiResponse($quiz_details, 'Quiz Details', true, 200);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Something went wrong', 500);
        }
    }

    public function startQuiz(Request $request)
    {
        $user_id = $request->user()->id;
        $course_id = $request->course_id ? $request->course_id : 0;
        $chapter_quiz_id = $request->quiz_id ? $request->quiz_id : 0;

        if (!$course_id || !$chapter_quiz_id) {
            return response()->json([
                'status' => false,
                'message' => 'Please, attach ID',
                'data' => [],
            ], 422);
        }

        if ($chapter_quiz_id) {

            $quiz_details = ChapterQuiz::where('id', $chapter_quiz_id)->first();

            $result = ChapterQuizResult::where('user_id', $user_id)
            ->where('chapter_quiz_id', $chapter_quiz_id)
            ->where('submission_status', 'Started')
            ->orderBy('id', 'desc')->first();

            if (!$result) {
                $result = ChapterQuizResult::create([
                    'user_id' => $user_id,
                    'chapter_quiz_id' => $chapter_quiz_id,
                    'course_id' => $course_id,
                    'end_time' => now()->addMinutes($quiz_details->duration),
                    'mark' => 0,
                ]);
            }
            // return $this->apiResponse($quiz_details, 'Quiz Details', true, 200);


            $quiz_details->written_questions = $quiz_details->writtenQuestions;

            $items = ChapterQuizItem::where('chapter_quiz_id', $chapter_quiz_id)
            ->with([
                'chapterQuizQuestion',
                'trueFalse',
                'matching',
                'matching.matchingAnswers',
                'fillInTheBlank',
                'fillInTheBlank.blankAnswers:id,fill_in_the_blank_question_id,blank_key'
            ])
            ->get();

            foreach ($items as $item) {
                if ($item->type === 'matching' && $item->matching) {
                    // Create a collection of matching answers without right_item
                    $matchingAnswersWithoutRightItem = $item->matching->matchingAnswers->map(function ($answer) {
                        $answerArray = $answer->toArray();
                        unset($answerArray['right_item']);
                        return $answerArray;
                    });

                    // Store the random right items separately
                    $item->matching->random_right_items = $item->matching->matchingAnswers->pluck('right_item')->shuffle();

                    // Replace the original matchingAnswers with the version without right_item
                    $item->matching->setRelation('matchingAnswers', collect($matchingAnswersWithoutRightItem));
                }
            }

            // $questions = ChapterQuizQuestion::inRandomOrder()
            // ->where('chapter_quiz_id', $chapter_quiz_id)
            // ->select([
            //     "id",
            //     "chapter_quiz_id",
            //     "question_text",
            //     "question_text_bn",
            //     "question_image",
            //     "option1",
            //     "option2",
            //     "option3",
            //     "option4",
            //     "option1_image",
            //     "option2_image",
            //     "option3_image",
            //     "option4_image",
            //     "explanation_text",
            //     "explanation_image"
            // ])
            // ->limit($quiz_details->number_of_question)
            // ->get();


            $quiz_details->result = $result;
            $quiz_details->items = $items;
        }


        return $this->apiResponse($quiz_details, 'Quiz Started', true, 200);

    }


    public function submitQuizAnswer(Request $request)
    {
        $courseController = new MasterCourseController();
        return $courseController->submitQuizAnswer($request);
    }

    public function quizAnswerDetails (Request $request, $result_id) {
        $courseController = new MasterCourseController();
        return $courseController->quizAnswerDetails($request, $result_id);
    }
    
    public function submitWrittenAnswer(Request $request)
    {
        try {
            $user_id = Auth::id();
            $result_id = $request->result_id ?: 0;

            // Find the quiz result
            $result = ChapterQuizResult::find($result_id);
            if (!$result) {
                return $this->errorResponse([], 'Please, Start Exam properly!', 422);
            }

            $chapter_quiz_id = $result->chapter_quiz_id;

            // Validate attachments
            if (!$request->hasFile('attachment_files')) {
                return $this->errorResponse([], 'Please, attach Answer!', 422);
            }

            // Upload files using trait method (supports both local and S3)
            $uploadedFiles = $this->uploadMultipleFiles(
                $request->file('attachment_files'),
                'written_answer',
                config('filesystems.default') === 's3', // Auto-detect S3
                'wa_' . $chapter_quiz_id
            );

            // Save attachments to database
            foreach ($uploadedFiles as $index => $filePath) {
                ChapterQuizWrittenAttachment::create([
                    'chapter_quiz_result_id' => $result_id,
                    'chapter_quiz_id' => $chapter_quiz_id,
                    'user_id' => $user_id,
                    'attachment_url' => $filePath,
                ]);
            }

            // Create default marks for each question
            $written = ChapterQuizWrittenQuestion::where('chapter_quiz_id', $chapter_quiz_id)->first();
            if ($written) {
                for ($i = 1; $i <= $written->no_of_question; $i++) {
                    ChapterQuizWrittenMark::create([
                        'chapter_quiz_result_id' => $result_id,
                        'chapter_quiz_id' => $chapter_quiz_id,
                        'user_id' => $user_id,
                        'question_no' => $i,
                        'mark' => 0.00,
                        'marks_givenby_id' => 0,
                    ]);
                }
            }

            return $this->successResponse(
                new ResultDetailsResource($result),
                'Quiz Submitted Successfully!'
            );

        } catch (\Throwable $th) {
            return $this->errorResponse(
                $th->getMessage(),
                'Something went wrong while submitting the quiz!',
                500
            );
        }
    }

    public function getResultList (Request $request)
    {
        $user_id = Auth::id();

        $results = ChapterQuizResult::where('organization_id', $request->organization_id)->where('user_id', $user_id)->orderBy('id', 'desc')->get();

        $result_count = $results->count();
        if ($result_count > 0) {
            return $this->apiResponse(ResultListResource::collection($results), 'Quiz Result Details', true, 200);
        } else {
            return $this->apiResponse([], 'You Are Not Attempted Any Quiz', true, 200);
        }

    }
    public function getResultDetails(Request $request)
    {
        $user_id = Auth::id();

        $result = ChapterQuizResult::where('id', $request->result_id)->where('user_id', $user_id)->first();
        if ($result) {
            return $this->apiResponse(new ResultDetailsResource($result), 'Quiz Result Details', true, 200);
        } else {
            return $this->apiResponse(null, 'Quiz Result Details Not Found', false, 404);
        }
    }


    public function studentDashboard(Request $request) {

        $user = Auth::user();
        // $latest

        $contentWatchLogController = new ContentWatchLogController($request);

        $student = StudentInformation::where('user_id', Auth::user()->id)->first();

        $list = Payment::where('user_id', $user->id)->where('organization_id', $user->organization_id)->pluck('item_id')->toArray();
        $courseList = Course::whereIn('id', $list)->limit(2)->get();
        $classSchedule =  new ClassScheduleController($request);
        $nextClass = $classSchedule->getNextClass($request);
        $assignments = $classSchedule->studentNextAssignment($student->id);
        // $this->studentAssignmentListMobile($student->id)
        $dashboad = [
            'latest_activities' => $contentWatchLogController->getLastAccessedContent(),
            'my_course' => OrganizationMyCourseListResource::collection($courseList),
            'next_class' => $nextClass,
            'assignments' => $assignments
        ];

        return $this->apiResponse($dashboad, 'Dashboard', true, 200);
    }

    public function studentDashboardWeb(Request $request) {

        $user = Auth::user();
        // $latest

        $contentWatchLogController = new ContentWatchLogController($request);

        $student = StudentInformation::where('user_id', Auth::user()->id)->first();

        $list = Payment::where('user_id', $user->id)->where('organization_id', $user->organization_id)->pluck('item_id')->toArray();
        $courseList = Course::whereIn('id', $list)->limit(4)->get();
        $classSchedule =  new ClassScheduleController($request);
        $nextClass = $classSchedule->getNextClass($request);
        $assignments = $classSchedule->studentNextAssignment($student->id);
        // $this->studentAssignmentListMobile($student->id)
        $dashboad = [
            'latest_activities' => $contentWatchLogController->getLastAccessedContent(),
            'my_course' => OrganizationMyCourseListResource::collection($courseList),
            'next_class' => $nextClass,
            'assignments' => $assignments
        ];

        return $this->apiResponse($dashboad, 'Dashboard', true, 200);
    }


    public function learningActivities (Request $request)
    {
        $user = Auth::user();
        $student = StudentInformation::where('user_id', $user->id)->first();
        $startDate = $request->start_date ? $request->start_date : date('Y-m-d', strtotime('-7 days'));
        $endDate = $request->end_date ? $request->end_date : date('Y-m-d');

        if (strtotime($endDate) > strtotime(date('Y-m-d'))) {
            return $this->apiResponse(null, 'End date can not be a future date', false, 422);
        }

        $endDate = date('Y-m-d', strtotime($endDate . '+1 days'));

        $classScheduleController = new ClassScheduleController ($request);
        $classCountPercent = $classScheduleController->getStudentClassJoinHistory($startDate, $endDate, $student->id);
        $joinCount = $classScheduleController->getStudentClassCount($startDate, $endDate, $student->id);

        $contentWatchLogController = new ContentWatchLogController($request);

        $totalVideoWatchCount = $contentWatchLogController->getVideoCount($startDate, $endDate, $user->id);
        $totalScriptViewCount = $contentWatchLogController->getScriptCount($startDate, $endDate, $user->id);

        $assignmentController = new AssignmentController($request);
        $assignmentSubmissionCount = $assignmentController->assignmentSubmissionCount($startDate, $endDate, $student->id);
        $assignmentCount = $assignmentController->totalAssignmentCount($startDate, $endDate, $student->id);




        return $this->apiResponse([
            'class_join_percent' => $classCountPercent,
            'quiz_count' => $this->quizParticipationCount($startDate, $endDate, $user->id),
            'correct_answers_percent' => $this->percentOfCorrectAnswers($startDate, $endDate, $user->id),
            'total_video_watch_count' => $totalVideoWatchCount,
            'total_assignment_submission_count' => $assignmentSubmissionCount,
            'total_join_count' => $joinCount,
            'total_assignment_count' => $assignmentCount,
            'total_script_count' => $totalScriptViewCount,

        ], 'Learning activities details', true, 200);

    }

    public function quizParticipationCount ($startDate, $endDate, $user_id ) {

        return ChapterQuizResult::where('user_id', $user_id)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->where('submission_status', '!=', 'Started')
            ->selectRaw('count(distinct chapter_quiz_id) as count')
            ->value('count');
    }


    public function percentOfCorrectAnswers ($startDate, $endDate, $user_id) {

        $results = ChapterQuizResult::where('user_id', $user_id)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->pluck('id');

        $answers = ChapterQuizResultAnswer::whereIn('chapter_quiz_result_id', $results)->count();
        $correctAnswers = ChapterQuizResultAnswer::whereIn('chapter_quiz_result_id', $results)->where('is_correct', 1)->count();
        if ($answers == 0) {
            return 0;
        }

        return round(($correctAnswers / $answers) * 100, 2);
    }


    public function learningGraph (Request $request) {
        $days = $request->days ? $request->days : 7;
        $startDate = date('Y-m-d', strtotime('-' . $days . ' days'));
        $endDate = date('Y-m-d');

        $dashboadGraph = $this->dayWisePerticipation($startDate, $endDate, $request);

        return $this->apiResponse($dashboadGraph, 'Dashboard', true, 200);
    }


    public function dayWisePerticipation ($startDate, $endDate, $request) {

        $user = Auth::user();
        $student = StudentInformation::where('user_id', $user->id)->first();

        $contentWatchLogController = new ContentWatchLogController($request);
        $totalVideoWatchCount = $contentWatchLogController->getVideoCount($startDate, $endDate, $user->id);
        $totalScriptViewCount = $contentWatchLogController->getScriptCount($startDate, $endDate, $user->id);

        $assignmentController = new AssignmentController($request);
        $assignmentSubmissionCount = $assignmentController->assignmentSubmissionCount($startDate, $endDate, $student->id);


        $classScheduleController = new ClassScheduleController ($request);


        $video = [
            "category" => "Video",
            "count" => $totalVideoWatchCount,
            "color" => "F26F6C"
        ];
        $script = [
            "category" => "Script",
            "count" => $totalScriptViewCount,
            "color" => "4FB8DA"
        ];
        $quiz = [
            "category" => "Quiz",
            "count" => $this->quizParticipationCount($startDate, $endDate, $user->id),
            "color" => "F6C754"
        ];
        $assignment = [
            "category" => "Assignment",
            "count" => $assignmentSubmissionCount,
            "color" => "85C658"
        ];
        $liveClass = [
            "category" => "Live Class",
            "count" => $classScheduleController->getStudentClassCount($startDate, $endDate, $student->id),
            "color" => "3384F7"
        ];

        $data = [$video, $script, $quiz, $assignment, $liveClass];
        return $data;
    }


    public function studentList (Request $request) {

        if(!$request->course_id){
            return $this->apiResponse(null, 'Please select a course', false, 422);
        }


        $list = Payment::where('item_id', $request->course_id)->pluck('user_id')->toArray();
        $students = StudentInformation::whereIn('user_id', $list)
        ->select('id', 'user_id', 'name', 'email', 'contact_no', 'image')
        ->get();
        return $this->apiResponse($students, 'Student List', true, 200);
    }
}

