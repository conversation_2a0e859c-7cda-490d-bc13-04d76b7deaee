<?php

namespace App\Http\Controllers\Mobile;

use App\Http\Controllers\Controller;
use App\Models\MentorInformation;
use App\Models\CourseMentor;
use App\Models\StudentInformation;
use App\Models\Course;
use App\Models\User;
use App\Models\CourseStudentMapping;
use Illuminate\Http\Request;
use Auth;
use App\Http\Resources\Mobile\Mentor\CourseListResource;
use App\Http\Resources\Mobile\MentorDetailsResource;
use App\Http\Resources\Mobile\MentorDashboardResource;
use App\Http\Resources\WebMentorDashboardResource;
use App\Http\Requests\Mobile\Mentor\ProfileUpdateRequest;
use DB;
class MentorController extends Controller
{

    public function dashboard (Request $request) {
        try {
            $user_id = Auth::id();
            $mentor = MentorInformation::where('user_id', $user_id)->first();
            $data = new MentorDashboardResource($mentor);
            return $this->apiResponse($data, 'Men<PERSON> profile fetched successfully', true, 200);
        } catch (\Throwable $th) {
            return $this->apiResponse(null, $th->getMessage(), false, 500);
        }
    }

    public function webDashboard (Request $request) {
        try {
            $user_id = Auth::id();
            $mentor = MentorInformation::where('user_id', $user_id)->first();
            $data = new WebMentorDashboardResource($mentor);
            return $this->apiResponse($data, 'Mentor profile fetched successfully', true, 200);
        } catch (\Throwable $th) {
            return $this->apiResponse(null, $th->getMessage(), false, 500);
        }
    }
    public function activities (Request $request) {
        $days = $request->days ? $request->days : 7;
        $startDate = date('Y-m-d', strtotime('-' . $days . ' days'));
        $endDate = date('Y-m-d');

        $user_id = Auth::id();
        $mentor = MentorInformation::where('user_id', $user_id)->first();

        $classScheduleController = new ClassScheduleController ($request);
        $data = $classScheduleController->getMentorActivitiesPerDay($mentor->id, $startDate, $endDate);

        return $this->apiResponse($data, 'Mentor activities found', true, 200);

    }

    public function myCourseList(Request $request)
    {
        try {
            $user_id = Auth::id();
            $mentor = MentorInformation::where('user_id', $user_id)->first();
            $ids = CourseMentor::where('mentor_id', $mentor->id)->pluck('course_id');

            $courses = Course::whereIn('courses.id', $ids)
                ->orderBy('courses.sequence', 'ASC')
                ->where('is_active', 1)
                ->get();

            return $this->apiResponse(CourseListResource::collection($courses), 'Course list fetched successfully', true, 200);
        } catch (\Throwable $th) {
            return $this->apiResponse(null, $th->getMessage(), false, 500);
        }
    }

    public function myProfile (Request $request)
    {
        try {
            $user_id = Auth::id();
            $mentor = MentorInformation::where('user_id', $user_id)->first();
            return $this->apiResponse(new MentorDetailsResource($mentor), 'Mentor profile fetched successfully', true, 200);
        } catch (\Throwable $th) {
            return $this->apiResponse(null, $th->getMessage(), false, 500);
        }
    }

    public function myStudentList (Request $request) {
        $user_id = Auth::id();
        $mentor = MentorInformation::where('user_id', $user_id)->first();
        if ($request->course_id) {
            $studentlist = CourseStudentMapping::where('mentor_id', $mentor->id)->where('course_id', $request->course_id)
            ->pluck('student_id');
        } else {
            $studentlist = CourseStudentMapping::where('mentor_id', $mentor->id)
                ->distinct()
                ->pluck('student_id');
        }

        $students = StudentInformation::whereIn('user_id', $studentlist)
        ->select('id', 'user_id', 'name', 'email', 'contact_no', 'image')
        ->get();
        return $this->apiResponse($students, 'My Student List', true, 200);
    }

    public function updateProfile(ProfileUpdateRequest $request)
    {
        try {
            DB::beginTransaction();
            $id = Auth::user()->id;
            $mentorInfo = MentorInformation::where('user_id', $id)->first();
            $user = User::findOrFail($mentorInfo->user_id);

            if ($request->contact_no) {
                if ($user->phone_verified_at && $user->contact_no != $request->contact_no) {
                    return $this->apiResponse(null, 'Phone number already verified', false, 403);
                }
            }

            if ($request->email && $user->email != $request->email) {
                if ($user->email_verified_at) {
                    return $this->apiResponse(null, 'Email already verified', false, 403);
                }
            }

            $user->update($request->only(['name', 'email', 'contact_no', 'address']));

            if ($request->hasFile('image')) {
                $user->update(['image' => $this->imageUpload($request, 'image', 'image')]);
            }

            $mentor = $request->only([
                'mentor_id',
                'education',
                'institute',
                'device_id',
                'alternative_contact_no',
                'gender',
                'blood_group',
                'bio',
                'father_name',
                'mother_name',
                'religion',
                'marital_status',
                'date_of_birth',
                'current_address',
                'permanent_address',
                'profession',
                'intro_video',
                'interests',
                'division_id',
                'city_id',
                'area_id',
                'nid_no',
                'birth_certificate_no',
                'passport_no',
                'status',
                'is_foreigner',
                'is_active',
                'rating',
            ]);

            $mentorInfo->update(array_merge(
                $mentor,
                [
                    'name' => $user->name,
                    'email' => $user->email,
                    'contact_no' => $user->contact_no,
                    'address' => $user->address,
                    'image' => $user->image,
                ]
            ));

            DB::commit();

            return $this->apiResponse(new MentorDetailsResource($mentorInfo), 'Profile Updated Successfully', true, 200);
        } catch (\Throwable $th) {
            DB::rollBack();

            return $this->apiResponse($th->getMessage(), 'Something went wrong', false, 500);
        }
    }
}
