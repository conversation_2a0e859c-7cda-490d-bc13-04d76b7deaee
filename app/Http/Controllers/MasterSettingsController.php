<?php

namespace App\Http\Controllers;

use App\Http\Traits\HelperTrait;
use App\Models\Category;
use App\Models\SubCategory;
use App\Models\Content;
use App\Models\ContentOutline;
use App\Models\Correction;
use App\Models\CorrectionRating;
use App\Models\Country;
use App\Models\Course;
use App\Models\CategoryItem;
use App\Models\CourseClassRoutine;
use App\Models\CourseFaq;
use App\Models\CourseFeature;
use App\Models\CourseMentor;
use App\Models\CourseOutline;
use App\Models\Grade;
use App\Models\Interest;
use App\Models\PackageType;
use App\Models\Payment;
use App\Models\PaymentDetail;
use App\Models\TopicConsume;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Facades\Validator;
use App\Http\Resources\Mobile\Course\ListResource;
use App\Http\Resources\CategoryDetailsResource;
use Auth;

class MasterSettingsController extends Controller
{
    use HelperTrait;

    public function truncateData(Request $request)
    {
        Correction::truncate();
        CorrectionRating::truncate();
        Payment::truncate();
        PaymentDetail::truncate();
        TopicConsume::truncate();

        return response()->json([
            'status' => true,
            'message' => 'Truncated Successful',
            'data' => [],
        ], 200);
    }

    public function packageTypeList(Request $request)
    {
        $package_list = PackageType::select('id', 'name', 'price', 'limit')->where('is_active', true)->get();

        return response()->json([
            'status' => true,
            'message' => 'List Successful',
            'data' => $package_list,
        ], 200);
    }

    public function gradeList(Request $request)
    {
        $grade_list = Grade::select('id', 'name')->where('is_active', true)->get();

        return response()->json([
            'status' => true,
            'message' => 'List Successful',
            'data' => $grade_list,
        ], 200);
    }

    public function categoryList(Request $request)
    {
        $category_list = Category::select('id', 'name')->where('is_active', true)->get();

        return response()->json([
            'status' => true,
            'message' => 'List Successful',
            'data' => $category_list,
        ], 200);
    }

    public function countryList(Request $request)
    {
        $country_list = Country::select('id', 'country_name')->get();

        return response()->json([
            'status' => true,
            'message' => 'List Successful',
            'data' => $country_list,
        ], 200);
    }

    //Admin Methods
    public function admin_PackageTypeList(Request $request)
    {
        $package_list = PackageType::all();

        return response()->json([
            'status' => true,
            'message' => 'List Successful',
            'data' => $package_list,
        ], 200);
    }

    public function saveOrUpdatePackageType(Request $request)
    {
        try {
            if ($request->id) {
                $type = PackageType::where('id', $request->id)->update($request->all());

                return response()->json([
                    'status' => true,
                    'message' => 'Type has been updated successfully',
                    'data' => [],
                ], 200);
            } else {
                $isExist = PackageType::where('name', $request->name)->first();
                if (empty($isExist)) {
                    $type = PackageType::create($request->all());

                    return response()->json([
                        'status' => true,
                        'message' => 'Type has been created successfully',
                        'data' => [],
                    ], 200);
                } else {
                    return response()->json([
                        'status' => false,
                        'message' => 'Type already Exist!',
                        'data' => [],
                    ], 200);
                }
            }
        } catch (Exception $e) {
            return response()->json([
                'status' => false,
                'message' => $e->getMessage(),
                'data' => [],
            ], 200);
        }
    }

    public function adminGradeList(Request $request)
    {
        $grade_list = Grade::all();

        return response()->json([
            'status' => true,
            'message' => 'List Successful',
            'data' => $grade_list,
        ], 200);
    }

    public function saveOrUpdateGrade(Request $request)
    {
        try {
            if ($request->id) {
                $type = Grade::where('id', $request->id)->update($request->all());

                return response()->json([
                    'status' => true,
                    'message' => 'Grade has been updated successfully',
                    'data' => [],
                ], 200);
            } else {
                $isExist = Grade::where('name', $request->name)->first();
                if (empty($isExist)) {
                    $type = Grade::create($request->all());

                    return response()->json([
                        'status' => true,
                        'message' => 'Grade has been created successfully',
                        'data' => [],
                    ], 200);
                } else {
                    return response()->json([
                        'status' => false,
                        'message' => 'Grade already Exist!',
                        'data' => [],
                    ], 200);
                }
            }
        } catch (Exception $e) {
            return response()->json([
                'status' => false,
                'message' => $e->getMessage(),
                'data' => [],
            ], 200);
        }
    }

    public function adminMenuList(Request $request)
    {
        $query = Category::query();
        $query->withCount('courses');
        $query->with('subCategories', function ($q) {
            $q->withCount('courses');
        });

        $query->orderBy('sequence', 'asc');

        if ($request->has('footer')) {
            $query->where('is_footer', 1);
        }
        if ($request->has('custom_page')) {
            $query->where('is_custom_page', 1);
            $query->where('is_footer', 0);
        }
        if ($request->has('course_category')) {
            $query->where('is_custom_page', 0);
        }
        // Searching
        $searchValue = $request->input('search');
        if ($searchValue) {
            $query->where(function ($q) use ($searchValue) {
                $q->where('name', 'like', '%'.$searchValue.'%');
            });
        }

        $pagination = $request->boolean('pagination', true);

        if ($pagination) {
            $itemsPerPage = $request->input('itemsPerPage', 10);

            $currentPage = Paginator::resolveCurrentPage('page');
            $results = $query->paginate($itemsPerPage, ['*'], 'page', $currentPage);

            return $this->apiResponse($results, 'List Successful', true, 200);
        }

        $results = $query->get();

        return $this->apiResponse($results, 'List Successful', true, 200);
    }


    public function courseListByID(Request $request)
    {
        $menu_id = $request->menu_id ? $request->menu_id : 0;

        if (! $menu_id) {
            return response()->json([
                'status' => false,
                'message' => 'Please, attach menu ID',
                'data' => [],
            ], 422);
        }

        $menu = Category::where('id', $menu_id)->first();

        if (empty($menu)) {
            return response()->json([
                'status' => false,
                'message' => 'Menu not found!',
                'data' => [],
            ], 404);
        }

        if ($menu->is_course) {
            $courses = Course::where('category_id', $menu->id)->orderBy('sequence', 'ASC')->get();
            $menu->courses = $courses;

            foreach ($courses as $course) {
                $course->course_outline = CourseOutline::select(
                    'course_outlines.*',
                    'class_levels.name as class_name',
                    'subjects.name as subject_name',
                    'chapters.name as chapter_name'
                )
                    ->where('course_outlines.course_id', $course->id)
                    ->leftJoin('class_levels', 'class_levels.id', 'course_outlines.class_level_id')
                    ->leftJoin('subjects', 'subjects.id', 'course_outlines.subject_id')
                    ->leftJoin('chapters', 'chapters.id', 'course_outlines.chapter_id')
                    ->get();

                $course->course_routine = CourseClassRoutine::where('course_id', $course->id)->get();
                $course->course_feature = CourseFeature::where('course_id', $course->id)->get();
                $course->course_mentor = CourseMentor::where('course_id', $course->id)->get();
                $course->course_faq = CourseFaq::where('course_id', $course->id)->get();
            }
        }

        if ($menu->is_content) {
            $content_list = Content::where('category_id', $menu->id)->get();
            $menu->contents = $content_list;

            foreach ($content_list as $content) {
                $content->content_outline = ContentOutline::select(
                    'content_outlines.*',
                    'class_levels.name as class_name',
                    'subjects.name as subject_name',
                    'chapters.name as chapter_name'
                )
                    ->where('content_outlines.content_id', $content->id)
                    ->leftJoin('class_levels', 'class_levels.id', 'content_outlines.class_level_id')
                    ->leftJoin('subjects', 'subjects.id', 'content_outlines.subject_id')
                    ->leftJoin('chapters', 'chapters.id', 'content_outlines.chapter_id')
                    ->get();
            }
        }

        return response()->json([
            'status' => true,
            'message' => 'Successful',
            'data' => $menu,
        ], 200);
    }

    public function mobileMenuList(Request $request)
    {
        $menus = Category::where('organization_id', $request->user()?->organization_id || $request->organization_id)->select('id', 'name')->orderBy('sequence', 'ASC')->get();


        return response()->json([
            'status' => true,
            'message' => 'List Successful',
            'data' => $menus,
        ], 200);
    }

    public function websiteAuthMenuList(Request $request) {

        $menus = Category::where('is_auth_footer', true)->where('is_active', true)->get();

        return $this->apiResponse($menus, 'Auth Menu List Successful', true, 200);

    }
    
    public function websiteMenuList(Request $request)
    {
        $menus = Category::with(['subCategories' => function ($query) {
            $query->whereHas('courses', function ($q) {
                $q->where('is_active', true);
            });
        }])
        ->where(function ($query) {
            $query->where('is_custom_page', true)
                  ->orWhere(function ($q) {
                      $q->where('is_custom_page', false)
                        ->where(function ($subQuery) {
                            $subQuery->whereHas('subCategories.courses', function ($courseQuery) {
                                $courseQuery->where('is_active', true);
                            })->orWhereHas('courses', function ($courseQuery) {
                                $courseQuery->where('is_active', true);
                            });
                        });
                  });
        })
        ->orderBy('sequence', 'ASC')
        ->get();


        return $this->apiResponse($menus, 'List Successful', true, 200);
    }

    public function websiteMenuDetails(Request $request)
    {
        $menu = Category::with(['subCategories' => function ($query) {
            $query->where('is_active', true);
        }, 'courses' => function ($query) use ($request)  {
            $query->where('is_active', true)
            ->when($request->sub_category_id, function ($q) use ($request) {
                return $q->where('sub_category_id', $request->sub_category_id);
            });
        }])
        ->find($request->id);
        $menu->courses = ListResource::collection($menu->courses);
        return $this->apiResponse($menu, 'Details Successful', true, 200);
    }



    public function saveOrUpdateMenu(Request $request)
    {
        $validatedData = $request->validate([
            'name' => 'required',
            'headline' => 'nullable',
            'description' => 'nullable',
            'is_authentication_needed' => 'nullable',
            'has_submenu' => 'nullable',
            'is_active' => 'nullable',
            'sub_categories' => 'nullable|array',
        ]);

        $user = Auth::user();
        $validatedData['organization_id'] = $user->organization_id;

        if ($request->id) {
            $category = Category::find($request->id);
            if ($category) {
                if ($request->hasFile('icon')) {
                    $validatedData['icon'] = $this->imageUpload($request, 'icon', 'icon', $category->icon ?? null);
                }

                $validatedData['is_footer'] = $request->is_footer ? $request->is_footer : $category->is_footer;

                $validatedData['is_auth_footer'] = $category->is_footer == false ? 0 : ($request->is_auth_footer ? $request->is_auth_footer : $category->is_auth_footer);
                // $validatedData['is_footer'] = $request->is_footer ?? false;
                $category->update($validatedData);

                if ($request->sub_categories) {
                    $validatedData = $request->validate([
                        'sub_categories.*.name' => 'required|string',
                        'sub_categories.*.name_bn' => 'nullable|string',
                    ]);

                    foreach ($request->sub_categories as $subcategory) {
                        if (isset($subcategory['id'])) {
                            // Update existing subcategory
                            SubCategory::where('id', $subcategory['id'])
                                ->update([
                                    'name' => $subcategory['name'],
                                    'name_bn' => $subcategory['name_bn'] ?? null,
                                    'category_id' => $category->id,
                                    'organization_id' => $category->organization_id,
                                ]);
                        } else {
                            // Insert new subcategory
                            SubCategory::create([
                                'name' => $subcategory['name'],
                                'name_bn' => $subcategory['name_bn'] ?? null,
                                'category_id' => $category->id,
                                'organization_id' => $category->organization_id,
                            ]);
                        }
                    }
                }

                return $this->apiResponse([], 'Category has been updated successfully', true, 200);
            } else {
                return $this->apiResponse([], 'Category not found!', false, 404);
            }
        } else {
            $isExist = Category::where('name', $request->name)->exists();
            if (!$isExist) {
                if ($request->hasFile('icon')) {
                    $validatedData['icon'] = $this->imageUpload($request, 'icon', 'icon', null);
                }

                if($request->is_content) {
                    $validatedData['is_content'] = true;
                    $validatedData['is_custom_page'] = true;
                }
                $validatedData['is_footer'] = $request->is_footer ?? false;

                // Get the maximum sequence value
                $maxSequence = Category::max('sequence');
                // Set the new category's sequence to be one more than the maximum
                $validatedData['sequence'] = $maxSequence + 1;

                $category = Category::create($validatedData);

                if ($request->sub_categories) {
                    foreach ($request->sub_categories as $subcategory) {
                        SubCategory::create([
                            'name' => $subcategory['name'],
                            'name_bn' => $subcategory['name_bn'] ?? null,
                            'category_id' => $category->id,
                            'organization_id' => $category->organization_id,
                        ]);
                    }
                }

                return $this->apiResponse([], 'Category has been created successfully', true, 200);
            } else {
                return $this->apiResponse([], 'Category already exists!', false, 400);
            }
        }
    }
    public function menuDetailsWebsite (Request $request, $slug) {
        try {
            // Check if slug is valid
            if (empty($slug)) {
                return $this->apiResponse([], 'Invalid slug parameter', false, 400);
            }

            // Find the menu by slug
            $menu = Category::where('slug', $slug)->first();

            // If not found, try to find by name (as a fallback)
            if (!$menu) {
                $menu = Category::where('name', $slug)->first();
            }

            // If still not found, return 404
            if (!$menu) {
                return $this->apiResponse([], 'Menu not found', false, 404);
            }

            // Generate slug for categories that don't have one
            if (empty($menu->slug)) {
                $menu->slug = \Illuminate\Support\Str::slug($menu->name);
                $menu->save();
            }

            return $this->apiResponse(new CategoryDetailsResource($menu), 'Menu details', true, 200);
        } catch (\Exception $e) {
            return $this->apiResponse([], 'Error: ' . $e->getMessage(), false, 500);
        }
    }

    public function menuDetails (Request $request, $id) {
        try {
            $menu = Category::findOrFail($id);
            return $this->apiResponse(new CategoryDetailsResource($menu), 'Menu details', true, 200);
        } catch (ModelNotFoundException $e) {
            return $this->apiResponse([], 'Menu not found', false, 404);
        }
    }

    public function addItemsToMenu (Request $request) {
        try {
            $menu = Category::findOrFail($request->menu_id);
            $menu->categoryItems()->create([
                'category_id' => $menu->id,
                'title' => $request->title,
                'description' => $request->description,
                'course_id' => $request->course_id,
                'ebook_id' => $request->ebook_id,
                'chapter_video_id' => $request->chapter_video_id,
                'chapter_script_id' => $request->chapter_script_id,
                'chapter_quiz_id' => $request->chapter_quiz_id,
                'is_active' => 1,
                'created_by' => auth()->user()->id,
            ]);

            return $this->apiResponse([], 'Menu has been updated successfully', true, 200);
        } catch (\Exception $e) {
            return $this->apiResponse([], 'An error occurred while updating the menu', false, 500, [
                'error' => $e->getMessage(),
            ]);
        }
    }

    public function updateItemOfMenu (Request $request) {
        try {
            $menu = CategoryItem::findOrFail($request->id);
            $menu->description = $request->description;

            $menu->save();

            return $this->apiResponse($menu, 'Menu has been updated successfully', true, 200);
        } catch (\Exception $e) {
            return $this->apiResponse([], 'An error occurred while updating the menu', false, 500, [
                'error' => $e->getMessage(),
            ]);
        }
    }




    public function menuDelete(Request $request, $id)
    {
        try {
            $menu = Category::findOrFail($id);
            $menu->delete();

            return $this->apiResponse([], 'Menu deleted successfully', true, 200);
        } catch (ModelNotFoundException $e) {
            return $this->apiResponse([], 'Menu not found', false, 404);
        } catch (\Exception $e) {
            return $this->apiResponse([], 'An error occurred while deleting the menu', false, 500);
        }
    }

    public function sortMenu(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'items' => 'required|array',
                'items.*.id' => 'required|integer|exists:categories,id',
                'items.*.sequence' => 'required|integer|min:0',
            ]);

            if ($validator->fails()) {
                return $this->apiResponse([], $validator->errors()->first(), false, 422);
            }

            // Begin transaction to ensure all updates succeed or fail together
            \DB::beginTransaction();

            foreach ($request->items as $item) {
                Category::where('id', $item['id'])->update([
                    'sequence' => $item['sequence']
                ]);
            }

            // Commit the transaction
            \DB::commit();

            return $this->apiResponse([], 'Menu order updated successfully', true, 200);
        } catch (\Exception $e) {
            // Rollback the transaction in case of error
            \DB::rollBack();
            return $this->apiResponse([], 'An error occurred while updating menu order: ' . $e->getMessage(), false, 500);
        }
    }
    public function adminCategoryList(Request $request)
    {
        $category_list = Category::all();

        return response()->json([
            'status' => true,
            'message' => 'List Successful',
            'data' => $category_list,
        ], 200);
    }

    public function saveOrUpdateCategory(Request $request)
    {
        try {
            if ($request->id) {
                $type = Category::where('id', $request->id)->update($request->all());

                return response()->json([
                    'status' => true,
                    'message' => 'Category has been updated successfully',
                    'data' => [],
                ], 200);
            } else {
                $isExist = Category::where('name', $request->name)->first();
                if (empty($isExist)) {
                    $type = Category::create($request->all());

                    return response()->json([
                        'status' => true,
                        'message' => 'Category has been created successfully',
                        'data' => [],
                    ], 200);
                } else {
                    return response()->json([
                        'status' => false,
                        'message' => 'Category already Exist!',
                        'data' => [],
                    ], 200);
                }
            }
        } catch (Exception $e) {
            return response()->json([
                'status' => false,
                'message' => $e->getMessage(),
                'data' => [],
            ], 200);
        }
    }

    public function markGradeList(Request $request)
    {
        $grade = ['BelowSatisfaction', 'Satisfactory', 'Good', 'Better', 'Excellent'];

        return response()->json([
            'status' => true,
            'message' => 'List Successful',
            'data' => $grade,
        ], 200);
    }

    public function tagsList(Request $request)
    {
        $interest = Interest::pluck('tags');

        return response()->json([
            'status' => true,
            'message' => 'List Successful',
            'data' => $interest,
        ], 200);
    }

    public function saveOrUpdateTags(Request $request)
    {
        try {
            $validateUser = Validator::make(
                $request->all(),
                [
                    'tag' => 'required',
                ]
            );

            if ($validateUser->fails()) {
                return response()->json([
                    'status' => false,
                    'message' => 'validation error',
                    'data' => $validateUser->errors(),
                ], 422);
            }
            if ($request->id) {
                Interest::where('id', $request->id)->update([
                    'tags' => $request->tag,
                ]);

                return response()->json([
                    'status' => true,
                    'message' => 'Tag has been updated successfully',
                    'data' => [],
                ], 200);
            } else {
                $isExist = Interest::where('tags', $request->tag)->first();
                if (empty($isExist)) {
                    Interest::create([
                        'tags' => $request->tag,
                    ]);

                    return response()->json([
                        'status' => true,
                        'message' => 'Tag has been created successfully',
                        'data' => [],
                    ], 200);
                } else {
                    return response()->json([
                        'status' => false,
                        'message' => 'Tag already Exist!',
                        'data' => [],
                    ], 200);
                }
            }
        } catch (Exception $e) {
            return response()->json([
                'status' => false,
                'message' => $e->getMessage(),
                'data' => [],
            ], 200);
        }
    }

    public function tagsListAdmin(Request $request)
    {
        $interest = Interest::get();

        return response()->json([
            'status' => true,
            'message' => 'List Successful',
            'data' => $interest,
        ], 200);
    }

    public function tagsSaveOrUpdateAdmin(Request $request)
    {
        try {
            if (empty($request->id)) {
                $tag = json_decode($request->tags, true);
                if ($tag) {
                    $tags = [];
                    foreach ($tag as $key => $value) {
                        $tags[] = [
                            'tags' => $value,
                        ];
                    }
                    Interest::insert($tags);
                }

                return $this->apiResponse([], 'Tags Created Successfully', true, 201);
            } else {
                $tags = Interest::where('id', $request->id)->first();
                $tags->update([
                    'tags' => $request->tags,
                ]);

                return $this->apiResponse([], 'Tags Updated Successfully', true, 200);
            }
        } catch (\Throwable $th) {
            return $this->apiResponse([], $th->getMessage(), false, 500);
        }
    }

    public function tagsDelete(Request $request, $id)
    {
        Interest::where('id', $id)->delete();

        return $this->apiResponse([], 'Tags Deleted Successfully', true, 200);
    }
}
