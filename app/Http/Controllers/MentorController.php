<?php

namespace App\Http\Controllers;

use App\Http\Requests\MentorCreateRequest;
use App\Http\Requests\MentorUpdateRequest;
use App\Http\Traits\HelperTrait;
use App\Models\Course;
use App\Models\CourseMentor;
use App\Models\CourseStudentMapping;
use App\Models\MentorInformation;
use App\Models\MentorZoomLink;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;

class MentorController extends Controller
{
    use HelperTrait;

    public function mentorDetails(Request $request)
    {
        $user_id = Auth::id();
        $student = MentorInformation::where('user_id', $user_id)->with('user')->first();

        return $this->apiResponse($student, 'Successful', true, 200);
    }

    public function allMentorList(Request $request)
    {
        $mentorList = MentorInformation::where('is_active', true)->get();

        return response()->json([
            'status' => true,
            'message' => 'List Successful',
            'data' => $mentorList,
        ], 200);
    }

    public function mentorDetailsByID(Request $request)
    {
        $mentor_id = $request->mentor_id ? $request->mentor_id : 0;

        if (! $mentor_id) {
            return response()->json([
                'status' => false,
                'message' => 'Please, attach Mentor ID',
                'data' => [],
            ], 422);
        }

        $mentor = MentorInformation::where('id', $mentor_id)->with('user')->first();

        return response()->json([
            'status' => true,
            'message' => 'Successful',
            'data' => $mentor,
        ], 200);
    }

    public function myCourseList(Request $request)
    {
        try {
            $user_id = Auth::id();
            $mentor = MentorInformation::forOrganization('mentor_informations')
                ->where('user_id', $user_id)->first();
            $ids = CourseMentor::forOrganization('course_mentors')
                ->where('mentor_id', $mentor->id)->pluck('course_id');

            $courses = Course::forOrganization('courses')
                ->select('courses.*', 'categories.name as category_name')
                ->whereIn('courses.id', $ids)
                ->leftJoin('categories', 'categories.id', 'courses.category_id')
                ->orderBy('courses.sequence', 'ASC')
                ->get();

            return $this->apiResponse($courses, 'Successful', true, 200);
        } catch (\Throwable $th) {
            return $this->apiResponse(null, $th->getMessage(), false, 500);
        }
    }

    public function updateZoomLink(Request $request)
    {
        $user_id = Auth::id();
        $mentor = MentorInformation::where('user_id', $user_id)->first();

        $zoomLink = MentorZoomLink::where('mentor_id', $mentor->id)->first();

        if (! empty($zoomLink)) {
            MentorZoomLink::where('id', $zoomLink->id)->update([
                'live_link' => $request->live_link,
            ]);
        } else {
            MentorZoomLink::create([
                'mentor_id' => $mentor->id,
                'live_link' => $request->live_link,
                'is_active' => true,
            ]);
        }

        return response()->json([
            'status' => true,
            'message' => 'Link has been updated successfully',
            'data' => [],
        ], 200);
    }

    public function getZoomLink(Request $request)
    {
        $user_id = Auth::id();
        $mentor = MentorInformation::where('user_id', $user_id)->first();

        $zoomLink = MentorZoomLink::where('mentor_id', $mentor->id)->first();

        if (empty($zoomLink)) {
            $zoomLink = (object) [
                'id' => $user_id,
                'live_link' => '',
                'mentor_id' => $mentor->id,
                'is_active' => true,
                'created_at' => null,
                'updated_at' => null,
            ];
        }

        return response()->json([
            'status' => true,
            'message' => 'Link has been created successfully',
            'data' => $zoomLink,
        ], 200);
    }

    public function mentorCreate(MentorCreateRequest $request)
    {
        try {
            DB::beginTransaction();

            $userData = $request->only(['name', 'email', 'username', 'contact_no', 'address']);
            $userData['user_type'] = 'Mentor';
            $plainPassword = $request->password;
            $userData['password'] = Hash::make($request->password);

            $user = User::create($userData);

            $user->sendConfirmationEmail($plainPassword);

            if ($request->hasFile('image')) {
                $user->update(['image' => $this->imageUpload($request, 'image', 'image')]);
            }

            $mentorData = $request->only([
                'education',
                'institute',
                'device_id',
                'referral_code',
                'referred_code',
                'alternative_contact_no',
                'gender',
                'bio',
                'father_name',
                'mother_name',
                'religion',
                'marital_status',
                'date_of_birth',
                'profession',
                'current_address',
                'permanent_address',
                'division_id',
                'district_id',
                'city_id',
                'area_id',
                'nid_no',
                'birth_certificate_no',
                'passport_no',
                'intro_video',
                'status',
                'is_foreigner',
                'is_life_couch',
                'is_host_staff',
                'is_host_certified',
                'is_active',
                'rating',
                'approval_date',
                'is_featured',
                'host_rank_number',
                'blood_group',
            ]);

            $mentorData = array_merge($mentorData, [
                'user_id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'username' => $user->username,
                'contact_no' => $user->contact_no,
                'address' => $user->address,
                'image' => $user->image,
                'is_active' => true,
                'mentor_code' => $this->codeGenerator('MC', MentorInformation::class),
            ]);

            MentorInformation::create($mentorData);

            DB::commit();

            return $this->apiResponse([], 'Mentor Created Successfully', true, 200);
        } catch (\Throwable $th) {
            DB::rollBack();

            return $this->apiResponse($th->getMessage(), 'Something went wrong', false, 500);
        }
    }

    public function mentorUpdate(MentorUpdateRequest $request, $id)
    {
        try {
            DB::beginTransaction();

            $mentorInfo = MentorInformation::findOrFail($id);
            $user = User::findOrFail($mentorInfo->user_id);

             // Check email uniqueness (exclude current user)
            if ($request->has('email') && $request->email != '') {
                $existEmail = User::where('email', $request->email)
                    ->where('id', '!=', $user->id)
                    ->exists();
                if ($existEmail) {
                    return $this->apiResponse(null, 'Email is already taken', false, 422);
                }
            }

            // Check contact number uniqueness (exclude current user)
            if ($request->has('contact_no') && $request->contact_no != '') {
                $existContactNo = User::where('contact_no', $request->contact_no)
                    ->where('organization_id', auth()->user()->organization_id)
                    ->where('id', '!=', $user->id)
                    ->exists();
                if ($existContactNo) {
                    return $this->apiResponse(null, 'Contact number is already taken', false, 422);
                }
            }


            $user->update($request->only(['name', 'email', 'contact_no', 'address']));

            if ($request->hasFile('image')) {
                $user->update(['image' => $this->imageUpload($request, 'image', 'image')]);
            }

            $mentorData = $request->only([
                'education',
                'institute',
                'device_id',
                'referral_code',
                'referred_code',
                'alternative_contact_no',
                'gender',
                'bio',
                'father_name',
                'mother_name',
                'religion',
                'marital_status',
                'date_of_birth',
                'profession',
                'current_address',
                'permanent_address',
                'division_id',
                'district_id',
                'city_id',
                'area_id',
                'nid_no',
                'birth_certificate_no',
                'passport_no',
                'intro_video',
                'status',
                'is_foreigner',
                'is_life_couch',
                'is_host_staff',
                'is_host_certified',
                'is_active',
                'rating',
                'approval_date',
                'is_featured',
                'host_rank_number',
                'blood_group',
            ]);

            $mentorInfo->update(array_merge(
                $mentorData,
                [
                    'name' => $user->name,
                    'email' => $user->email,
                    'contact_no' => $user->contact_no,
                    'address' => $user->address,
                    'image' => $user->image,
                ]
            ));

            DB::commit();

            return $this->apiResponse([], 'Mentor Updated Successfully', true, 200);
        } catch (\Throwable $th) {
            DB::rollBack();

            return $this->apiResponse($th->getMessage(), 'Something went wrong', false, 500);
        }
    }

    public function mentorSaveOrUpdate(Request $request)
    {

        try {
            $mentor = [
                'education' => $request->education,
                'institute' => $request->institute,
                'device_id' => $request->device_id,
                'referral_code' => $request->referral_code,
                'referred_code' => $request->referred_code,
                'alternative_contact_no' => $request->alternative_contact_no,
                'gender' => $request->gender,
                'bio' => $request->bio,
                'father_name' => $request->father_name,
                'mother_name' => $request->mother_name,
                'religion' => $request->religion,
                'marital_status' => $request->marital_status,
                'date_of_birth' => $request->date_of_birth,
                'profession' => $request->profession,
                'current_address' => $request->current_address,
                'permanent_address' => $request->permanent_address,
                'division_id' => $request->division_id,
                'district_id' => $request->district_id,
                'city_id' => $request->city_id,
                'area_id' => $request->area_id,
                'nid_no' => $request->nid_no,
                'birth_certificate_no' => $request->birth_certificate_no,
                'passport_no' => $request->passport_no,
                'intro_video' => $request->intro_video,
                'status' => $request->status,
                'is_foreigner' => $request->is_foreigner,
                'is_life_couch' => $request->is_life_couch,
                'is_host_staff' => $request->is_host_staff,
                'is_host_certified' => $request->is_host_certified,
                'is_active' => $request->is_active,
                'rating' => $request->rating,
                'approval_date' => $request->approval_date,
                'host_rank_number' => $request->host_rank_number,
                'blood_group' => $request->blood_group,
                'nid_no' => $request->nid_no,

            ];

            if (empty($request->id)) {
                $validateUser = Validator::make(
                    $request->all(),
                    [
                        'name' => 'required',
                        'username' => 'required|unique:users,username,',
                        'email' => 'unique:users,email,',
                        'contact_no' => 'unique:users,contact_no,',
                        'password' => 'required',
                    ]
                );

                if ($validateUser->fails()) {
                    return response()->json([
                        'status' => false,
                        'message' => $validateUser->errors()->first(),
                        'data' => [],
                    ], 422);
                }

                if ($request->email) {
                    $is_exist = User::where('email', $request->email)->first();
                    if (! empty($is_exist)) {
                        return response()->json([
                            'status' => true,
                            'message' => 'Email address already been used! Please use another email',
                            'data' => [],
                        ], 422);
                    }
                }

                if ($request->contact_no) {
                    $is_exist = User::where('contact_no', $request->contact_no)->first();
                    if (! empty($is_exist)) {
                        return response()->json([
                            'status' => true,
                            'message' => 'Contact No already been used! Please use another number',
                            'data' => [],
                        ], 422);
                    }
                }

                $user = User::create([
                    'name' => $request->name,
                    'email' => $request->email,
                    'username' => $request->username,
                    'contact_no' => $request->contact_no,
                    'address' => $request->address,
                    'user_type' => 'Mentor',
                    'password' => Hash::make($request->password),
                ]);

                $user->sendConfirmationEmail($request->password);

                if ($request->hasFile('image')) {
                    $user->update([
                        'image' => $this->imageUpload($request, 'image', 'image'),
                    ]);
                }
                $mentorInfo = MentorInformation::create(
                    [
                        'name' => $user->name,
                        'user_id' => $user->id,
                        'email' => $user->email,
                        'username' => $user->username,
                        'contact_no' => $user->contact_no,
                        'address' => $user->address,
                        'image' => $user->image,
                        'is_active' => true,
                        'mentor_code' => $this->codeGenerator('MC', MentorInformation::class),
                    ]
                );
                $mentorInfo->update($mentor);

                return $this->apiResponse([], 'Mentor Created Successfully', true, 200);
            } else {

                $validateUser = Validator::make(
                    $request->all(),
                    [
                        'name' => 'required',
                        'email' => 'unique:users,email,'.$request->user_id,
                        'contact_no' => 'unique:users,contact_no,'.$request->user_id,
                    ]
                );

                if ($validateUser->fails()) {
                    return response()->json([
                        'status' => false,
                        'message' => 'validation error',
                        'data' => $validateUser->errors(),
                    ], 422);
                }
                DB::beginTransaction();
                $user = User::where('id', $request->user_id)->first();
                $user->update([
                    'name' => $request->name,
                    'email' => $request->email,
                    'contact_no' => $request->contact_no,
                    'address' => $request->address,
                ]);

                if ($request->hasFile('image')) {
                    $user->update([
                        'image' => $this->imageUpload($request, 'image', 'image'),
                    ]);
                }

                $mentorInfo = MentorInformation::where('id', $request->id)->first();
                $mentorInfo->update([
                    'name' => $user->name,
                    'email' => $user->email,
                    'contact_no' => $user->contact_no,
                    'address' => $user->address,
                    'image' => $user->image,

                ]);
                $mentorInfo->update($mentor);
                DB::commit();

                return $this->apiResponse([], 'Mentor Updated Successfully', true, 200);
            }
        } catch (\Throwable $th) {
            return $this->apiResponse($th->getMessage(), 'Something went wrong', false, 500);
        }
    }

    public function MentorList(Request $request)
    {
        $query = MentorInformation::query();
        $query->where('is_active', true)->with('user');
        $this->applySorting($query, $request);

        $searchKeys = ['name', 'email'];
        $this->applySearch($query, $request->input('search'), $searchKeys);

        $pagination = $request->boolean('pagination', true);
        if ($pagination) {
            $itemsPerPage = $request->input('itemsPerPage', 10);
            $currentPage = Paginator::resolveCurrentPage('page');
            $results = $query->paginate($itemsPerPage, ['*'], 'page', $currentPage);

            return $this->apiResponse($results, 'Mentor List', true, 200);
        }

        $results = $query->get();

        return $this->apiResponse($results, 'Mentor List', true, 200);
    }

    public function allMentorListAdmin(Request $request)
    {
        $query = MentorInformation::query();

        $this->applySorting($query, $request);

        $searchKeys = ['name', 'email'];
        $this->applySearch($query, $request->input('search'), $searchKeys);

        $pagination = $request->boolean('pagination', true);
        if ($pagination) {
            $itemsPerPage = $request->input('itemsPerPage', 10);
            $currentPage = Paginator::resolveCurrentPage('page');
            $results = $query->paginate($itemsPerPage, ['*'], 'page', $currentPage);

            return $this->apiResponse($results, 'Mentor List', true, 200);
        }

        $results = $query->get();

        return $this->apiResponse($results, 'Mentor List', true, 200);
    }

    public function mentorListForFilter(Request $request)
    {
        $course_id = $request->course_id ? $request->course_id : 0;
        $mentorList = CourseStudentMapping::where('course_id', $course_id)->pluck('mentor_id');
        $mentorList = MentorInformation::whereIn('id', $mentorList)
            ->where('is_active', true)
            ->select('id', 'name', 'image', 'mentor_code')
            ->get();

        return $this->apiResponse($mentorList, 'Mentor List', true, 200);
    }


    public function mentorDelete ($id) {
        try {
            $mentor = MentorInformation::where('id', $id)->first();
            if (empty($mentor)) {
                return $this->apiResponse([], 'Mentor Not Found', false, 404);
            }
            $user = User::where('id', $mentor->user_id)->first();
            $user->delete();
            $mentor->delete();
            return $this->apiResponse([], 'Mentor Deleted Successfully', true, 200);
        } catch (\Exception $e) {
            return $this->apiResponse(null, $e->getMessage(), false, 500);
        }
    }
}
