<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Services\PaymentGateway\CybersourceGatewayTwoService;

class CybersourceController extends Controller
{

    public function __construct(protected CybersourceGatewayTwoService $cybersource) {}

    public function getToken(Request $request)
    {
        $orgId = $request->user()->organization_id;
        $token = $this->cybersource->getCaptureContext($orgId);
        return response()->json(['data' => $token]);
    }

    public function pay(Request $request)
    {
        $request->validate([
            'token' => 'required',
            'amount' => 'required|numeric',
        ]);

        $orgId = $request->user()->organization_id;
        $response = $this->cybersource->processCardToken($orgId, $request->token, $request->amount);
        return response()->json(['data' => $response]);
    }
}
