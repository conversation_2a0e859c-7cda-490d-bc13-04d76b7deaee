<?php

namespace App\Http\Controllers;

use App\Models\OfflineExam;
use Illuminate\Http\Request;
use App\Http\Requests\OfflineExamCreateRequest;
use App\Http\Requests\OfflineExamUpdateRequest;
use App\Services\OfflineExamService;
use App\Http\Resources\OfflineExamDetailsResource;
use App\Http\Resources\OfflineExamListResource;

class OfflineExamController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function __construct(
        protected OfflineExamService $offlineExamService
      ) {}

    public function index()
    {
        try {
            $data = $this->offlineExamService->all();
            return $this->apiResponse(OfflineExamListResource::collection($data), 'Offline Exam List', true, 200);
        } catch (\Exception $e) {
            return $this->apiResponse(null, $e->getMessage(), false, 400);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(OfflineExamCreateRequest $request)
    {
        try {
            $data = $this->offlineExamService->create($request->validatedData());
            return $this->apiResponse(new OfflineExamDetailsResource($data), 'Offline Exam created successfully', true, 201);
        } catch (\Exception $e) {
            return $this->apiResponse(null, $e->getMessage(), false, 400);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Request $request, $id)
    {
        // return $id;

        $data = $this->offlineExamService->find($id);
        return $this->apiResponse(new OfflineExamDetailsResource($data), 'Offline Exam List', true, 200);

    }

    public function markOfflineExam(Request $request)
    {
        try {
            $data = $this->offlineExamService->markOfflineExam($request);
            return $this->apiResponse($data, 'Marking successfully completed', true, 201);
        } catch (\Exception $e) {
            return $this->apiResponse(null, $e->getMessage(), false, 400);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(OfflineExamUpdateRequest $request, $id)
    {
        try {
            $data = $this->offlineExamService->update($request->validated(), $id);
            return $this->apiResponse($data, 'Offline Exam updated successfully', true, 201);
        } catch (\Exception $e) {
            return $this->apiResponse(null, $e->getMessage(), false, 400);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        try {
            $data = $this->offlineExamService->delete($id);
            return $this->apiResponse($data, 'Offline Exam deleted successfully', true, 201);
        } catch (\Exception $e) {
            return $this->apiResponse(null, $e->getMessage(), false, 400);
        }
    }
}
