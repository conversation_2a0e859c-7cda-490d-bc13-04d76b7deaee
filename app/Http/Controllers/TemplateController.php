<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreTemplateItemRequest;
use App\Http\Requests\StoreTemplateRequest;
use App\Http\Requests\UpdateTemplateItemRequest;
use App\Http\Requests\UpdateTemplateRequest;
use App\Http\Traits\HelperTrait;
use App\Models\Template;
use App\Models\TemplateItem;
use Illuminate\Http\Request;

class TemplateController extends Controller
{
    use HelperTrait;

    public function templateDetails(Request $request)
    {
        $template = Template::with('items')
            ->where('is_active', 1)
            ->get();

        return $this->successResponse($template, 'Template retrieved successfully', 200);
    }

    public function storeTemplate(StoreTemplateRequest $request)
    {
        try {
            $template = $request->validated();
            if ($request->hasFile('theme_image')) {
                $template['theme_image'] = $this->imageUpload($request, 'theme_image', 'theme');
            }
            $template = Template::create($template);

            return $this->successResponse($template, 'Template created successfully');
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Something went wrong', 500);
        }
    }

    public function updateTemplate(UpdateTemplateRequest $request, $id)
    {

        try {
            $template = Template::findOrFail($id);
            $template->fill($request->validated());
            if ($request->hasFile('theme_image')) {
                $template->theme_image = $this->imageUpload($request, 'theme_image', 'theme');
            }
            $template->save();

            return $this->successResponse($template, 'Template updated successfully', 200);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Something went wrong', 500);
        }
    }

    public function deleteTemplate($id)
    {
        try {
            $template = Template::find($id);
            $template->delete();

            return $this->successResponse([], 'Template deleted successfully', 200);
        } catch (\Throwable $th) {
            return $this->errorResponse([], 'Something went wrong', 500);
        }
    }

    public function getTemplates()
    {
        try {
            $templates = Template::all();

            return $this->successResponse($templates, 'Templates retrieved successfully', 200);
        } catch (\Throwable $th) {
            return $this->errorResponse([], 'Something went wrong', 500);
        }
    }

    public function showTemplate($id)
    {
        try {
            $template = Template::with('items')->find($id);

            return $this->successResponse($template, 'Template retrieved successfully', 200);
        } catch (\Throwable $th) {
            return $this->errorResponse([], 'Something went wrong', 500);
        }
    }

    public function storeTemplateItem(StoreTemplateItemRequest $request)
    {
        try {
            $templateItem = $request->validated();
            if ($request->hasFile('image')) {
                $templateItem['image'] = $this->imageUpload($request, 'image', 'template');
            }
            $templateItem = TemplateItem::create($templateItem);

            return $this->successResponse($templateItem, 'Template Item created successfully', 201);
        } catch (\Throwable $th) {
            return $this->errorResponse([], 'Something went wrong', 500);
        }
    }

    public function updateTemplateItem(UpdateTemplateItemRequest $request, $id)
    {
        try {
            $templateItem = TemplateItem::findOrFail($id);
            $templateItem->fill($request->validated());
            if ($request->hasFile('image')) {
                $templateItem->image = $this->imageUpload($request, 'image', 'template');
            }
            $templateItem->save();

            return $this->successResponse($templateItem, 'Template Item updated successfully', 200);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Something went wrong', 500);
        }
    }

    public function deleteTemplateItem($id)
    {
        try {
            $templateItem = TemplateItem::find($id);
            $templateItem->delete();

            return $this->successResponse([], 'Template Item deleted successfully', 200);
        } catch (\Throwable $th) {
            return $this->errorResponse([], 'Something went wrong', 500);
        }
    }

    public function getTemplateItems()
    {
        try {
            $templateItems = TemplateItem::all();

            return $this->successResponse($templateItems, 'Template Items retrieved successfully', 200);
        } catch (\Throwable $th) {
            return $this->errorResponse([], 'Something went wrong', 500);
        }
    }

    public function showTemplateItem($id)
    {
        try {
            $templateItem = TemplateItem::find($id);

            return $this->successResponse($templateItem, 'Template Item retrieved successfully', 200);
        } catch (\Throwable $th) {
            return $this->errorResponse([], 'Something went wrong', 500);
        }
    }
}
