<?php

namespace App\Http\Controllers;

use App\Models\OrganizationPayment;
use App\Models\OrganizationPackage;
use App\Models\Payment;
use App\Models\PaymentDetail;
use App\Models\Course;
use App\Models\CourseParticipant;
use App\Mail\PaymentSuccessfulMail;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;

class OrganizationPaymentController extends Controller
{
    /**
     * Process a payment using the selected payment gateway.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function processPayment(Request $request)
    {
        $request->validate([
            'gateway' => 'required|string',
            'amount' => 'required|numeric',
            'course_id' => 'required|exists:courses,id',
            'coupon' => 'nullable|string',
            'currency' => 'required|string',
        ]);

        $organization = auth()->user()->organization;
        $course = Course::findOrFail($request->input('course_id'));
        $amount = $request->input('amount');
        $couponCode = $request->input('coupon');
        $coupon = null;
        $discountAmount = 0;

        // Apply coupon if provided
        if ($couponCode) {
            $coupon = \App\Models\Coupon::where('code', $couponCode)
                ->where('is_active', true)
                ->first();

            if (!$coupon) {
                return response()->json(['error' => 'Invalid coupon code'], 400);
            }

            if ($coupon->isExpired()) {
                return response()->json(['error' => 'Coupon has expired'], 400);
            }

            if ($coupon->max_usage > 0 && $coupon->usage_number >= $coupon->max_usage) {
                return response()->json(['error' => 'Coupon usage limit exceeded'], 400);
            }

            // Calculate discount
            if ($coupon->discount_type == 'percentage') {
                $discountAmount = ($amount * $coupon->discount) / 100;
            } else {
                $discountAmount = $coupon->discount;
            }

            // Ensure discount doesn't exceed the amount
            $discountAmount = min($discountAmount, $amount);

            // Calculate final amount
            $amount = $amount - $discountAmount;
        }

        // Get selected gateway by slug, e.g., 'stripe'
        $selectedGateway = $organization->paymentGateways()
            ->whereHas('paymentGateway', fn($q) => $q->where('type', strtolower($request->input('gateway'))))
            ->where('is_active', true)
            ->first();

        if (!$selectedGateway) {
            return response()->json(['error' => 'Invalid or inactive gateway'], 400);
        }

        $gatewaySlug = strtolower($request->input('gateway'));
        $credentials = $selectedGateway->credentials;

        try {
            // Update request with discounted amount
            $request->merge(['amount' => $amount]);

            // Add coupon information to the request for later use
            if ($coupon) {
                $request->merge([
                    'original_amount' => $request->input('amount'),
                    'discount_amount' => $discountAmount,
                    'coupon_id' => $coupon->id
                ]);
            }

            $gatewayService = $this->getGatewayService($gatewaySlug, $credentials);
            $result = $gatewayService->process($request);

            // If payment is successful and coupon was used, update coupon usage
            if ($coupon && isset($result->id)) {
                $this->updateCouponUsage($coupon);
            }

            return $this->apiResponse($result, 'Process success', true, 200);
        } catch (\InvalidArgumentException $e) {
            return response()->json(['error' => $e->getMessage()], 400);
        } catch (\Exception $e) {
            Log::error('Payment processing error: ' . $e->getMessage());
            return response()->json(['error' => 'Payment processing failed'], 500);
        }
    }


    /**
     * Process a Stripe payment for course purchase.
     * This can be used in two ways:
     * 1. With a payment_method_id to process payment immediately
     * 2. Without a payment_method_id to create a payment intent for later processing
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function makeStripePayment(Request $request)
    {
        // Validate the request
        $validated = $request->validate([
            // Payment processing options
            'payment_method_id' => 'nullable|string', // Payment method ID from Stripe.js (optional)
            'payment_intent_id' => 'nullable|string', // Payment intent ID for confirming existing payment
            'is_confirm_payment' => 'nullable|boolean', // Flag to indicate if confirming existing payment

            // Course information
            'item_id' => 'nullable|exists:courses,id',
            'description' => 'nullable|string',

            // Payment information
            'amount' => 'nullable|numeric',
            'currency' => 'nullable|string',
            'coupon' => 'nullable|string', // Coupon code

            // Customer information
            'customer_name' => 'nullable|string',
            'customer_email' => 'nullable|email',
            'customer_phone' => 'nullable|string',

            // If you absolutely need to accept card details directly (not recommended)
            // These will only be used if payment_method_id is not provided
            'card_number' => 'nullable|string',
            'card_exp_month' => 'nullable|numeric',
            'exp_month' => 'nullable|numeric',
            'card_exp_year' => 'nullable|numeric',
            'exp_year' => 'nullable|numeric',
            'card_cvc' => 'nullable|string',
            'cvc' => 'nullable|string',
        ]);

        $user = auth()->user();
        $organization = $user->organization;

        $courseId = $validated['item_id'];

        if (!$courseId) {
            return response()->json(['error' => 'Course ID is required.'], 400);
        }

        // Get the course
        $course = Course::findOrFail($courseId);

        // Calculate payable amount - use provided amount if available, otherwise use course price
        // $payableAmount = $course->sale_price > 0 ? $course->sale_price : $course->regular_price;


        $payableAmount = $course->installment_type === "Monthly"
        ? $course->minimum_enroll_amount
        : $course->sale_price;

        // If no amount is provided and course is free, return error
        if (!isset($validated['amount']) && $course->is_free) {
            return response()->json(['error' => 'This course is free. No payment required.'], 400);
        }

        // Apply coupon if provided
        $couponCode = $validated['coupon'] ?? null;
        $coupon = null;
        $discountAmount = 0;

        if ($couponCode) {
            $coupon = \App\Models\Coupon::where('code', $couponCode)
                ->where('is_active', true)
                ->first();

            if (!$coupon) {
                return response()->json(['error' => 'Invalid coupon code'], 400);
            }

            if ($coupon->isExpired()) {
                return response()->json(['error' => 'Coupon has expired'], 400);
            }

            if ($coupon->max_usage > 0 && $coupon->usage_number >= $coupon->max_usage) {
                return response()->json(['error' => 'Coupon usage limit exceeded'], 400);
            }

            // Calculate discount
            if ($coupon->discount_type == 'percentage') {
                $discountAmount = ($payableAmount * $coupon->discount) / 100;
            } else {
                $discountAmount = $coupon->discount;
            }

            // Ensure discount doesn't exceed the amount
            $discountAmount = min($discountAmount, $payableAmount);

            // Calculate final amount
            $payableAmount = $payableAmount - $discountAmount;
        }

        // Check if user is already enrolled
        $isEnrolled = CourseParticipant::where('user_id', $user->id)
            ->where('item_id', $course->id)
            ->where('item_type', 'Course')
            ->exists();

        if ($isEnrolled) {
            return response()->json(['error' => 'You are already enrolled in this course.'], 400);
        }

        // Get Stripe gateway
        $selectedGateway = $organization->paymentGateways()
            ->whereHas('paymentGateway', fn($q) => $q->where('name', 'stripe'))
            ->where('is_active', true)
            ->first();

        if (!$selectedGateway) {
            return response()->json(['error' => 'Stripe payment gateway not configured or inactive'], 400);
        }

        $credentials = $selectedGateway->credentials;

        DB::beginTransaction();
        try {
            // Generate a unique transaction ID
            $transactionId = 'stripe_' . Str::random(16);

            // Prepare payment data
            $paymentData = [
                'amount' => $payableAmount,
                'currency' => $validated['currency'] ?? $course->currency ?? 'USD',
                'description' => $validated['description'] ?? 'Payment for course: ' . $course->title,
                'metadata' => [
                    'course_id' => $course->id,
                    'organization_id' => $organization->id,
                    'user_id' => $user->id,
                    'transaction_id' => $transactionId,
                ],
            ];

            // Get the gateway service
            $gatewayService = $this->getGatewayService('stripe', $credentials);

            // Handle different payment scenarios
            if (isset($validated['payment_intent_id']) && isset($validated['card_number'])) {
                // Special case: Both payment intent ID and card details provided
                // First try to verify if the payment intent is already paid
                $verifyResult = $gatewayService->verify($validated['payment_intent_id']);

                if ($verifyResult['success'] && isset($verifyResult['is_paid']) && $verifyResult['is_paid']) {
                    // Payment intent is already paid, use it
                    $result = $verifyResult;
                    $paymentIntent = $result['payment_intent'];
                    $paymentMethod = $paymentIntent->payment_method;

                    // Try to get card details if available
                    $cardBrand = null;
                    $cardLastFour = null;

                    if ($paymentMethod) {
                        try {
                            $paymentMethodDetails = \Stripe\PaymentMethod::retrieve($paymentMethod);
                            if (isset($paymentMethodDetails->card)) {
                                $cardBrand = $paymentMethodDetails->card->brand;
                                $cardLastFour = $paymentMethodDetails->card->last4;
                            }
                        } catch (\Exception $e) {
                            // Just log the error but continue
                            Log::error('Error retrieving payment method: ' . $e->getMessage());
                        }
                    }
                } else {
                    // Payment intent is not paid, use card details to process payment
                    $paymentData['card_number'] = $validated['card_number'];
                    $paymentData['exp_month'] = $validated['exp_month'];
                    $paymentData['exp_year'] = $validated['exp_year'];
                    $paymentData['cvc'] = $validated['cvc'];

                    $result = $gatewayService->processCardPayment($paymentData);
                }
            } else if (isset($validated['is_confirm_payment']) && $validated['is_confirm_payment'] && isset($validated['payment_intent_id'])) {
                // Scenario 1: Confirm an existing payment intent
                $result = $gatewayService->verify($validated['payment_intent_id']);

                // If payment is not successful, return error
                if (!$result['success'] || !$result['is_paid']) {
                    DB::rollBack();
                    return response()->json([
                        'success' => false,
                        'message' => 'Payment verification failed',
                        'error' => $result['error'] ?? 'Payment not completed',
                    ], 400);
                }

                // Get payment details from the verified payment intent
                $paymentIntent = $result['payment_intent'];
                $paymentMethod = $paymentIntent->payment_method;

                // Try to get card details if available
                $cardBrand = null;
                $cardLastFour = null;

                if ($paymentMethod) {
                    try {
                        $paymentMethodDetails = \Stripe\PaymentMethod::retrieve($paymentMethod);
                        if (isset($paymentMethodDetails->card)) {
                            $cardBrand = $paymentMethodDetails->card->brand;
                            $cardLastFour = $paymentMethodDetails->card->last4;
                        }
                    } catch (\Exception $e) {
                        // Just log the error but continue
                        Log::error('Error retrieving payment method: ' . $e->getMessage());
                    }
                }

            } else if (isset($validated['payment_method_id'])) {
                // Scenario 2: Process payment with payment method ID
                $paymentData['payment_method_id'] = $validated['payment_method_id'];
                $result = $gatewayService->processPaymentWithPaymentMethod($paymentData);

            } else if (isset($validated['card_number']) && isset($validated['exp_month']) &&
                      isset($validated['exp_year']) && isset($validated['cvc'])) {
                // Scenario 3: Process payment with direct card details (not recommended)
                $paymentData['card_number'] = $validated['card_number'];
                $paymentData['exp_month'] = $validated['exp_month'];
                $paymentData['exp_year'] = $validated['exp_year'];
                $paymentData['cvc'] = $validated['cvc'];

                $result = $gatewayService->processCardPayment($paymentData);

            } else {
                // Scenario 4: Just create a payment intent for later confirmation
                $result = $gatewayService->createEmptyPaymentIntent($paymentData);

                // If we're just creating a payment intent, return it without creating records
                if ($result['success']) {
                    return $this->apiResponse([
                        'payment_intent' => $result['payment_intent'],
                        'client_secret' => $result['client_secret'],
                        'course' => $course,
                        'amount' => $payableAmount,
                        'currency' => $course->currency ?? 'USD',
                    ], 'Payment intent created successfully', true, 200);
                }
            }

            if (!$result['success']) {
                DB::rollBack();
                return response()->json([
                    'success' => false,
                    'message' => 'Payment failed',
                    'error' => $result['error'] ?? 'Unknown error',
                ], 400);
            }

            // Get card details from the result if available
            $cardBrand = $result['card_brand'] ?? null;
            $cardLastFour = $result['card_last_four'] ?? null;

            // For payment intent confirmation, we might have already set these values
            if (!isset($paymentIntent)) {
                $paymentIntent = $result['payment_intent'] ?? null;
            }

            // Create payment record
            $payment = Payment::create([
                'user_id' => $user->id,
                'organization_id' => $organization->id,
                'item_id' => $course->id,
                'item_type' => 'Course',
                'is_promo_applied' => $coupon ? true : false,
                'coupon_id' => $coupon ? $coupon->id : null,
                'payable_amount' => $payableAmount,
                'paid_amount' => $payableAmount,
                'discount_amount' => $discountAmount,
                'currency' => $validated['currency'] ?? $course->currency ?? 'USD',
                'transaction_id' => $transactionId,
                'payment_type' => 'Web',
                'payment_method' => 'card',
                'status' => 'Completed',
                'is_verified_payment' => true,
                'is_approved' => true,
                'approved_by' => $user->id,
            ]);

            // Create payment detail record
            $paymentDetail = PaymentDetail::create([
                'payment_id' => $payment->id,
                'user_id' => $user->id,
                'organization_id' => $organization->id,
                'item_id' => $course->id,
                'unit_price' => $coupon ? ($payableAmount + $discountAmount) : $payableAmount,
                'quantity' => 1,
                'total' => $payableAmount,
                'payment_method' => 'card',
                'trx_id' => $transactionId,
                'pay_for' => 'Enrollment',
                'paid_amount' => $payableAmount,
                'payable_amount' => $coupon ? ($payableAmount + $discountAmount) : $payableAmount,
                'due_amount' => 0,
                'discount_amount' => $discountAmount,
                'coupon_code' => $coupon ? $coupon->code : null,
                'currency' => $validated['currency'] ?? $course->currency ?? 'USD',
                'is_approved' => true,
                'customer_name' => $validated['customer_name'] ?? $user->name,
                'customer_email' => $validated['customer_email'] ?? $user->email,
                'customer_phone' => $validated['customer_phone'] ?? null,
            ]);

            // Create course participant record (enrollment)
            $participant = CourseParticipant::create([
                'organization_id' => $organization->id,
                'user_id' => $user->id,
                'item_id' => $course->id,
                'item_price' => $coupon ? ($payableAmount + $discountAmount) : $payableAmount,
                'paid_amount' => $payableAmount,
                'payment_id' => $payment->id,
                'discount' => $discountAmount,
                'item_type' => 'Course',
                'is_trial_taken' => 0,
                'is_active' => 1,
            ]);

            // Update course enrollment count
            $course->number_of_enrolled = $course->number_of_enrolled + 1;
            $course->save();

            // Update coupon usage if a coupon was used
            if ($coupon) {
                $this->updateCouponUsage($coupon);
            }

            // Send success email notification
            $this->sendPaymentSuccessEmail($payment, $course, $user, $organization, $paymentDetail, $validated);

            DB::commit();

            // Prepare response data
            $responseData = [
                'payment' => $payment,
                'payment_detail' => $paymentDetail,
                'course' => $course,
            ];

            // Add coupon information if a coupon was used
            if ($coupon) {
                $responseData['coupon'] = [
                    'code' => $coupon->code,
                    'discount_type' => $coupon->discount_type,
                    'discount' => $coupon->discount,
                    'discount_amount' => $discountAmount,
                ];
            }

            // Add payment intent details if available
            if (isset($paymentIntent)) {
                $responseData['payment_intent_id'] = $paymentIntent->id ?? null;
                $responseData['payment_intent_status'] = $paymentIntent->status ?? null;
            }

            // Add card details if available
            if ($cardBrand) {
                $responseData['card_brand'] = $cardBrand;
            }

            if ($cardLastFour) {
                $responseData['card_last_four'] = $cardLastFour;
            }

            return $this->apiResponse($responseData, 'Course enrollment successful', true, 200);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Stripe payment error: ' . $e->getMessage());
            return response()->json(['error' => 'Payment processing failed: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Process a CyberSource payment for course purchase.
     * Similar to Stripe payment but uses CyberSource gateway.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function makeCybersourcePayment(Request $request)
    {
        // Validate the request
        $validated = $request->validate([
            // Payment processing options
            'transaction_id' => 'nullable|string', // CyberSource transaction ID for verification
            'is_confirm_payment' => 'nullable|boolean', // Flag to indicate if confirming existing payment

            // Course information
            'item_id' => 'nullable|exists:courses,id',
            'description' => 'nullable|string',

            // Payment information
            'amount' => 'nullable|numeric',
            'currency' => 'nullable|string',
            'coupon' => 'nullable|string', // Coupon code

            // Customer information
            'customer_name' => 'nullable|string',
            'customer_email' => 'nullable|email',
            'customer_phone' => 'nullable|string',

            // Card details for CyberSource
            'card_number' => 'nullable|string',
            'card_exp_month' => 'nullable|numeric',
            'exp_month' => 'nullable|numeric',
            'card_exp_year' => 'nullable|numeric',
            'exp_year' => 'nullable|numeric',
            'card_cvc' => 'nullable|string',
            'cvc' => 'nullable|string',
        ]);

        $user = auth()->user();
        $organization = $user->organization;

        $courseId = $validated['item_id'];

        if (!$courseId) {
            return response()->json(['error' => 'Course ID is required.'], 400);
        }

        // Get the course
        $course = Course::findOrFail($courseId);

        // Calculate payable amount
        $payableAmount = $course->installment_type === "Monthly"
            ? $course->minimum_enroll_amount
            : $course->sale_price;

        // If no amount is provided and course is free, return error
        if (!isset($validated['amount']) && $course->is_free) {
            return response()->json(['error' => 'This course is free. No payment required.'], 400);
        }

        // Apply coupon if provided
        $couponCode = $validated['coupon'] ?? null;
        $coupon = null;
        $discountAmount = 0;

        if ($couponCode) {
            $coupon = \App\Models\Coupon::where('code', $couponCode)
                ->where('is_active', true)
                ->first();

            if (!$coupon) {
                return response()->json(['error' => 'Invalid coupon code'], 400);
            }

            if ($coupon->isExpired()) {
                return response()->json(['error' => 'Coupon has expired'], 400);
            }

            if ($coupon->max_usage > 0 && $coupon->usage_number >= $coupon->max_usage) {
                return response()->json(['error' => 'Coupon usage limit exceeded'], 400);
            }

            // Calculate discount
            if ($coupon->discount_type == 'percentage') {
                $discountAmount = ($payableAmount * $coupon->discount) / 100;
            } else {
                $discountAmount = $coupon->discount;
            }

            // Ensure discount doesn't exceed the amount
            $discountAmount = min($discountAmount, $payableAmount);

            // Calculate final amount
            $payableAmount = $payableAmount - $discountAmount;
        }

        // Check if user is already enrolled
        $isEnrolled = CourseParticipant::where('user_id', $user->id)
            ->where('item_id', $course->id)
            ->where('item_type', 'Course')
            ->exists();

        if ($isEnrolled) {
            return response()->json(['error' => 'You are already enrolled in this course.'], 400);
        }

        // Get CyberSource gateway
        $selectedGateway = $organization->paymentGateways()
            ->whereHas('paymentGateway', fn($q) => $q->where('type', 'cybersource'))
            ->where('is_active', true)
            ->first();

        if (!$selectedGateway) {
            return response()->json(['error' => 'CyberSource payment gateway not configured or inactive'], 400);
        }

        $credentials = $selectedGateway->credentials;

        DB::beginTransaction();
        try {
            // Generate a unique transaction ID
            $transactionId = 'cybersource_' . Str::random(16);

            // Prepare payment data
            $paymentData = [
                'amount' => $payableAmount,
                'currency' => $validated['currency'] ?? $course->currency ?? 'USD',
                'description' => $validated['description'] ?? 'Payment for course: ' . $course->title,
                'customer_name' => $validated['customer_name'] ?? $user->name,
                'customer_email' => $validated['customer_email'] ?? $user->email,
                'customer_phone' => $validated['customer_phone'] ?? null,
                'metadata' => [
                    'course_id' => $course->id,
                    'organization_id' => $organization->id,
                    'user_id' => $user->id,
                    'transaction_id' => $transactionId,
                ],
            ];

            // Get the gateway service
            $gatewayService = $this->getGatewayService('cybersource', $credentials);

            // Handle different payment scenarios
            if (isset($validated['is_confirm_payment']) && $validated['is_confirm_payment'] && isset($validated['transaction_id'])) {
                // Scenario 1: Confirm an existing payment
                $result = $gatewayService->verify($validated['transaction_id']);

                // If payment is not successful, return error
                if (!$result['success'] || !$result['is_paid']) {
                    DB::rollBack();
                    return response()->json([
                        'success' => false,
                        'message' => 'Payment verification failed',
                        'error' => $result['error'] ?? 'Payment not completed',
                    ], 400);
                }

            } else if (isset($validated['card_number']) && isset($validated['exp_month']) &&
                      isset($validated['exp_year']) && isset($validated['cvc'])) {
                // Scenario 2: Process payment with card details
                $paymentData['card_number'] = $validated['card_number'];
                $paymentData['exp_month'] = $validated['exp_month'];
                $paymentData['exp_year'] = $validated['exp_year'];
                $paymentData['cvc'] = $validated['cvc'];

                $result = $gatewayService->processCardPayment($paymentData);

            } else {
                // Scenario 3: Just create a payment intent for later confirmation
                $result = $gatewayService->createEmptyPaymentIntent($paymentData);

                // If we're just creating a payment intent, return it without creating records
                if ($result['success']) {
                    return $this->apiResponse([
                        'payment_intent' => $result['payment_intent'],
                        'client_secret' => $result['client_secret'],
                        'course' => $course,
                        'amount' => $payableAmount,
                        'currency' => $course->currency ?? 'USD',
                    ], 'Payment intent created successfully', true, 200);
                }
            }

            if (!$result['success']) {
                DB::rollBack();
                return response()->json([
                    'success' => false,
                    'message' => 'Payment failed',
                    'error' => $result['error'] ?? 'Unknown error',
                ], 400);
            }

            // Get card details from the result if available
            $cardBrand = $result['card_brand'] ?? null;
            $cardLastFour = $result['card_last_four'] ?? null;

            // Create payment record
            $payment = Payment::create([
                'user_id' => $user->id,
                'item_id' => $course->id,
                'item_type' => 'Course',
                'is_promo_applied' => $coupon ? true : false,
                'coupon_id' => $coupon ? $coupon->id : null,
                'payable_amount' => $payableAmount,
                'paid_amount' => $payableAmount,
                'discount_amount' => $discountAmount,
                'currency' => $validated['currency'] ?? $course->currency ?? 'USD',
                'transaction_id' => $transactionId,
                'payment_type' => 'Web',
                'payment_method' => 'card',
                'status' => 'Completed',
                'is_verified_payment' => true,
                'is_approved' => true,
                'approved_by' => $user->id,
            ]);

            // Create payment detail record
            $paymentDetail = PaymentDetail::create([
                'payment_id' => $payment->id,
                'user_id' => $user->id,
                'item_id' => $course->id,
                'unit_price' => $coupon ? ($payableAmount + $discountAmount) : $payableAmount,
                'quantity' => 1,
                'total' => $payableAmount,
                'payment_method' => 'card',
                'trx_id' => $transactionId,
                'pay_for' => 'Enrollment',
                'paid_amount' => $payableAmount,
                'payable_amount' => $coupon ? ($payableAmount + $discountAmount) : $payableAmount,
                'due_amount' => 0,
                'discount_amount' => $discountAmount,
                'coupon_code' => $coupon ? $coupon->code : null,
                'currency' => $validated['currency'] ?? $course->currency ?? 'USD',
                'is_approved' => true,
                'customer_name' => $validated['customer_name'] ?? $user->name,
                'customer_email' => $validated['customer_email'] ?? $user->email,
                'customer_phone' => $validated['customer_phone'] ?? null,
            ]);

            // Create course participant record (enrollment)
            $participant = CourseParticipant::create([
                'user_id' => $user->id,
                'item_id' => $course->id,
                'item_price' => $coupon ? ($payableAmount + $discountAmount) : $payableAmount,
                'paid_amount' => $payableAmount,
                'payment_id' => $payment->id,
                'discount' => $discountAmount,
                'item_type' => 'Course',
                'is_trial_taken' => 0,
                'is_active' => 1,
            ]);

            // Update course enrollment count
            $course->number_of_enrolled = $course->number_of_enrolled + 1;
            $course->save();

            // Update coupon usage if a coupon was used
            if ($coupon) {
                $this->updateCouponUsage($coupon);
            }

            // Send success email notification
            $this->sendPaymentSuccessEmail($payment, $course, $user, $organization, $paymentDetail, $validated);

            DB::commit();

            // Prepare response data
            $responseData = [
                'payment' => $payment,
                'payment_detail' => $paymentDetail,
                'course' => $course,
            ];

            // Add coupon information if a coupon was used
            if ($coupon) {
                $responseData['coupon'] = [
                    'code' => $coupon->code,
                    'discount_type' => $coupon->discount_type,
                    'discount' => $coupon->discount,
                    'discount_amount' => $discountAmount,
                ];
            }

            // Add transaction details if available
            if (isset($result['payment_intent'])) {
                $responseData['transaction_id'] = $result['transaction_id'] ?? $transactionId;
                $responseData['payment_status'] = $result['status'] ?? 'completed';
            }

            // Add card details if available
            if ($cardBrand) {
                $responseData['card_brand'] = $cardBrand;
            }

            if ($cardLastFour) {
                $responseData['card_last_four'] = $cardLastFour;
            }

            return $this->apiResponse($responseData, 'Course enrollment successful via CyberSource', true, 200);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('CyberSource payment error: ' . $e->getMessage());
            return response()->json(['error' => 'Payment processing failed: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Verify a CyberSource payment using the transaction ID.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function verifyCybersourcePayment(Request $request)
    {
        $request->validate([
            'transactionId' => 'required|string',
        ]);

        $organization = auth()->user()->organization;
        $selectedGateway = $organization->paymentGateways()
            ->whereHas('paymentGateway', fn($q) => $q->where('type', 'cybersource'))
            ->where('is_active', true)
            ->first();

        if (!$selectedGateway) {
            return response()->json(['error' => 'CyberSource payment gateway not configured or inactive'], 400);
        }

        $credentials = $selectedGateway->credentials;

        try {
            $gatewayService = $this->getGatewayService('cybersource', $credentials);
            if (!method_exists($gatewayService, 'verify')) {
                return response()->json(['error' => 'Verification not supported for this gateway'], 400);
            }
            $result = $gatewayService->verify($request->input('transactionId'));
            return $this->apiResponse($result, 'CyberSource verification success', true, 200);
        } catch (\InvalidArgumentException $e) {
            return response()->json(['error' => $e->getMessage()], 400);
        } catch (\Exception $e) {
            Log::error('CyberSource payment verification error: ' . $e->getMessage());
            return response()->json(['error' => 'Payment verification failed'], 500);
        }
    }

    /**
     * Get the appropriate gateway service instance.
     * @throws \InvalidArgumentException
     */
    protected function getGatewayService(string $gatewaySlug, array $credentials)
    {
        $classMap = [
            'stripe' => \App\Services\PaymentGateway\StripeGatewayService::class,
            'sslcommerz' => \App\Services\PaymentGateway\SslcommerzGatewayService::class,
            'cybersource' => \App\Services\PaymentGateway\CybersourceGatewayService::class,
        ];
        if (!isset($classMap[$gatewaySlug])) {
            throw new \InvalidArgumentException('Unsupported gateway');
        }
        return new $classMap[$gatewaySlug]($credentials);
    }

    /**
     * Update coupon usage count when a payment is successful
     *
     * @param \App\Models\Coupon $coupon
     * @return void
     */
    protected function updateCouponUsage(\App\Models\Coupon $coupon)
    {
        try {
            // Increment the usage count
            $coupon->usage_number = ($coupon->usage_number ?? 0) + 1;
            $coupon->save();

            // If the coupon has reached its maximum usage, deactivate it
            if ($coupon->max_usage > 0 && $coupon->usage_number >= $coupon->max_usage) {
                $coupon->is_active = false;
                $coupon->save();
            }
        } catch (\Exception $e) {
            // Log the error but don't fail the payment
            Log::error('Error updating coupon usage: ' . $e->getMessage());
        }
    }

    /**
     * Verify a payment using the payment intent ID (e.g., for Stripe).
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function verifyPayment(Request $request)
    {
        $request->validate([
            'paymentIntentId' => 'required|string',
            // Optionally: 'gateway' => 'required|string'
        ]);

        // For now, assume Stripe. If you want to support multiple gateways, add logic here.
        $gatewaySlug = 'stripe'; // or $request->input('gateway')
        $organization = auth()->user()->organization;
        $selectedGateway = $organization->paymentGateways()
            ->whereHas('paymentGateway', fn($q) => $q->where('name', $gatewaySlug))
            ->where('is_active', true)
            ->first();

        if (!$selectedGateway) {
            return response()->json(['error' => 'Invalid or inactive gateway'], 400);
        }

        $credentials = $selectedGateway->credentials;

        try {
            $gatewayService = $this->getGatewayService($gatewaySlug, $credentials);
            if (!method_exists($gatewayService, 'verify')) {
                return response()->json(['error' => 'Verification not supported for this gateway'], 400);
            }
            $result = $gatewayService->verify($request->input('paymentIntentId'));
            return $this->apiResponse($result, 'Verification success', true, 200);
        } catch (\InvalidArgumentException $e) {
            return response()->json(['error' => $e->getMessage()], 400);
        } catch (\Exception $e) {
            \Log::error('Payment verification error: ' . $e->getMessage());
            return response()->json(['error' => 'Payment verification failed'], 500);
        }
    }

    /**
     * Test CyberSource connection and configuration
     */
    public function testCybersourceConnection(Request $request)
    {
        $organization = auth()->user()->organization;
        $selectedGateway = $organization->paymentGateways()
            ->whereHas('paymentGateway', fn($q) => $q->where('type', 'cybersource'))
            ->where('is_active', true)
            ->first();

        if (!$selectedGateway) {
            return response()->json(['error' => 'CyberSource payment gateway not configured or inactive'], 400);
        }

        $credentials = $selectedGateway->credentials;

        try {
            $gatewayService = $this->getGatewayService('cybersource', $credentials);
            $result = $gatewayService->testConnection();
            return $this->apiResponse($result, 'CyberSource connection test completed', true, 200);
        } catch (\Exception $e) {
            Log::error('CyberSource connection test error: ' . $e->getMessage());
            return response()->json(['error' => 'Connection test failed: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Authorize payment using CyberSource token
     */
    public function authorizePayment(Request $request)
    {
        $request->validate([
            'token' => 'required|string',
            'amount' => 'required|numeric|min:0.01',
            'currency' => 'sometimes|string|size:3',
            'course_id' => 'sometimes|integer|exists:courses,id',
            'firstName' => 'sometimes|string|max:255',
            'lastName' => 'sometimes|string|max:255',
            'email' => 'sometimes|email|max:255',
            'address1' => 'sometimes|string|max:255',
            'locality' => 'sometimes|string|max:255',
            'administrativeArea' => 'sometimes|string|max:10',
            'postalCode' => 'sometimes|string|max:20',
            'country' => 'sometimes|string|size:2',
            'phoneNumber' => 'sometimes|string|max:20'
        ]);

        $organization = auth()->user()->organization;
        $selectedGateway = $organization->paymentGateways()
            ->whereHas('paymentGateway', fn($q) => $q->where('type', 'cybersource'))
            ->where('is_active', true)
            ->first();

        if (!$selectedGateway) {
            return response()->json(['error' => 'CyberSource payment gateway not configured or inactive'], 400);
        }

        $credentials = $selectedGateway->credentials;

        try {
            $gatewayService = $this->getGatewayService('cybersource', $credentials);

            $token = $request->input('token');
            $amount = $request->input('amount');
            $currency = $request->input('currency', 'USD');

            // Additional billing data
            $additionalData = [
                'firstName' => $request->input('firstName'),
                'lastName' => $request->input('lastName'),
                'email' => $request->input('email'),
                'address1' => $request->input('address1'),
                'locality' => $request->input('locality'),
                'administrativeArea' => $request->input('administrativeArea'),
                'postalCode' => $request->input('postalCode'),
                'country' => $request->input('country'),
                'phoneNumber' => $request->input('phoneNumber')
            ];

            $result = $gatewayService->authorizePayment($token, $amount, $currency, $additionalData);

            if ($result['success']) {
                // If course_id is provided, create enrollment
                if ($request->has('course_id')) {
                    $course = Course::find($request->input('course_id'));
                    if ($course) {
                        // Create enrollment logic here
                        $enrollment = $this->createEnrollment(auth()->user(), $course, $result);
                        $result['enrollment'] = $enrollment;
                    }
                }

                return $this->apiResponse($result, 'Payment authorized successfully', true, 200);
            } else {
                return response()->json(['error' => $result['error']], 400);
            }

        } catch (\Exception $e) {
            Log::error('CyberSource authorization error: ' . $e->getMessage());
            return response()->json(['error' => 'Authorization failed: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Generate CyberSource captureContext for Flex Microform
     */
    public function generateToken(Request $request)
    {
        $request->validate([
            'targetOrigins' => 'sometimes|array',
            'targetOrigins.*' => 'string|regex:/^https?:\/\/[a-zA-Z0-9.-]+(?::[0-9]+)?$/'
        ]);

        $organization = auth()->user()->organization;
        $selectedGateway = $organization->paymentGateways()
            ->whereHas('paymentGateway', fn($q) => $q->where('type', 'cybersource'))
            ->where('is_active', true)
            ->first();

        if (!$selectedGateway) {
            return response()->json(['error' => 'CyberSource payment gateway not configured or inactive'], 400);
        }

        $credentials = $selectedGateway->credentials;

        try {
            $gatewayService = $this->getGatewayService('cybersource', $credentials);

            $targetOrigins = $request->input('targetOrigins', []);
            $result = $gatewayService->generateCaptureContext($targetOrigins);

            if ($result['success']) {
                return $this->apiResponse($result, 'Capture context generated successfully', true, 200);
            } else {
                return response()->json(['error' => $result['error']], 400);
            }

        } catch (\Exception $e) {
            Log::error('CyberSource capture context generation error: ' . $e->getMessage());
            return response()->json(['error' => 'Capture context generation failed: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Helper method to create enrollment after successful payment
     */
    private function createEnrollment($user, $course, $paymentResult)
    {
        // This is a basic implementation - adjust according to your enrollment logic
        try {
            $enrollment = [
                'user_id' => $user->id,
                'course_id' => $course->id,
                'transaction_id' => $paymentResult['transaction_id'],
                'authorization_code' => $paymentResult['authorization_code'] ?? null,
                'enrolled_at' => now(),
                'status' => 'active'
            ];

            // Add your enrollment creation logic here
            // Example: Enrollment::create($enrollment);

            return $enrollment;
        } catch (\Exception $e) {
            Log::error('Enrollment creation failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Send payment success email notification
     */
    private function sendPaymentSuccessEmail($payment, $course, $user, $organization, $paymentDetail, $requestData)
    {
        try {
            // Determine recipient email - prioritize customer_email from request, then user email
            $recipientEmail = $requestData['customer_email'] ?? $user->email;

            if (!$recipientEmail) {
                Log::warning('No email address available for payment success notification', [
                    'payment_id' => $payment->id,
                    'user_id' => $user->id
                ]);
                return;
            }

            // Send the email
            Mail::to($recipientEmail)->send(new PaymentSuccessfulMail(
                $payment,
                $course,
                $user,
                $organization,
                $paymentDetail
            ));

            Log::info('Payment success email sent successfully', [
                'payment_id' => $payment->id,
                'recipient_email' => $recipientEmail,
                'course_id' => $course->id
            ]);

        } catch (\Exception $e) {
            // Log the error but don't fail the payment process
            Log::error('Failed to send payment success email', [
                'payment_id' => $payment->id,
                'error' => $e->getMessage(),
                'user_id' => $user->id,
                'course_id' => $course->id
            ]);
        }
    }
}
