<?php

namespace App\Http\Controllers;

use App\Models\BatchStudent;
use Illuminate\Http\Request;

class BatchStudentController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(BatchStudent $batchStudent)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(BatchStudent $batchStudent)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, BatchStudent $batchStudent)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(BatchStudent $batchStudent)
    {
        //
    }
}
