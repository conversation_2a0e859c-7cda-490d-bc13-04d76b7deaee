<?php

namespace App\Http\Traits;
use Illuminate\Http\Request;
use App\Http\Traits\HelperTrait;
use <PERSON><PERSON>\Sanctum\PersonalAccessToken;
use App\Events\MessageSent;
use App\Models\Notification as NotificationModel;
use App\Notifications\PushNotification;
use Illuminate\Support\Facades\Notification;

use Jenssegers\Agent\Agent;



trait CommonTrait
{
    use HelperTrait;
    public function getList (Request $request) {
        $results = $this->model::query()
            ->select($this->model->getTable() . '.*');

        if (method_exists($this->model, 'scopeIsActive')) {
            $results = $results->isActive();
        }

        foreach ($this->joins as $table => $on) {
            $results = $results->join($table, $on[0], $on[1], $on[2]);
        }

        $results = $results->where(function($query) use ($request) {
            // Handle search
            if ($request->search) {
                $search = $request->input('search');
                $query->where(function($subQuery) use ($search) {
                    foreach ($this->searchKeys as $key) {
                        $subQuery->orWhere($key, 'like', '%' . $search . '%');
                    }
                });
            }

            // Handle filters
            foreach ($request->except(['search', 'pagination', 'itemsPerPage', 'currentPage']) as $key => $value) {
                if (in_array($key, $this->model->getFillable())) {
                    if ($value) {
                        $query->where($key, $value);
                    }
                }
            }
        });

        if ($request->input('pagination') == 'false') {
            $data = $this->listResource::collection($results->get());
            return $this->apiResponse($data, $this->listMessage, true, 200);
        } else {
            $itemsPerPage = $request->input('items_per_page', 10);
            $currentPage = $request->input('current_page', 0);
            $results = $results->paginate($itemsPerPage, ['*'], 'page', $currentPage + 1 );
            return $this->apiResponse([
                'data' => $this->listResource::collection($results),
                'total' => $results->total(),
                'per_page' => $results->perPage(),
                'current_page' => $results->currentPage() -1,
                'last_page' => $results->lastPage() -1,
                'from' => $results->firstItem() - 1,
                'to' => $results->lastItem() - 1

            ], $this->listMessage, true, 200);
        }
    }

    public function getDetails(Request $request)
    {
        $result = $this->model::find($request->id);
        if (empty($result)) {
            return $this->apiResponse([], 'No data found', false, 404);
        }
        return $this->apiResponse( new $this->detailsResource($result), $this->detailsMessage, true, 200);
    }



    public function checkTokenUser(Request $request)
    {
        return auth('sanctum')->user();
        $token = $request->bearerToken();

        // If token is present, try to find the associated user
        if ($token) {
            // Find the token in the personal_access_tokens table
            $accessToken = PersonalAccessToken::findToken($token);
            // dd($accessToken);
            return $accessToken->tokenable;
        }

        return false;
    }

    public function checkUserAgent(Request $request)
    {
        $deviceInfo = $request->header('User-Agent');

        return $deviceInfo;
    }


    /**
     * Send a test push notification using Laravel's notification system
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function pushNotificationTest () {
        $user = auth('sanctum')->user();
        $message = "Test notification from " . ($user ? $user->name : 'system');
        $title = "Test Notification";
        $type = "test";
        $data = [
            'message' => $message,
            'test_data' => 'This is a test notification with additional data'
        ];

        // Options for the notification
        $options = [
            'title' => $title,
            'type' => $type,
            'channel' => 'notifications'
        ];

        // If user is authenticated, make it a user-specific notification
        if ($user) {
            $options['user_id'] = $user->id;
            $options['organization_id'] = $user->organization_id;

            // Create a notification model instance for the user
            $notificationModel = new NotificationModel([
                'user_id' => $user->id,
                'organization_id' => $user->organization_id,
                'title' => $title,
                'message' => $message,
                'type' => $type,
                'data' => $data,
                'channel' => "user.{$user->id}"
            ]);
        } else {
            // Create a general notification model instance
            $notificationModel = new NotificationModel([
                'title' => $title,
                'message' => $message,
                'type' => $type,
                'data' => $data,
                'channel' => 'notifications'
            ]);
        }

        // Save the notification to the database
        $notificationModel->save();

        // Send the notification
        Notification::send($notificationModel, new PushNotification($message, $data, $options));

        // For debugging, you can also use the direct Pusher approach as a fallback
        // This is useful to verify that Pusher is configured correctly
        if (env('APP_DEBUG', false)) {
            $pusher = new \Pusher\Pusher(
                env('PUSHER_APP_KEY', '0fe88d769e216cb402cf'),
                env('PUSHER_APP_SECRET', '2cf91f652aef149cdc26'),
                env('PUSHER_APP_ID', '*******'),
                [
                    'cluster' => env('PUSHER_APP_CLUSTER', 'ap2'),
                    'useTLS' => true,
                ]
            );

            $channel = $user ? "user.{$user->id}" : 'notifications';
            $pusher->trigger($channel, 'notification.sent', [
                'id' => $notificationModel->id,
                'title' => $title,
                'message' => $message,
                'data' => $data,
                'type' => $type,
                'user_id' => $user ? $user->id : null,
                'organization_id' => $user ? $user->organization_id : null,
                'time' => now()->toIso8601String(),
            ]);
        }

        return response()->json([
            'status' => true,
            'message' => 'Notification sent successfully!',
            'data' => [
                'id' => $notificationModel->id,
                'title' => $title,
                'message' => $message,
                'type' => $type,
                'channel' => $user ? "user.{$user->id}" : 'notifications',
                'user_id' => $user ? $user->id : null,
                'organization_id' => $user ? $user->organization_id : null
            ]
        ], 200);
    }

    /**
     * Send a notification to a specific user
     *
     * @param int $userId User ID to send notification to
     * @param string $message Notification message
     * @param string $title Notification title
     * @param string $type Notification type
     * @param array $data Additional data to include
     * @return \App\Models\Notification
     */
    public function sendUserNotification($userId, $message, $title = null, $type = 'general', $data = [])
    {
        // Log the notification attempt for debugging
        \Illuminate\Support\Facades\Log::info('Sending user notification', [
            'user_id' => $userId,
            'message' => $message,
            'title' => $title,
            'type' => $type
        ]);

        $user = \App\Models\User::find($userId);

        if (!$user) {
            \Illuminate\Support\Facades\Log::warning('User not found for notification', [
                'user_id' => $userId
            ]);
            return null;
        }

        $options = [
            'title' => $title,
            'type' => $type,
            'user_id' => $user->id,
            'organization_id' => $user->organization_id
        ];

        // Create a notification model instance
        $notificationModel = new NotificationModel([
            'user_id' => $user->id,
            'organization_id' => $user->organization_id,
            'title' => $title,
            'message' => $message,
            'type' => $type,
            'data' => $data,
            'channel' => "user.{$user->id}"
        ]);

        // Save the notification to the database
        $notificationModel->save();

        try {
            // Send the notification
            Notification::send($notificationModel, new PushNotification($message, $data, $options));

            // Also try direct Pusher approach for debugging
            $pusher = new \Pusher\Pusher(
                env('PUSHER_APP_KEY', '0fe88d769e216cb402cf'),
                env('PUSHER_APP_SECRET', '2cf91f652aef149cdc26'),
                env('PUSHER_APP_ID', '*******'),
                [
                    'cluster' => env('PUSHER_APP_CLUSTER', 'ap2'),
                    'useTLS' => true,
                ]
            );

            $channelName = "user.{$user->id}";
            $eventName = 'notification.sent';
            $eventData = [
                'id' => $notificationModel->id,
                'title' => $title,
                'message' => $message,
                'data' => $data,
                'type' => $type,
                'user_id' => $user->id,
                'organization_id' => $user->organization_id,
                'channel' => $channelName,
                'time' => now()->toIso8601String(),
            ];

            $pusherResult = $pusher->trigger($channelName, $eventName, $eventData);

            \Illuminate\Support\Facades\Log::info('Pusher direct trigger result for user notification', [
                'result' => $pusherResult,
                'channel' => $channelName,
                'event' => $eventName,
                'user_id' => $user->id
            ]);

        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error sending user notification', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_id' => $user->id
            ]);
        }

        return $notificationModel;
    }

    /**
     * Send a notification to all users in an organization
     *
     * @param int $organizationId Organization ID to send notification to
     * @param string $message Notification message
     * @param string $title Notification title
     * @param string $type Notification type
     * @param array $data Additional data to include
     * @return \App\Models\Notification
     */
    public function sendOrganizationNotification($organizationId, $message, $title = null, $type = 'general', $data = [])
    {
        // Log the notification attempt for debugging
        \Illuminate\Support\Facades\Log::info('Sending organization notification', [
            'organization_id' => $organizationId,
            'message' => $message,
            'title' => $title,
            'type' => $type
        ]);

        $options = [
            'title' => $title,
            'type' => $type,
            'organization_id' => $organizationId
        ];

        // Create a notification model instance
        $notificationModel = new NotificationModel([
            'organization_id' => $organizationId,
            'title' => $title,
            'message' => $message,
            'type' => $type,
            'data' => $data,
            'channel' => "organization.{$organizationId}"
        ]);

        // Save the notification to the database
        $notificationModel->save();

        try {
            // Send the notification
            Notification::send($notificationModel, new PushNotification($message, $data, $options));

            // Also try direct Pusher approach for debugging
            $pusher = new \Pusher\Pusher(
                env('PUSHER_APP_KEY', '0fe88d769e216cb402cf'),
                env('PUSHER_APP_SECRET', '2cf91f652aef149cdc26'),
                env('PUSHER_APP_ID', '*******'),
                [
                    'cluster' => env('PUSHER_APP_CLUSTER', 'ap2'),
                    'useTLS' => true,
                ]
            );

            $channelName = "organization.{$organizationId}";
            $eventName = 'notification.sent';
            $eventData = [
                'id' => $notificationModel->id,
                'title' => $title,
                'message' => $message,
                'data' => $data,
                'type' => $type,
                'organization_id' => $organizationId,
                'channel' => $channelName,
                'time' => now()->toIso8601String(),
            ];

            $pusherResult = $pusher->trigger($channelName, $eventName, $eventData);

            \Illuminate\Support\Facades\Log::info('Pusher direct trigger result', [
                'result' => $pusherResult,
                'channel' => $channelName,
                'event' => $eventName
            ]);

        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error sending notification', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }

        return $notificationModel;
    }

    /**
     * Legacy method for broadcasting messages
     *
     * @deprecated Use sendUserNotification or sendOrganizationNotification instead
     * @param string $message
     * @return mixed
     */
    public function broacastMessage($message)
    {
        return broadcast(new MessageSent($message));
    }
}
