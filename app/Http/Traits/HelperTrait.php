<?php

namespace App\Http\Traits;

use Carbon\Carbon;
use GuzzleHttp\Client;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Pagination\Paginator;
use App\Models\Organization;
// use Intervention\Image\Facades\Image;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;
use Aws\S3\S3Client;
use Aws\Exception\AwsException;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;

trait HelperTrait
{
    private function applySorting($query, Request $request): void
    {
        $sortBy = $request->input('sortBy', 'id');
        $sortDesc = $request->boolean('sortDesc', true) ? 'desc' : 'asc';
        $query->orderBy($sortBy, $sortDesc);
    }

    private function applySearch($query, ?string $searchValue, array $searchKeys): void
    {
        if ($searchValue) {
            $query->where(function ($query) use ($searchValue, $searchKeys) {
                foreach ($searchKeys as $key) {
                    $query->orWhereRaw('LOWER('.$key.') LIKE ?', ['%'.strtolower($searchValue).'%']);
                }
            });
        }
    }

    private function paginateOrGet($query, Request $request): Collection|LengthAwarePaginator|array
    {
        if ($request->boolean('pagination', true)) {
            $itemsPerPage = $request->input('itemsPerPage', 10);
            $currentPage = Paginator::resolveCurrentPage('page');

            return $query->paginate($itemsPerPage, ['*'], 'page', $currentPage);
        }

        return $query->get();
    }

    private function applyFilters($query, $request, $filters)
    {
        foreach ($filters as $key => $operator) {
            if ($request->filled($key)) {
                $value = $request->input($key);
                switch ($operator) {
                    case '=':
                        $query->where($key, '=', $value);
                        break;
                    case 'like':
                        $query->where($key, 'like', '%'.$value.'%');
                        break;
                    case '>':
                    case '<':
                    case '>=':
                    case '<=':
                        $query->where($key, $operator, $value);
                        break;
                        // Add more cases for additional operators as needed.
                    default:
                        // Handle other operators or throw an exception if needed.
                        break;
                }
            }
        }
    }

    protected function apiResponse($data = null, $message = null, $status = null, $statusCode = 200)
    {
        $array = [
            'status' => $status,
            'message' => $message,
            'data' => $data,
        ];

        return response()->json($array, $statusCode);
    }

    protected function codeGenerator($prefix, $model)
    {
        if ($model::count() == 0) {
            $newId = $prefix.'-'.str_pad(1, 5, 0, STR_PAD_LEFT);

            return $newId;
        }
        $lastId = $model::orderBy('id', 'desc')->first()->id;
        $lastIncrement = substr($lastId, -3);
        $newId = $prefix.'-'.str_pad($lastIncrement + 1, 5, 0, STR_PAD_LEFT);
        $newId++;

        return $newId;
    }


    // Function to generate a random hex color
    public function randomColor()
    {
        return sprintf('#%06X', mt_rand(0, 0xFFFFFF));
    }

    // Function to calculate luminance of a color (brightness)
    public function getLuminance($hexColor)
    {
        $color = sscanf($hexColor, "#%02x%02x%02x");
        $r = $color[0] / 255;
        $g = $color[1] / 255;
        $b = $color[2] / 255;

        // Using the luminance formula (standard for human perception)
        return 0.2126 * $r + 0.7152 * $g + 0.0722 * $b;
    }


    //delete image Ex:icon/12346.png
    protected function deleteImage($image)
    {
        $image_path = public_path('uploads/'.$image);
        if (file_exists($image_path)) {
            unlink($image_path);
        }

        return true;
    }

    public function addHour($date_time, $hour)
    {
        return date('Y-m-d H:i:s', strtotime('+'.$hour.' hours', strtotime($date_time)));
    }

    public function getUTCTime($date)
    {
        return new Carbon($date, 'UTC');
    }

    public function getTimeDifference($start, $end)
    {
        $start_time = new Carbon($start);
        $end_time = new Carbon($end);

        return $start_time->diff($end_time)->format('%H:%I:%S');
    }

    public function calculateTime($time_array)
    {
        $sum = strtotime('00:00:00');
        $totaltime = 0;

        foreach ($time_array as $element) {
            $timeinsec = strtotime($element) - $sum;
            $totaltime = $totaltime + $timeinsec;
        }

        $h = intval($totaltime / 3600);
        $totaltime = $totaltime - ($h * 3600);
        $m = intval($totaltime / 60);
        $s = $totaltime - ($m * 60);

        return "$h:$m:$s";
    }

    protected function successResponse($data, $message, $statusCode = 200): JsonResponse
    {
        $array = [
            'data' => $data,
            'message' => $message,
        ];

        return response()->json($array, $statusCode);
    }

    protected function errorResponse($error, $message, $statusCode): JsonResponse
    {
        $array = [
            'errors' => $error,
            'message' => $message,
        ];

        return response()->json($array, $statusCode);
    }

    protected function generateOTP() {
        // return 1234;
        return rand(1000, 9999);
    }
    protected function sendSMS($phone, $message)
    {
        // return true;
        $client = new Client();
        $url = "https://api.sms.net.bd/sendsms?api_key=9NuhTjtgko80Pc8JcnTUzWnKJ3ATR8QauHtfsU1S&msg=". $message."&to=". $phone;
        return $res = $client->request('GET', $url);
    }

    private function getLogo ($organizationId) {
        $organization = Organization::find($organizationId);
        if ($organization) {
            return $organization->logo;
        }
        return null;
    }
    /**
     * Basic image upload with AWS S3 support
     */
    protected function imageUpload($request, $imageField, $destination, $oldImage = null, $aws = false)
    {
        if ($aws) {
            try {
                $result = $this->s3FileUpload($request, $imageField, $destination, $oldImage);
                return is_array($result) ? $result['path'] : $result;
            } catch (\Exception $e) {
                Log::error('AWS S3 upload failed: ' . $e->getMessage());
                if (config('filesystems.default') === 's3') {
                    throw $e;
                }
                $aws = false;
            }
        }

        return $this->localImageUpload($request->file($imageField), $destination, $oldImage);
    }


    /**
 * Upload multiple files with S3 support
 *
 * @param array|\Illuminate\Http\UploadedFile[] $files Array of uploaded files
 * @param string $destination Destination folder path
 * @param bool $aws Whether to use AWS S3
 * @param string|null $prefix Optional filename prefix
 * @return array Array of uploaded file paths/URLs
 */
protected function uploadMultipleFiles($files, $destination, $aws = false, $prefix = null)
{
    $uploadedFiles = [];

    foreach ($files as $index => $file) {
        if (!$file->isValid()) {
            continue;
        }

        $fileName = ($prefix ? $prefix.'_' : '') . time() . '_' . $index . '.' . $file->getClientOriginalExtension();

        if ($aws) {
            try {
                $uploaded = $this->s3FileUploadDirect($file, $destination, null);
                $uploadedFiles[] = $uploaded['path'] ?? null;
            } catch (\Exception $e) {
                Log::error("S3 upload failed for file {$index}: " . $e->getMessage());
                // Fallback to local upload
                $uploadedFiles[] = $this->localFileUpload($file, $destination, $fileName);
            }
        } else {
            $uploadedFiles[] = $this->localFileUpload($file, $destination, $fileName);
        }
    }

    return array_filter($uploadedFiles);
}

    /**
     * Enhanced image upload with detailed S3 response
     */
    protected function imageUploadEnhanced($request, $imageField, $destination, $oldImage = null, $aws = false)
    {
        if ($aws) {
            try {
                return $this->s3FileUpload($request, $imageField, $destination, $oldImage);
            } catch (\Exception $e) {
                Log::error('AWS S3 upload failed in imageUploadEnhanced: ' . $e->getMessage());
                throw $e;
            }
        }

        $file = $request->file($imageField);
        if (!$file) {
            return null;
        }

        $path = $this->localImageUpload($file, $destination, $oldImage);
        $url = asset('uploads/' . $path);

        return [
            'path' => $path,
            'url' => $url,
            'full_url' => $url,
            'storage' => 'local',
            'file_name' => basename($path),
            'original_name' => $file->getClientOriginalName(),
            'size' => $file->getSize(),
            'mime_type' => $file->getMimeType()
        ];
    }

    /**
     * Generic file upload with S3 support
     */
    protected function fileUpload($file, $destination, $oldFile = null, $aws = false)
    {
        if ($aws) {
            try {
                $result = $this->s3FileUploadDirect($file, $destination, $oldFile);
                return is_array($result) ? $result['path'] : $result;
            } catch (\Exception $e) {
                Log::error('AWS S3 upload failed: ' . $e->getMessage());
            }
        }

        return $this->localImageUpload($file, $destination, $oldFile);
    }

    /**
     * Service-level image upload with S3 support
     */
    protected function imageUploadService($imageField, $destination, $oldImage = null, $aws = false)
    {
        if ($aws) {
            try {
                $result = $this->s3FileUploadDirect($imageField, $destination, $oldImage);
                return is_array($result) ? $result['path'] : $result;
            } catch (\Exception $e) {
                Log::error('AWS S3 upload failed: ' . $e->getMessage());
            }
        }

        return $this->localImageUpload($imageField, $destination, $oldImage);
    }

    /**
     * Image upload with filename prefix and S3 support
     */
    protected function imageUploadWithPrefix($request, $image, $destination, $prefix, $old_image = null, $aws = false)
    {
        if (!$request->hasFile($image)) {
            return null;
        }

        $file = $request->file($image);
        $fileName = $prefix.'_'.time().'.'.$file->getClientOriginalExtension();

        if ($aws) {
            try {
                // For S3, we'll keep the same naming convention
                $result = $this->s3FileUpload($request, $image, $destination, $old_image);
                return is_array($result) ? $result['path'] : $result;
            } catch (\Exception $e) {
                Log::error('AWS S3 upload failed: ' . $e->getMessage());
                $aws = false;
            }
        }

        // Local upload
        if ($old_image) {
            $oldImagePath = public_path('uploads/'.$old_image);
            if (file_exists($oldImagePath)) {
                @unlink($oldImagePath);
            }
        }

        $imageDestination = public_path('uploads/'.$destination);
        if (!file_exists($imageDestination)) {
            mkdir($imageDestination, 0755, true);
        }

        $file->move($imageDestination, $fileName);
        return $destination.'/'.$fileName;
    }

    /**
     * Unified local image upload handler
     */
    protected function localImageUpload($file, $destination, $oldFile = null)
    {
        if (!$file) {
            return null;
        }

        // Remove old file if exists
        if ($oldFile) {
            $oldFilePath = public_path('uploads/'.$oldFile);
            if (file_exists($oldFilePath)) {
                @unlink($oldFilePath);
            }
        }

        // Create directory if needed
        $imageDestination = public_path('uploads/'.$destination);
        if (!file_exists($imageDestination)) {
            mkdir($imageDestination, 0755, true);
        }

        // Process upload
        $fileName = 'bb_'.time().'.'.$file->getClientOriginalExtension();
        $file->move($imageDestination, $fileName);

        return $destination.'/'.$fileName;
    }

    /**
     * Generate course thumbnail (keeps original implementation)
     */
    protected function generateCourseThumbnail($course)
    {
        $title = $course->title;
        $thumbnailPath = public_path('thumbnail/');
        if (!file_exists($thumbnailPath)) {
            mkdir($thumbnailPath, 0755, true);
        }

        $thumbName = str_replace(' ', '_', $title);
        $thumbnailName = $thumbName . time() . '_thumbnail.png';

        $image = new \Imagick();
        $backgroundColor = $this->randomColor();
        $luminance = $this->getLuminance($backgroundColor);
        $fontColor = ($luminance < 0.5) ? '#FFFFFF' : '#000000';
        $image->newImage(800, 400, new \ImagickPixel($backgroundColor));
        $image->setImageFormat('png');

        if ($this->getLogo($course->organization_id)) {
            $waterMark = public_path('uploads/'. $this->getLogo($course->organization_id));
            $watermark = new \Imagick($waterMark);
            $watermark->resizeImage(250, 0, \Imagick::FILTER_LANCZOS, 1);
            $watermark->evaluateImage(\Imagick::EVALUATE_MULTIPLY, 1, \Imagick::CHANNEL_ALPHA);
            $x = 50;
            $y = 50;
            $image->compositeImage($watermark, \Imagick::COMPOSITE_OVER, $x, $y);
        }

        // Add course title
        $draw = new \ImagickDraw();
        $draw->setFillColor(new \ImagickPixel($fontColor));
        $draw->setFont(public_path('fonts/noto-sans-bengali/NotoSansBengali-VariableFont_wdth,wght.ttf'));
        $draw->setTextAlignment(\Imagick::ALIGN_CENTER);
        $maxWidth = 750;
        $fontSize = 24;
        $draw->setFontSize($fontSize);
        $textWidth = $image->queryFontMetrics($draw, $title)['textWidth'];
        while ($textWidth > $maxWidth) {
            $fontSize--;
            $draw->setFontSize($fontSize);
            $textWidth = $image->queryFontMetrics($draw, $title)['textWidth'];
        }
        while ($textWidth < $maxWidth) {
            $fontSize++;
            $draw->setFontSize($fontSize);
            $textWidth = $image->queryFontMetrics($draw, $title)['textWidth'];
        }
        $image->annotateImage($draw, 400, 300, 0, $title);

        // Add pricing capsule
        if ($course->sale_price || $course->monthly_amount || $course->is_free) {
            $capsuleDraw = new \ImagickDraw();
            $textDraw = new \ImagickDraw();

            if ($course->sale_price) {
                $capsuleText = $course->sale_price . ' '. $course->currency;
                $capsuleColor = 'red';
            } elseif ($course->monthly_amount) {
                $capsuleText = $course->monthly_amount . ' / Month';
                $capsuleColor = 'blue';
            } else {
                $capsuleText = 'Free';
                $capsuleColor = 'green';
            }

            $textDraw->setFont(public_path('fonts/arial.ttf'));
            $textDraw->setFontSize(24);
            $metrics = $image->queryFontMetrics($textDraw, $capsuleText);
            $textWidth = $metrics['textWidth'];
            $textHeight = $metrics['textHeight'];

            $padding = 20;
            $capsuleWidth = $textWidth + $padding;
            $capsuleHeight = $textHeight + $padding;
            $x1 = 700 - ($capsuleWidth / 2);
            $x2 = 700 + ($capsuleWidth / 2);
            $y1 = 50;
            $y2 = $y1 + $capsuleHeight;

            $capsuleDraw->setFillColor(new \ImagickPixel($capsuleColor));
            $capsuleDraw->roundRectangle($x1, $y1, $x2, $y2, 15, 15);
            $image->drawImage($capsuleDraw);

            $textDraw->setFillColor(new \ImagickPixel('white'));
            $textDraw->setTextAlignment(\Imagick::ALIGN_CENTER);
            $image->annotateImage($textDraw, 700, $y1 + ($capsuleHeight / 2) + ($textHeight / 4), 0, $capsuleText);
        }

        $imagePath = public_path('uploads/thumbnail/' . $thumbnailName);
        $image->writeImage($imagePath);

        return 'thumbnail/' . $thumbnailName;
    }

    /**
     * Upload file to AWS S3
     */
    protected function s3FileUpload($request, $fileName, $path, $oldFile = null)
    {
        if (!$request->hasFile($fileName)) {
            return null;
        }

        try {
            // Delete old file if exists
            if ($oldFile) {
                $this->s3FileDelete($oldFile);
            }

            $file = $request->file($fileName);
            if (!$file->isValid()) {
                throw new \Exception('Invalid file upload');
            }

            $path = rtrim($path, '/');
            $extension = $file->getClientOriginalExtension();
            $uniqueName = 'bb_' . time() . '_' . Str::uuid() . '.' . $extension;
            $key = $path . '/' . $uniqueName;

            Log::info('Attempting S3 upload', [
                'file_name' => $fileName,
                'original_name' => $file->getClientOriginalName(),
                'path' => $path,
                'key' => $key,
                'size' => $file->getSize(),
                'mime_type' => $file->getMimeType()
            ]);

            try {
                $uploaded = Storage::disk('s3')->putFileAs($path, $file, $uniqueName);
                if (!$uploaded) {
                    throw new \Exception('putFileAs returned false');
                }
                Log::info('S3 upload successful using putFileAs: ' . $uploaded);
            } catch (\Exception $e1) {
                Log::warning('putFileAs failed, trying alternative method: ' . $e1->getMessage());
                $fileContents = file_get_contents($file->getPathname());
                $uploaded = Storage::disk('s3')->put($key, $fileContents);
                if (!$uploaded) {
                    throw new \Exception('Alternative upload method also failed');
                }
                Log::info('S3 upload successful using put method: ' . $uploaded);
            }

            if (!Storage::disk('s3')->exists($key)) {
                throw new \Exception('File upload verification failed - file not found in S3');
            }

            $url = Storage::disk('s3')->url($key);

            return [
                'path' => $key,
                'url' => $url,
                'full_url' => $url,
                'storage' => 's3',
                'file_name' => $uniqueName,
                'original_name' => $file->getClientOriginalName(),
                'size' => $file->getSize(),
                'mime_type' => $file->getMimeType()
            ];

        } catch (AwsException $e) {
            Log::error('S3 Upload AWS error: ' . $e->getAwsErrorMessage());
            throw new \Exception('S3 Upload error: ' . $e->getAwsErrorMessage());
        } catch (\Exception $e) {
            Log::error('File upload error: ' . $e->getMessage());
            throw new \Exception('File upload error: ' . $e->getMessage());
        }
    }

    /**
     * Upload file to AWS S3 using direct file object
     */
    protected function s3FileUploadDirect($file, $path, $oldFile = null)
    {
        if (!$file || !$file->isValid()) {
            return null;
        }

        try {
            if ($oldFile) {
                $this->s3FileDelete($oldFile);
            }

            $path = rtrim($path, '/') . '/';
            $extension = $file->getClientOriginalExtension();
            $uniqueName = 'bb_' . time() . '_' . Str::uuid() . '.' . $extension;
            $key = $path . $uniqueName;

            $uploaded = Storage::disk('s3')->putFileAs($path, $file, $uniqueName, 'public');
            if (!$uploaded) {
                throw new \Exception('Failed to upload file to S3');
            }

            $url = Storage::disk('s3')->url($key);

            return [
                'path' => $key,
                'url' => $url,
                'full_url' => $url
            ];

        } catch (AwsException $e) {
            Log::error('S3 Upload AWS error: ' . $e->getAwsErrorMessage());
            throw new \Exception('S3 Upload error: ' . $e->getAwsErrorMessage());
        } catch (\Exception $e) {
            Log::error('File upload error: ' . $e->getMessage());
            throw new \Exception('File upload error: ' . $e->getMessage());
        }
    }

    /**
     * Delete file from AWS S3
     */
    protected function s3FileDelete($filePath)
    {
        if (!$filePath) {
            return false;
        }

        try {
            if (str_contains($filePath, 'amazonaws.com')) {
                $parsedUrl = parse_url($filePath);
                $filePath = ltrim($parsedUrl['path'], '/');
            }

            if (Storage::disk('s3')->exists($filePath)) {
                Storage::disk('s3')->delete($filePath);
                Log::info('S3 file deleted: ' . $filePath);
                return true;
            }

            return false;
        } catch (AwsException $e) {
            Log::error('S3 Delete AWS error: ' . $e->getAwsErrorMessage());
            return false;
        } catch (\Exception $e) {
            Log::error('S3 Delete error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get a temporary signed URL for private S3 files
     */
    protected function s3GetSignedUrl($filePath, $expirationMinutes = 60)
    {
        if (!$filePath) {
            return null;
        }

        try {
            if (str_contains($filePath, 'amazonaws.com')) {
                $parsedUrl = parse_url($filePath);
                $filePath = ltrim($parsedUrl['path'], '/');
            }

            if (!Storage::disk('s3')->exists($filePath)) {
                return null;
            }

            return Storage::disk('s3')->temporaryUrl(
                $filePath,
                now()->addMinutes($expirationMinutes)
            );

        } catch (AwsException $e) {
            Log::error('S3 Signed URL AWS error: ' . $e->getAwsErrorMessage());
            return null;
        } catch (\Exception $e) {
            Log::error('S3 Signed URL error: ' . $e->getMessage());
            return null;
        }
    }



}
