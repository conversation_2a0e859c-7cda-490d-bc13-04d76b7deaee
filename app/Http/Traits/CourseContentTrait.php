<?php

namespace App\Http\Traits;

use App\Models\CourseOutline;

trait CourseContentTrait
{
    // Automatically insert or update CourseOutline
    public static function bootCourseContentTrait()
    {
      


        static::created(function ($model) {
            $outline = new CourseOutline();
            self::populateOutline($outline, $model);
            self::assignModelIdToOutline($outline, $model);
            $outline->save();
        });

        static::updated(function ($model) {
            $outline = CourseOutline::where(self::getOutlineForeignKey($model), $model->id)->first();
            if ($outline) {
                self::populateOutline($outline, $model);
                $outline->save();
            }
        });
    }

    /**
     * Populate the CourseOutline model with data from the original model.
     *
     * @param  mixed  $model
     * @return void
     */
    protected static function populateOutline(CourseOutline $outline, $model)
    {
        $outline->title = $model->title;
        $outline->title_bn = $model->title_bn;
        $outline->course_id = $model->course_id;
        $outline->course_category_id = $model->course_category_id ?? null;
        $outline->class_level_id = $model->class_level_id ?? null;
        $outline->subject_id = $model->subject_id ?? null;
        $outline->content_subject_id = $model->content_subject_id ?? null;
        $outline->chapter_id = $model->chapter_id ?? null;
        $outline->is_free = $model->is_free;
        $outline->sequence = $model->sequence;
        $outline->is_active = $model->is_active;
    }

    /**
     * Assign the model ID to the appropriate CourseOutline field.
     *
     * @param  mixed  $model
     * @return void
     */
    protected static function assignModelIdToOutline(CourseOutline $outline, $model)
    {
        $modelMapping = [
            'App\Models\ChapterScript' => 'chapter_script_id',
            'App\Models\ChapterVideo' => 'chapter_video_id',
            'App\Models\ChapterQuiz' => 'chapter_quiz_id',
        ];

        $modelClass = get_class($model);
        if (isset($modelMapping[$modelClass])) {
            $outline->{$modelMapping[$modelClass]} = $model->id;
        }
    }

    /**
     * Get the foreign key used to link the CourseOutline to the model.
     *
     * @param  mixed  $model
     * @return string|null
     */
    protected static function getOutlineForeignKey($model)
    {
        $modelMapping = [
            'App\Models\ChapterScript' => 'chapter_script_id',
            'App\Models\ChapterVideo' => 'chapter_video_id',
            'App\Models\ChapterQuiz' => 'chapter_quiz_id',
        ];

        $modelClass = get_class($model);

        return $modelMapping[$modelClass] ?? null;
    }
}
