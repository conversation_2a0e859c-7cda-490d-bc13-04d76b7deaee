<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ChapterVideoRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'title' => 'required|string',
            'title_bn' => 'nullable|string',
            'class_level_id' => 'nullable|integer',
            'course_id' => 'required|integer',
            'course_category_id' => 'nullable|integer',
            'subject_id' => 'nullable|integer',
            'chapter_id' => 'nullable|integer',
            'author_name' => 'nullable|string',
            'author_details' => 'nullable|string',
            'description' => 'nullable|string',
            'raw_url' => 'nullable|string',
            's3_url' => 'nullable|string',
            'youtube_url' => 'nullable|string',
            'download_url' => 'nullable|string',
            'thumbnail' => 'nullable|image|mimes:png,jpg,jpeg|max:2048',
            'duration' => 'nullable|string',
            'price' => 'required|numeric',
            'rating' => 'nullable|numeric',
        ];
    }
}
