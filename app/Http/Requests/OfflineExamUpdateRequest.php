<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class OfflineExamUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'course_id' => 'nullable|exists:courses,id',
            'title' => 'nullable|string|max:255',
            'title_bn' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'mcq_mark' => 'nullable|numeric',
            'written_mark' => 'nullable|numeric',
            'assignment_mark' => 'nullable|numeric',
            'presentation_mark' => 'nullable|numeric',
            'practical_mark' => 'nullable|numeric',
            'pass_mark' => 'nullable|numeric',
            'total_mark' => 'nullable|numeric',
            'duration' => 'nullable|numeric',
            'exam_date' => 'nullable|date',
        ];
    }
}
