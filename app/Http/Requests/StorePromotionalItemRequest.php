<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StorePromotionalItemRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'title' => 'required|string|max:255',
            'title_bn' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'type' => ['required', Rule::in(['course', 'video', 'script', 'quiz', 'ebook', 'youtube'])],
            'course_id' => 'nullable|required_if:type,course|exists:courses,id',
            'chapter_video_id' => 'nullable|required_if:type,video|exists:chapter_videos,id',
            'chapter_script_id' => 'nullable|required_if:type,script|exists:chapter_scripts,id',
            'chapter_quiz_id' => 'nullable|required_if:type,quiz|exists:chapter_quizzes,id',
            'ebook_id' => 'nullable|required_if:type,ebook|exists:ebooks,id',
            'youtube_url' => 'nullable|required_if:type,youtube|url',
        ];
    }

    public function validated($key = null, $default = null)
    {
        $data = parent::validated();
        $data['organization_id'] = auth()->user()->organization_id;
        $data['created_by'] = auth()->id();
        $data['is_active'] = true;
        return $data;
    }
}
