<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class MentorCreateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required',
            'username' => 'nullable|unique:users,username',
            'email' => 'nullable|unique:users,email',
            'contact_no' => 'nullable|unique:users,contact_no',
            'password' => 'required',
            // Additional mentor fields can be added here
            'education' => 'nullable|string',
            'institute' => 'nullable|string',
            'device_id' => 'nullable|string',
            'referral_code' => 'nullable|string',
            'referred_code' => 'nullable|string',
            'alternative_contact_no' => 'nullable|string',
            'gender' => 'nullable|string',
            'bio' => 'nullable|string',
            'father_name' => 'nullable|string',
            'mother_name' => 'nullable|string',
            'religion' => 'nullable|string',
            'marital_status' => 'nullable|string',
            'date_of_birth' => 'nullable|date',
            'profession' => 'nullable|string',
            'current_address' => 'nullable|string',
            'permanent_address' => 'nullable|string',
            'division_id' => 'nullable|integer',
            'district_id' => 'nullable|integer',
            'city_id' => 'nullable|integer',
            'area_id' => 'nullable|integer',
            'nid_no' => 'nullable|string',
            'birth_certificate_no' => 'nullable|string',
            'passport_no' => 'nullable|string',
            'intro_video' => 'nullable|string',
            'status' => 'nullable|string',
            'is_foreigner' => 'nullable|boolean',
            'is_life_couch' => 'nullable|boolean',
            'is_host_staff' => 'nullable|boolean',
            'is_host_certified' => 'nullable|boolean',
            'is_active' => 'nullable|boolean',
            'rating' => 'nullable|numeric',
            'approval_date' => 'nullable|date',
            'host_rank_number' => 'nullable|string',
            'blood_group' => 'nullable|string',
            'is_featured' => 'nullable|boolean',
        ];
    }
}
