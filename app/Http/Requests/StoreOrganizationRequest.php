<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreOrganizationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'details' => 'nullable|string',
            'short_name' => 'nullable|string|max:255|unique:organizations,short_name',
            'address' => 'nullable|string|max:255',
            'email' => 'nullable|string|email|max:255|unique:organizations,email',
            'contact_no' => 'nullable|string|max:20',
            'logo' => 'nullable|file|mimes:jpg,jpeg,png|max:2048',
            'menu_position' => 'nullable|in:top,left,right,bottom',
            'contact_person' => 'nullable|string',
            'is_active' => 'boolean',
            'banner' => 'nullable|file|mimes:jpg,jpeg,png|max:2048',
            'contact_number' => 'nullable|string|max:20',
            'hotline_number' => 'nullable|string|max:20',
            'host_url' => 'nullable|string|max:255|url',
            'asset_host' => 'nullable|string|max:255',
            'color_theme' => 'nullable|string',
            'template_id' => 'nullable|integer',
        ];
    }
}
