<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class OfflineExamCreateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'course_id' => 'required|exists:courses,id',
            'title' => 'required|string|max:255',
            'title_bn' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'mcq_mark' => 'nullable|numeric',
            'written_mark' => 'nullable|numeric',
            'assignment_mark' => 'nullable|numeric',
            'presentation_mark' => 'nullable|numeric',
            'practical_mark' => 'nullable|numeric',
            'pass_mark' => 'nullable|numeric',
            'total_mark' => 'required|numeric',
            'duration' => 'nullable|numeric',
            'exam_date' => 'required|date',
            'batch_ids' => 'nullable',
        ];
    }

    public function validatedData()
    {
        $validatedData = $this->validated();
        $validatedData['organization_id'] = auth()->user()->organization_id;
        return $validatedData;
    }
}

