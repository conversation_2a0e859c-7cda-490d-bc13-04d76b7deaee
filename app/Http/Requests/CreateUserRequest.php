<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CreateUserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email',
            'contact_no' => 'required|string|max:20',
            'address' => 'nullable|string|max:255',
            'username' => 'required|string|max:255|unique:users,username',
            'organization_id' => 'nullable|exists:organizations,id',
            'image' => 'nullable|image|max:2048',
            'password' => 'required|string|min:8',
            'user_type' => 'required|string|in:<PERSON><PERSON><PERSON><PERSON>,SuperAd<PERSON>,Organization<PERSON>d<PERSON>,<PERSON><PERSON>,Student,Other',
            'is_active' => 'required|boolean',
        ];
    }
}
