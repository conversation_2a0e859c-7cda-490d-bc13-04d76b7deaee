<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class TestimonialStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'designation' => 'nullable|string',
            'message' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'user_type' => 'required|string'
        ];
    }

    public function messages()
    {
        return [
            'organization_id.required' => 'The organization field is required.',
            'name.required' => 'The title field is required.',
            'user_type.required' => 'The user type field is required.',
            'is_active.required' => 'The is active field is required.',
            'created_by.required' => 'The created by field is required.',
        ];
    }
  
    public function validatedData()
    {
        $data = $this->validated();
        $data['organization_id'] = auth('sanctum')->user()->organization_id;
        $data['created_by'] = auth('sanctum')->user()->id;
        return $data;
    }
}

