<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UserRegistrationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string',
            'email' => 'nullable|email|unique:users,email',
            'contact_no' => 'nullable|unique:users,contact_no',
            'username' => 'required|unique:users,username',
            'address' => 'nullable|string',
            'organization_id' => 'nullable|exists:organizations,id',
            'user_type' => 'required|in:SuperAdmin,OrganizationAdmin,Mentor,Student',
            'password' => 'required|min:6',

        ];
    }
}
