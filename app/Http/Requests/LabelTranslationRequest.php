<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class LabelTranslationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'label_key' => 'required|string|max:255',
            'label_value' => 'required|string|max:255',
        ];
    }
}
