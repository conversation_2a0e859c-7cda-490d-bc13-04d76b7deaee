<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class BatchMentorAddRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'batch_id' => 'required|integer|exists:batches,id',
            'mentor_ids' => 'required|array',
            'mentor_ids.*' => 'integer|exists:mentor_informations,id',
        ];
    }
}
