<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateSubCategoryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string',
            'name_bn' => 'nullable|string',
            'description' => 'nullable|string',
            'link' => 'nullable|string',
            'icon' => 'nullable|image|mimes:png,jpg,jpeg|max:2048',
            'is_active' => 'nullable|boolean',
            'sequence' => 'nullable|integer',
        ];
    }
}
