<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class PackageStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'plan_name' => 'sometimes|required|string|max:255',
            'description' => 'sometimes|nullable|string',
            'plan_image' => 'sometimes|nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'price' => 'sometimes|required|numeric|min:0',
            'discount_percentage' => 'sometimes|nullable|numeric|min:0|max:100',
            'sale_price' => 'sometimes|nullable|numeric|min:0',
            'dollar_price' => 'sometimes|nullable|numeric|min:0',
            'dollar_discount_percentage' => 'sometimes|nullable|numeric|min:0|max:100',
            'dollar_sale_price' => 'sometimes|nullable|numeric|min:0',
            'yen_price' => 'sometimes|nullable|numeric|min:0',
            'yen_discount_percentage' => 'sometimes|nullable|numeric|min:0|max:100',
            'yen_sale_price' => 'sometimes|nullable|numeric|min:0',
            'krw_price' => 'sometimes|nullable|numeric|min:0',
            'krw_discount_percentage' => 'sometimes|nullable|numeric|min:0|max:100',
            'krw_sale_price' => 'sometimes|nullable|numeric|min:0',
            'billing_cycle' => 'sometimes|required|string|in:Monthly,Yearly',
            'features' => 'sometimes|nullable|string',
            'disabled_features' => 'sometimes|nullable|string',
            'max_users' => 'sometimes|nullable|integer|min:0',
            'max_courses' => 'sometimes|nullable|integer|min:0',
            'support_level' => 'sometimes|nullable|string',
            'trial_period_days' => 'sometimes|nullable|integer|min:0',
            'is_active' => 'sometimes|required|boolean',
            'is_popular' => 'sometimes|required|boolean',
        ];
    }

    /**
     * Prepare and return the validated data with custom parsing.
     */
    public function validatedData(): array
    {
        $data = $this->validated();

        // Convert comma-separated strings to arrays
        if (isset($data['features'])) {
            $data['features'] = array_map('trim', explode(',', $data['features']));
        }

        if (isset($data['disabled_features'])) {
            $data['disabled_features'] = array_map('trim', explode(',', $data['disabled_features']));
        }

        return $data;
    }
}
