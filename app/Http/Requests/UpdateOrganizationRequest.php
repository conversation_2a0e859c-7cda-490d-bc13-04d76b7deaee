<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateOrganizationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'sometimes|string|max:255',
            'short_name' => 'nullable|string|max:255|unique:organizations,short_name,'.$this->route('organization'),
            'details' => 'nullable|string',
            'address' => 'nullable|string|max:255',
            'email' => 'nullable|string|email|max:255|unique:organizations,email,'.$this->route('organization'),
            'user_id' => 'nullable|integer|exists:users,id',
            'contact_no' => 'nullable|string|max:20',
            'logo' => 'nullable|file|mimes:jpg,jpeg,png|max:2048',
            'banner' => 'nullable|file|mimes:jpg,jpeg,png|max:2048',
            'footer_logo' => 'nullable|file|mimes:jpg,jpeg,png|max:2048',
            'menu_position' => 'sometimes|in:top,left,right,bottom',
            'contact_person' => 'nullable|string',
            'is_active' => 'boolean',
            'contact_number' => 'nullable|string|max:20',
            'hotline_number' => 'nullable|string|max:20',
            'host_url' => 'sometimes|string|max:255|url',
            'asset_host' => 'nullable|string|max:255',
            'color_theme' => 'nullable|string',
            'template_id' => 'nullable|integer|exists:templates,id',
            'custom_student_number' => 'nullable|string',
            'custom_course_number' => 'nullable|string',
            'custom_user_number' => 'nullable|string',
            'custom_book_number' => 'nullable|string',
            'custom_instructor_number' => 'nullable|string',
            'website' => 'nullable|string|max:255|url',
            'facebook' => 'nullable|string|max:255|url',
            'twitter' => 'nullable|string|max:255|url',
            'linkedin' => 'nullable|string|max:255|url',
            'youtube' => 'nullable|string|max:255|url'
        ];
    }
}
