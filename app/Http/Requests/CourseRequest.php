<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CourseRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'title' => 'nullable|string|max:255',
            'title_bn' => 'nullable|string|max:255',
            'gp_product_id' => 'nullable|string|max:255',
            'category_id' => 'nullable|integer',
            'sub_category_id' => 'nullable|integer',
            'course_type_id' => 'nullable|integer',
            'description' => 'nullable|string',
            'youtube_url' => 'nullable|string|max:255',
            'thumbnail' => 'nullable|file|image|max:2048', // For file uploads, image, max 2MB
            'icon' => 'nullable|file|image|max:2048',
            'number_of_enrolled' => 'nullable|integer|min:0',
            'regular_price' => 'nullable|numeric|min:0',
            'sale_price' => 'nullable|numeric|min:0',
            'discount_percentage' => 'nullable|numeric|min:0|max:100',
            'rating' => 'nullable|numeric|min:0|max:5',
            'is_free' => 'nullable|boolean',
            'is_draft' => 'nullable|boolean',
            'sequence' => 'nullable|integer|min:0',
            'appeared_from' => 'nullable|date',
            'appeared_to' => 'nullable|date|after_or_equal:appeared_from',
            'routine_text' => 'nullable|string',
            'is_active' => 'nullable|boolean',
            'minimum_enroll_amount' => 'nullable',
            'max_installment_qty' => 'nullable|integer|min:0',
            'installment_type' => 'nullable|in:One Time,Installment,Monthly',
            'monthly_amount' => 'nullable|numeric|min:0',
            'course_duration' => 'nullable|numeric|min:0',
            'duration_per_day' => 'nullable|numeric|min:0',
        ];
    }
}
