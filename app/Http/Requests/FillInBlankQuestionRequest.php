<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class FillInBlankQuestionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'chapter_quiz_id' => 'required|integer',
            'question_text' => 'required|string',
            'question_image' => 'nullable|image|mimes:png,jpg,jpeg|max:2048',
            'explanation_text' => 'nullable|string',
            'explanation_image' => 'nullable|image|mimes:png,jpg,jpeg|max:2048',
            'is_active' => 'nullable|boolean',
            'blank_answers' => 'required|array',
            'blank_answers.*.blank_key' => 'required|string',
            'blank_answers.*.blank_answer' => 'required|string',
        ];
    }
}
