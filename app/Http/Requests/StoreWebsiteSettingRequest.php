<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreWebsiteSettingRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'title' => 'required|string',
            'title_bn' => 'nullable|string',
            'description' => 'nullable|string',
            'image' => 'nullable|image|mimes:png,jpg,jpeg|max:2048',
            'banner' => 'nullable|image|mimes:png,jpg,jpeg|max:2048',
            'color_theme' => 'nullable|string',
            'bg_color' => 'nullable|string',
            'type' => 'nullable|string',
            'style' => 'nullable|string',
            'is_active' => 'nullable|boolean',
        ];
    }
}
