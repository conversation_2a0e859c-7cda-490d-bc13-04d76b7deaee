<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class UpdateEbookRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'title' => 'required|string|max:255',
            'title_bn' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'image' => 'sometimes|image|mimes:jpeg,png,jpg,gif|max:2048',
            'pdf' => 'sometimes|file|mimes:pdf|max:10240',
            'price' => 'required|numeric|min:0',
            'is_active' => 'sometimes|boolean',
        ];
    }

    public function messages()
    {
        return [
            'title.required' => 'The title field is required.',
            'title.max' => 'The title may not be greater than 255 characters.',
            'title_bn.max' => 'The title (Bangla) may not be greater than 255 characters.',
            'description.string' => 'The description must be a string.',
            'image.required' => 'The image field is required.',
            'image.image' => 'The image must be an image.',
            'image.mimes' => 'The image must be a file of type: jpeg, png, jpg, gif.',
            'image.max' => 'The image may not be greater than 2048 kilobytes.',
            'pdf.required' => 'The pdf field is required.',
            'pdf.file' => 'The pdf must be a file.',
            'pdf.mimes' => 'The pdf must be a file of type: pdf.',
            'pdf.max' => 'The pdf may not be greater than 10240 kilobytes.',
            'price.required' => 'The price field is required.',
            'price.numeric' => 'The price must be a number.',
            'price.min' => 'The price must be at least 0.'
        ];
    }

    public function validatedData()
    {
        $validatedData = $this->validated();
        return $validatedData;
    }
}

