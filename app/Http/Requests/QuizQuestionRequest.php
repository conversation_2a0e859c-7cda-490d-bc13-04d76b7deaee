<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class QuizQuestionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'chapter_quiz_id' => 'required|integer',
            'class_level_id' => 'nullable|integer',
            'subject_id' => 'nullable|integer',
            'chapter_id' => 'nullable|integer',
            'question_text' => 'required|string',
            'question_text_bn' => 'nullable|string',
            'question_image' => 'nullable|image|mimes:png,jpg,jpeg|max:2048',
            'option1' => 'nullable|string',
            'option2' => 'nullable|string',
            'option3' => 'nullable|string',
            'option4' => 'nullable|string',
            'option1_image' => 'nullable|image|mimes:png,jpg,jpeg|max:2048',
            'option2_image' => 'nullable|image|mimes:png,jpg,jpeg|max:2048',
            'option3_image' => 'nullable|image|mimes:png,jpg,jpeg|max:2048',
            'option4_image' => 'nullable|image|mimes:png,jpg,jpeg|max:2048',
            'answer1' => 'nullable|boolean',
            'answer2' => 'nullable|boolean',
            'answer3' => 'nullable|boolean',
            'answer4' => 'nullable|boolean',
            'explanation_text' => 'nullable|string',
            'explanation_image' => 'nullable|image|mimes:png,jpg,jpeg|max:2048',
            'question_set_id' => 'nullable|integer',
            'chapter_quiz_subject_id' => 'nullable|integer',
            'is_active' => 'nullable|boolean',
        ];
    }
}
