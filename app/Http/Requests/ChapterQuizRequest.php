<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ChapterQuizRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'title' => 'required|string',
            'title_bn' => 'nullable|string',
            'description' => 'nullable|string',
            'course_id' => 'required|integer',
            'course_category_id' => 'nullable|integer',
            'class_level_id' => 'nullable|integer',
            'subject_id' => 'nullable|integer',
            'chapter_id' => 'nullable|integer',
            'duration' => 'required|numeric',
            'positive_mark' => 'required|numeric',
            'negative_mark' => 'required|numeric',
            'total_mark' => 'required|numeric',
            'number_of_question' => 'required|integer',
            'is_free' => 'nullable|boolean',
            'sequence' => 'nullable|integer',
            'is_active' => 'nullable|boolean',
        ];
    }
}
