<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CouponUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Adjust authorization logic as needed
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        // For update, 'code' should be unique except for the current coupon
        return [
            'code' => 'required|string|unique:coupons,code,' . $this->route('coupon')?->id,
            'discount' => 'required|numeric|min:0',
            'discount_type' => 'required|in:percentage,fixed',
            'expiry_date' => 'nullable|date',
            'max_usage' => 'nullable|numeric',
            'is_active' => 'boolean',
        ];
    }
}
