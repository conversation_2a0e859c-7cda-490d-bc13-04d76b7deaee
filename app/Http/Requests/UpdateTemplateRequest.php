<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateTemplateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'title' => 'required|string|max:255',
            'short_description' => 'nullable|string|max:255',
            'folder_name' => 'required|string|max:255',
            'theme_color' => 'nullable|string|max:255',
            'title_color' => 'nullable|string|max:255',
            'text_color' => 'nullable|string|max:255',
            'theme_image' => 'nullable|file|image|mimes:png,jpg,jpeg|max:2048',
            'is_active' => 'nullable|boolean',
        ];
    }
}
