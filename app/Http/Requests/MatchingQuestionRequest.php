<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class MatchingQuestionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'chapter_quiz_id' => 'required|integer',
            'question_text' => 'required|string',
            'explanation_text' => 'nullable|string',
            'explanation_image' => 'nullable|image|mimes:png,jpg,jpeg|max:2048',
            'is_active' => 'nullable|boolean',
            'matching_answers' => 'required|array',
            'matching_answers.*.left_item' => 'required|string',
            'matching_answers.*.right_item' => 'required|string',
        ];
    }
}
