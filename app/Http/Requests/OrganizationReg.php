<?php
namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class OrganizationReg extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'headline' => 'nullable|string|max:255',
            'sub_headline' => 'nullable|string|max:255',
            'details' => 'nullable|string',
            'address' => 'nullable|string|max:255',
            'short_name' => 'nullable|string|max:255|unique:organizations,short_name',
            'email' => 'nullable|email|max:255',
            'contact_no' => 'nullable|string|max:255',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg',
            'menu_position' => 'nullable|in:top,left,right,bottom',
            'contact_person' => 'nullable|string',
            'is_active' => 'nullable|boolean',
            'banner' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg',
            'contact_number' => 'nullable|string|max:255',
            'hotline_number' => 'nullable|string|max:255',
            'host_url' => 'nullable|string|max:255',
            'asset_host' => 'nullable|string|max:255',
            'color_theme' => 'nullable|string',
            'template_id' => 'nullable|integer',

            'user_name' => 'required|string|max:255',
            'user_email' => 'nullable|string|email|max:255|unique:users,email',
            'user_contact_no' => 'nullable|string|max:20|unique:users,contact_no',
            'user_username' => 'required|string|max:255|unique:users,username',
            'user_address' => 'nullable|string|max:255',
            'user_image' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg',
            'user_password' => 'required|string|min:8',
        ];
    }

    /**
     * Custom validation messages.
     */
    public function messages(): array
    {
        return [
            'name.required' => 'The organization name is required.',
            'short_name.unique' => 'The short name must be unique.',
            'email.email' => 'Please provide a valid email address.',
            'user_name.required' => 'Please enter your name.',
            'user_email.unique' => 'This email is already registered.',
            'user_contact_no.unique' => 'This contact number is already in use.',
            'user_username.required' => 'The username is required.',
            'user_username.unique' => 'The username is already exists.',
            'user_username.required' => 'The username is required.',
            'user_password.required' => 'The password is required.',
            'user_password.min' => 'The password must be at least 8 characters.',
            'logo.image' => 'The logo must be an image file.',
            'banner.image' => 'The banner must be an image file.',
            'menu_position.in' => 'The menu position must be one of the following: top, left, right, bottom.',
        ];
    }
}
