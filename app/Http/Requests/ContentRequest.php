<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ContentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'title' => 'required|string|max:255',
            'title_bn' => 'nullable|string|max:255',
            'category_id' => 'required|integer|exists:categories,id',
            'gp_product_id' => 'nullable|string|max:255',
            'youtube_url' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'thumbnail' => 'nullable|file|image|max:2048',
            'icon' => 'nullable|file|image|max:2048',
            'number_of_enrolled' => 'nullable|integer|min:0',
            'regular_price' => 'nullable|numeric|min:0',
            'sale_price' => 'nullable|numeric|min:0',
            'discount_percentage' => 'nullable|numeric|min:0|max:100',
            'rating' => 'nullable|numeric|min:0|max:5',
            'is_active' => 'nullable|boolean',
            'is_free' => 'nullable|boolean',
            'sequence' => 'nullable|integer|min:0',
            'appeared_from' => 'nullable|date',
            'appeared_to' => 'nullable|date|after_or_equal:appeared_from',
        ];
    }
}
