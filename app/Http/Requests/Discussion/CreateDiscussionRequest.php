<?php

namespace App\Http\Requests\Discussion;

class CreateDiscussionRequest extends BaseDiscussionRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'course_id' => 'required|exists:courses,id',
            'title' => 'required|string|max:255',
            'content' => 'required|string|max:10000',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'course_id.required' => 'The course ID is required',
            'course_id.exists' => 'The selected course does not exist',
            'title.required' => 'The discussion title is required',
            'title.max' => 'The discussion title cannot exceed 255 characters',
            'content.required' => 'The discussion content is required',
            'content.max' => 'The discussion content cannot exceed 10000 characters',
        ];
    }
}
