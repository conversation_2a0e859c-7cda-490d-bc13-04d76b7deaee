<?php

namespace App\Http\Requests\Discussion;

class ToggleLikeRequest extends BaseDiscussionRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'discussion_id' => 'required_without:comment_id|exists:discussions,id',
            'comment_id' => 'required_without:discussion_id|exists:discussion_comments,id',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'discussion_id.required_without' => 'Either discussion ID or comment ID is required',
            'discussion_id.exists' => 'The selected discussion does not exist',
            'comment_id.required_without' => 'Either discussion ID or comment ID is required',
            'comment_id.exists' => 'The selected comment does not exist',
        ];
    }
}
