<?php

namespace App\Http\Requests\Discussion;

class AddCommentRequest extends BaseDiscussionRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'discussion_id' => 'required|exists:discussions,id',
            'content' => 'required|string|max:5000',
            'parent_id' => 'nullable|exists:discussion_comments,id',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'discussion_id.required' => 'The discussion ID is required',
            'discussion_id.exists' => 'The selected discussion does not exist',
            'content.required' => 'The comment content is required',
            'content.max' => 'The comment content cannot exceed 5000 characters',
            'parent_id.exists' => 'The selected parent comment does not exist',
        ];
    }
}
