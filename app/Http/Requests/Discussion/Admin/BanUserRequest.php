<?php

namespace App\Http\Requests\Discussion\Admin;

use App\Http\Requests\Discussion\BaseDiscussionRequest;

class BanUserRequest extends BaseDiscussionRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'user_id' => 'required|exists:users,id',
            'course_id' => 'nullable|exists:courses,id',
            'reason' => 'nullable|string|max:1000',
            'duration_days' => 'nullable|integer|min:1',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'user_id.required' => 'The user ID is required',
            'user_id.exists' => 'The selected user does not exist',
            'course_id.exists' => 'The selected course does not exist',
            'reason.max' => 'The reason cannot exceed 1000 characters',
            'duration_days.integer' => 'The duration must be a valid number of days',
            'duration_days.min' => 'The duration must be at least 1 day',
        ];
    }
}
