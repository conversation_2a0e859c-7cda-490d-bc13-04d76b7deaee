<?php

namespace App\Http\Requests\Discussion\Admin;

use App\Http\Requests\Discussion\BaseDiscussionRequest;

class HandleReportRequest extends BaseDiscussionRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'status' => 'required|in:reviewed,rejected',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'status.required' => 'The status is required',
            'status.in' => 'The status must be either "reviewed" or "rejected"',
        ];
    }
}
