<?php

namespace App\Http\Requests\Discussion\Admin;

use App\Http\Requests\Discussion\BaseDiscussionRequest;

class UnbanUserRequest extends BaseDiscussionRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'user_id' => 'required|exists:users,id',
            'course_id' => 'nullable|exists:courses,id',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'user_id.required' => 'The user ID is required',
            'user_id.exists' => 'The selected user does not exist',
            'course_id.exists' => 'The selected course does not exist',
        ];
    }
}
