<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;


use Illuminate\Support\Facades\Auth;
use App\Http\Traits\HelperTrait;
class AnnouncementCreateRequest extends FormRequest
{
    use HelperTrait;
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,jpg,png,gif,svg',
            'file' => 'nullable|mimes:doc,docx,pdf,txt',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date',
            'is_active' => 'boolean',
            'user_ids' => 'nullable|array'
        ];
    }

    public function generatedData ()
    {
        $data = $this->all();
        $announcement = [];
        $announcement['organization_id'] = Auth::user()->organization_id;
        $announcement['created_by'] = Auth::user()->id;
        if ($this->hasFile('image')) {
            $announcement['image'] = $this->imageUpload($this, 'image', 'announcement');
        }
        if ($this->hasFile('file')) {
            $announcement['file'] = $this->imageUpload($this, 'file', 'announcement_file');
        }
        $announcement['title'] = $this->title;
        $announcement['description'] = $data['description'];
        $announcement['start_date'] =isset($data['start_date']) ? $data['start_date'] : null;
        $announcement['end_date'] =isset($data['end_date']) ? $data['end_date'] : null;

        $announcement['is_active'] = $data['is_active'] ?? false;
        $announcementUsers = [];
        if (isset($data['user_ids'])) {
            foreach ($data['user_ids'] as $user) {
                $announcementUsers[] = [
                    'user_id' => $user,
                    'created_at' => now(),
                    'created_at' => now()
                ];
            }
        }

        return [
            'announcement' => $announcement,
            'announcement_users' => $announcementUsers
        ];
    }


}
