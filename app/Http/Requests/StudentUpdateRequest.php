<?php

namespace App\Http\Requests;

use App\Models\StudentInformation;
use Illuminate\Foundation\Http\FormRequest;

class StudentUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $user = StudentInformation::find($this->id);

        return [
            'name' => 'required|string|max:255',
            'email' => 'nullable|email',
            'contact_no' => 'nullable',
            'address' => 'nullable|string|max:255',
            // Add validation rules for other student fields
            'education' => 'nullable|string',
            'institute' => 'nullable|string',
            'device_id' => 'nullable|string',
            'alternative_contact_no' => 'nullable|string',
            'gender' => 'nullable|string',
            'blood_group' => 'nullable|string',
            'bio' => 'nullable|string',
            'father_name' => 'nullable|string',
            'mother_name' => 'nullable|string',
            'religion' => 'nullable|string',
            'marital_status' => 'nullable|string',
            'date_of_birth' => 'nullable|date',
            'current_address' => 'nullable|string',
            'permanent_address' => 'nullable|string',
            'interests' => 'nullable|string',
            'division_id' => 'nullable|integer',
            'city_id' => 'nullable|integer',
            'area_id' => 'nullable|integer',
            'nid_no' => 'nullable|string',
            'birth_certificate_no' => 'nullable|string',
            'passport_no' => 'nullable|string',
            'status' => 'nullable|string',
            'is_foreigner' => 'nullable|boolean',
            'is_active' => 'nullable|boolean',
            'rating' => 'nullable|numeric',
        ];
    }
}
