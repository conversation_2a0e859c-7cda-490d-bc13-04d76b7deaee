<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class OrganizationPaymentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'package_id' => 'required|exists:organization_packages,id',
            'payment_method' => 'required|in:bkash,card',
            'bkash_number' => 'required_if:payment_method,bkash|nullable|regex:/(01)[0-9]{9}/',
            'card_number' => 'required_if:payment_method,card|nullable|digits:16',
            'customer_name' => 'nullable|string',
            'customer_email' => 'nullable|email',
            'customer_phone' => 'nullable|regex:/(01)[0-9]{9}/',
        ];
    }
}
