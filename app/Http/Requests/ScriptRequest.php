<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ScriptRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'title' => 'required|string',
            'title_bn' => 'nullable|string',
            'description' => 'nullable|string',
            'course_id' => 'required|integer',
            'course_category_id' => 'nullable|integer',
            'class_level_id' => 'nullable|integer',
            'subject_id' => 'nullable|integer',
            'chapter_id' => 'nullable|integer',
            'raw_url' => 'nullable|string',
            'thumbnail' => 'nullable|image|mimes:png,jpg,jpeg|max:2048',
            'price' => 'required|numeric',
            'rating' => 'nullable|numeric',
            'is_active' => 'nullable|boolean',
        ];
    }
}
