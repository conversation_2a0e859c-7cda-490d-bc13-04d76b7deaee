<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateUserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'email' => 'nullable|string|email|max:255|unique:users,email,'.$this->user,
            'contact_no' => 'required|string|max:20',
            'address' => 'nullable|string|max:255',
            'username' => 'nullable|string|max:255|unique:users,username,'.$this->user,
            'organization_id' => 'nullable|exists:organizations,id',
            'image' => 'nullable|image|max:2048',
            'password' => 'nullable|string|min:8',
            'user_type' => 'required|string|in:<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON>,Student,Other',
            'is_active' => 'required|boolean',
        ];
    }
}
