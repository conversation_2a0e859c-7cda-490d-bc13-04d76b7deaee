<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreTemplateItemRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {

        return [
            'template_id' => 'required|integer',
            'title' => 'required|string|max:255',
            'short_description' => 'nullable|string|max:255',
            'image' => 'required|file|image|mimes:png,jpg,jpeg|max:2048',
            'is_active' => 'nullable|boolean',
        ];
    }
}
