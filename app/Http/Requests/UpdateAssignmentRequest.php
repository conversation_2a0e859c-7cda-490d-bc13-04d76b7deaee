<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateAssignmentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'title' => ['required', 'string', 'max:255'],
            'title_bn' => ['nullable', 'string', 'max:255'],
            'mark' => ['nullable', 'integer', 'min:0'],
            'pass_mark' => ['nullable', 'integer', 'min:0'],
            'total_time' => ['nullable', 'integer', 'min:0'],
            'instructions' => ['nullable', 'string'],
            'description' => ['nullable', 'string'],
            'supporting_doc' => ['nullable', 'file', 'max:10240', 'mimes:pdf'],
            'publish_date' => ['nullable', 'date'],
            'deadline' => ['nullable', 'date'],
            'status' => ['nullable', 'string', 'in:Ongoing,Unpublished,OnHold,Finished'],
            'is_active' => ['nullable', 'boolean'],
        ];
    }
}
