<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class TestimonialUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'sometimes|required|string|max:255',
            'designation' => 'sometimes|nullable|string|max:255',
            'message' => 'sometimes|nullable|string',
            'image' => 'sometimes|nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'user_type' => 'sometimes|required|string',
        ];
    }

    public function messages()
    {
        return [
            'name.required' => 'The name field is required when provided.',
            'designation.string' => 'The designation must be a string.',
            'message.string' => 'The message must be a string.',
            'image.image' => 'The uploaded file must be an image.',
            'image.mimes' => 'The image must be a file of type: jpeg, png, jpg, gif, svg.',
            'image.max' => 'The image may not be greater than 2048 kilobytes.',
            'user_type.required' => 'The user type field is required when provided.',
        ];
    }

    public function validatedData()
    {
        $data = $this->validated();
        return $data;
    }
}
