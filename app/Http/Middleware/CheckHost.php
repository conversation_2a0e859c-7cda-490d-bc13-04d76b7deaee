<?php

namespace App\Http\Middleware;

use App\Models\Organization;
use Closure;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckHost
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $hostUrl = env('APP_ENV') === 'local'
        ? $request->header('Host')
        : $request->server('HTTP_ORIGIN');
        $organization = Organization::where('host_url', $hostUrl)->first();

        if ($organization) {
            // Apply global scope for the organization based on the host URL
            $this->applyOrganizationScope($organization->id);
        } else {
            // Handle the case where the organization is null
            // Option 1: Deny access if no organization is found
            abort(403, 'Organization not found or unauthorized access.');

            // Option 2: Apply a global scope with a non-existent organization_id
            // Uncomment this line if you prefer returning no data instead of aborting
            // $this->applyOrganizationScope(0); // assuming 0 is a non-existent organization_id
        }

        return $next($request);
    }

    /**
     * Apply the global scope to all models that require organization scoping.
     *
     * @param  int  $organizationId
     * @return void
     */
    protected function applyOrganizationScope($organizationId)
    {
        $modelClasses = $this->getModelClasses();

        foreach ($modelClasses as $modelClass) {
            $modelClass::addGlobalScope('organization', function (Builder $builder) use ($organizationId) {
                $builder->where('organization_id', $organizationId);
            });
        }
    }

    /**
     * Get all model classes from the app/Models directory.
     *
     * @return array
     */
    protected function getModelClasses()
    {
        $modelsPath = app_path('Models');
        $modelFiles = \File::allFiles($modelsPath);

        $modelClasses = [];
        foreach ($modelFiles as $modelFile) {
            $relativePath = $modelFile->getRelativePathname();
            $class = 'App\\Models\\'.str_replace(['/', '.php'], ['\\', ''], $relativePath);

            if (class_exists($class) && is_subclass_of($class, \Illuminate\Database\Eloquent\Model::class)) {
                $modelClasses[] = $class;
            }
        }

        return $modelClasses;
    }
}
