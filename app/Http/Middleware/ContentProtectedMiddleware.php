<?php

namespace App\Http\Middleware;

use App\Models\CourseParticipant;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class ContentProtectedMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $authId = Auth::id();
        $itemId = $request->item_id;
        $itemType = $request->item_type;

        if (! Auth::check()) {
            return response()->json(['message' => 'Unauthenticated'], 401);
        }

        if (empty($itemId) || empty($itemType)) {
            return response()->json(['message' => 'Item ID and item type is required'], 400);
        }

        $isExist = CourseParticipant::where('user_id', $authId)
            ->where('item_id', $itemId)
            ->where('item_type', $itemType)
            ->exists();

        if ($isExist) {
            return $next($request);
        }

        return response()->json(['message' => 'Content is protected'], 403);

    }
}
