<?php

namespace App\Http\Middleware;

use App\Models\Organization;
use Closure;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Schema;

class CheckOrganization
{
    /**
     * Handle an incoming request.
     *
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // Get the host URL based on the current environment.
        $hostUrl = $request->server('HTTP_ORIGIN');
        if (strpos($hostUrl, 'www.')) {
            $hostUrl = str_replace("www.", "", $hostUrl);
        }

        // $organization = Organization::where('host_url', $hostUrl)->first();

        if (!$hostUrl || $hostUrl == 'http://localhost:4001') {

            $organization = Organization::where('id', 1)->first();
        } else {
            $organization = Organization::where('host_url', $hostUrl)->first();
        }

        $organizationId = null;

        if (Auth::check()) {
            if (! in_array(Auth::user()->user_type, ['SystemAdmin', 'SuperAdmin'])) {
                $organizationId = Auth::user()->organization_id;
            }
        } elseif ($organization) {
            $organizationId = $organization->id;
        }

        if ($organizationId) {
            $this->applyOrganizationScope($organizationId);
        } else {
            // If no organization is found, prevent any data from being retrieved.
            $this->applyNoOrganizationScope();
        }

        $response = $next($request);

        if ($organizationId) {
            $this->removeOrganizationScope();
        } else {
            $this->removeNoOrganizationScope();
        }

        return $response;
    }

    /**
     * Get all model classes from the app/Models directory.
     *
     * @return array
     */
    protected function getModelClasses()
    {
        $modelsPath = app_path('Models');
        $modelFiles = File::allFiles($modelsPath);

        $modelClasses = [];
        foreach ($modelFiles as $modelFile) {
            $relativePath = $modelFile->getRelativePathname();
            $class = 'App\\Models\\'.str_replace(['/', '.php'], ['\\', ''], $relativePath);

            if (class_exists($class) && is_subclass_of($class, \Illuminate\Database\Eloquent\Model::class)) {
                $modelClasses[] = $class;
            }
        }

        return $modelClasses;
    }

    /**
     * Apply the global scope to all models dynamically.
     *
     * @param  int  $organizationId
     * @return void
     */
    protected function applyOrganizationScope($organizationId)
    {
        $modelClasses = $this->getModelClasses();

        foreach ($modelClasses as $modelClass) {
            if ($this->modelHasOrganizationIdColumn($modelClass)) {
                $modelClass::addGlobalScope('organization', function (Builder $builder) use ($organizationId) {
                    $builder->where('organization_id', $organizationId);
                });
            }
        }
    }

    /**
     * Apply a global scope that prevents any data from being retrieved if no organization is found.
     *
     * @return void
     */
    protected function applyNoOrganizationScope()
    {
        $modelClasses = $this->getModelClasses();

        foreach ($modelClasses as $modelClass) {
            if ($this->modelHasOrganizationIdColumn($modelClass)) {
                $modelClass::addGlobalScope('no_organization', function (Builder $builder) {
                    $builder->whereRaw('1 = 0'); // This ensures no data is retrieved
                });
            }
        }
    }

    /**
     * Remove the organization global scope from all models dynamically.
     *
     * @return void
     */
    protected function removeOrganizationScope()
    {
        $modelClasses = $this->getModelClasses();

        foreach ($modelClasses as $modelClass) {
            if ($this->modelHasOrganizationIdColumn($modelClass)) {
                $modelClass::withoutGlobalScope('organization');
            }
        }
    }

    /**
     * Remove the no-organization global scope from all models dynamically.
     *
     * @return void
     */
    protected function removeNoOrganizationScope()
    {
        $modelClasses = $this->getModelClasses();

        foreach ($modelClasses as $modelClass) {
            if ($this->modelHasOrganizationIdColumn($modelClass)) {
                $modelClass::withoutGlobalScope('no_organization');
            }
        }
    }

    /**
     * Check if the model has the 'organization_id' column.
     *
     * @param  string  $modelClass
     * @return bool
     */
    protected function modelHasOrganizationIdColumn($modelClass)
    {
        $instance = new $modelClass;

        return Schema::hasColumn($instance->getTable(), 'organization_id');
    }
}
