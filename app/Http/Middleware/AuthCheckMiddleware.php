<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Sanctum\PersonalAccessToken;
use Symfony\Component\HttpFoundation\Response;

class AuthCheckMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if ($token = $request->bearerToken()) {
            $accessToken = PersonalAccessToken::findToken($token);

            if ($accessToken && $accessToken->tokenable) {
                $user = $accessToken->tokenable;

                $request->merge(['user' => $user]);
            }

        } else {
            $request->merge(['user' => 'guest']);

        }
        // return response()->json([
        //     'status' => false,
        //     'message' => 'Not Logged in',
        //     'data' => [],
        // ], 401);
        return $next($request);
    }
}
