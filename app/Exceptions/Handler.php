<?php

namespace App\Exceptions;

use Illuminate\Auth\AuthenticationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Http\Response;
use Illuminate\Routing\Exceptions\InvalidSignatureException;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpKernel\Exception\UnauthorizedHttpException;
use Illuminate\Http\Exceptions\ThrottleRequestsException;
use Throwable;

class Handler extends ExceptionHandler
{
    /**
     * A list of the exception types that are not reported.
     *
     * @var array
     */
    protected $dontReport = [
        // Add exception types that should not be reported here
    ];

    /**
     * A list of the inputs that are never flashed for validation exceptions.
     *
     * @var array
     */
    protected $dontFlash = [
        'password',
        'password_confirmation',
    ];

    /**
     * Report or log an exception.
     *
     * @return void
     *
     * @throws \Exception
     */
    public function report(Throwable $exception)
    {
        parent::report($exception);
    }

    /**
     * Render an exception into an HTTP response.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function render($request, Throwable $exception)
    {
        if ($exception instanceof ModelNotFoundException) {
            return response()->json([
                'error' => null,
                'errors' => [],
                'message' => 'No query results for model ['.$exception->getModel().'] '.$exception->getIds(),
            ], Response::HTTP_NOT_FOUND);
        }

        if ($exception instanceof ValidationException) {
            return response()->json([
                'status' => false,
                'error' => 'Validation error',
                'errors' => $exception->errors(),
                'message' => 'Error: '.$exception->validator->errors()->first(),
                'data' => null,
            ], Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        if ($exception instanceof AuthenticationException) {
            return response()->json([
                'status' => false,
                'error' => 'Unauthenticated. Please log in and try again.',
                'message' => 'Unauthenticated. Please log in and try again.',
                'data' => null,
            ], Response::HTTP_UNAUTHORIZED);
        }

        if ($exception instanceof UnauthorizedHttpException) {
            return response()->json([
                'status' => false,
                'error' => 'Unauthorized access. Please check your credentials.',
                'data' => [],
            ], Response::HTTP_UNAUTHORIZED);
        }

        if ($exception instanceof InvalidSignatureException) {
            return response()->json([
                'status' => false,
                'message' => 'Invalid signature.',
                'exception' => get_class($exception),
            ], Response::HTTP_FORBIDDEN);
        }

        if ($exception instanceof ThrottleRequestsException) {
            return response()->json([
                'status' => false,
                'message' => 'Too many request',
                'data' => null
            ], 429, $exception->getHeaders());
        }

        return parent::render($request, $exception);
    }

}
