<?php

namespace App\Imports;

use App\Models\ChapterQuizQuestion;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class QuizImport implements ToModel, WithHeadingRow
{
    /**
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function __construct($chapter_quiz_id)
    {
        $this->chapter_quiz_id = $chapter_quiz_id;
    }

    public function model(array $row)
    {

        return new ChapterQuizQuestion([
            'chapter_quiz_id' => $this->chapter_quiz_id,
            'question_text' => $row['question_text'],
            'question_text_bn' => $row['question_text_bn'],
            'option1' => $row['option1'],
            'option2' => $row['option2'],
            'option3' => $row['option3'],
            'option4' => $row['option4'],
            'answer1' => $row['answer1'] ? 1 : 0,
            'answer2' => $row['answer2'] ? 1 : 0,
            'answer3' => $row['answer3'] ? 1 : 0,
            'answer4' => $row['answer4'] ? 1 : 0,
            'explanation_text' => $row['explanation_text']
        ]);
    }
}
